import AppCore
import OGApp
import OGFirebaseKit
import OGSecret
import SwiftUI
import UICatalog
import Welcome

// MARK: - OGKitApp

@main
struct OGKitApp: App {
  @UIApplicationDelegateAdaptor(AppDelegate.self) private var delegate
  @StateObject private var viewStore: OGKitAppStore

  @SwiftUI.State private var screenSize: CGSize = .zero

  private let appConfigurator: AppConfigurator

  init() {
    _viewStore = StateObject(wrappedValue: OGKitAppStore.make())

    var isDebugOrBetaBuild: Bool {
      #if DEBUG || BETA
      true
      #else
      false
      #endif
    }

    var isDebugBuild: Bool {
      #if DEBUG
      true
      #else
      false
      #endif
    }

    self.appConfigurator = AppConfigurator(
      fontResolver: OGFontResolver(),
      secrets: Secrets.ogSecrets,
      isDebugOrBetaBuild: false,
      isDebugBuild: false
    )
  }

  var body: some Scene {
    WindowGroup {
      MainView()
        .overlay(
          GeometryReader { proxy in
            Color.clear
              .preference(
                key: SizePreferenceKey.self,
                value: proxy.size
              )
          }
        )
        .onPreferenceChange(SizePreferenceKey.self) { value in
          screenSize = value
        }
        .environment(\.screenSize, screenSize)
        .c2aButtonCornerRadius(UILayoutConstants.C2AButtonStyle.cornerRadius)
        .c2aSecondaryButtonStyling(borderColor: UILayoutConstants.C2ASecondaryButtonStyle.borderColor)
        .c2aSecondaryButtonBorderWidth(UILayoutConstants.C2ASecondaryButtonStyle.borderWidth)
        .environmentObject(DeviceState())
        .dynamicTypeSize(...DynamicTypeSize.accessibility3)
    }
  }
}
