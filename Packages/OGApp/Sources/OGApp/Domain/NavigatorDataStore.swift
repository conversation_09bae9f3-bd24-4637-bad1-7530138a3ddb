import Account
import Assortment
import Combine
import Foundation
import Login
import OGBadge
import OGCore
import OGDIService
import OGDomainStore
import OGFeatureKit
import OGL10n
import OGNavigation
import OGNavigationCore
import OGWebBridge
import OGWebView
import PushPromotion
import TabBar
import Welcome

public typealias NavigatorDataStore = OGDomainStore<NavigatorDataState, NavigatorDataAction>

extension NavigatorDataStore {
  static func make() -> NavigatorDataStore {
    NavigatorDataStore(
      reducer: NavigatorDataState.Reducer.reduce,
      middlewares: NavigatorDataState.Middleware(),
      connector: NavigatorDataState.Connector()
    )
  }
}

// MARK: - NavigatorDataAction

public enum NavigatorDataAction: OGDomainAction, Equatable {
  case _setNavigatorData(NavigatorData)
  case _setShowTabBarBadge(Bool)
}

// MARK: - NavigatorData

public struct NavigatorData: Equatable, Hashable, Sendable {
  public var navigationItems: [OGNavigationItem]?
  public var hideSearchOnTabs: [String]?

  public init(
    navigationItems: [OGNavigationItem]? = nil,
    hideSearchOnTabs: [String]? = nil
  ) {
    self.navigationItems = navigationItems
    self.hideSearchOnTabs = hideSearchOnTabs
  }
}

// MARK: - NavigatorDataState

public struct NavigatorDataState: OGDomainState {
  public private(set) var isAwaitingUpdate: Bool

  private(set) var navigatorData: NavigatorData?
  private(set) var showTabBarBadge: Bool = false

  public static var initial: Self = .init()

  public init(
    isAwaitingUpdate: Bool = false,
    navigatorData: NavigatorData? = nil,
    showTabBarBadge: Bool = false
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.navigatorData = navigatorData
    self.showTabBarBadge = showTabBarBadge
  }

  mutating func update(
    navigatorData: NavigatorData? = nil,
    showTabBarBadge: Bool? = nil
  ) {
    self.navigatorData = navigatorData ?? self.navigatorData
    self.showTabBarBadge = showTabBarBadge ?? self.showTabBarBadge
  }
}

extension NavigatorDataState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _ state: inout NavigatorDataState,
      with action: NavigatorDataAction
    ) {
      switch action {
      case let ._setNavigatorData(navigatorData):
        guard state.navigatorData != navigatorData else { break }
        state.update(navigatorData: navigatorData)
      case let ._setShowTabBarBadge(showTabBarBadge):
        state.update(showTabBarBadge: showTabBarBadge)
      }
    }
  }

  public struct Middleware: OGDomainMiddleware {
    private let badgeStore: any OGDomainActionDetachedDispatchable

    init(
      badgeStore: any OGDomainActionDetachedDispatchable = OGBadgeContainer.shared.badgeStore()
    ) {
      self.badgeStore = badgeStore
    }

    public func callAsFunction(
      action: NavigatorDataAction,
      for state: NavigatorDataState
    ) async -> NavigatorDataAction? {
      switch action {
      case let ._setShowTabBarBadge(showBadge):
        badgeStore.dispatchDetached(OGBadgeAction.showTabBarBadge(showBadge))
        return nil

      case let ._setNavigatorData(navigationData) where state.showTabBarBadge:
        guard
          let accountTab = navigationData.navigationItems?.first(where: { $0.tab.rawValue == OGIdentifier.account.value })?.tab
        else { return nil }
        badgeStore.dispatchDetached(
          OGBadgeAction.setTab(
            tab: accountTab,
            badgeContent: OGTab.Constants.logInBadge
          )
        )
        return nil

      case let ._setNavigatorData(navigationData):
        guard let navigationItems = navigationData.navigationItems else { return nil }
        OGNavigationCoreContainer.shared.wishlistTab.register {
          navigationItems.map(\.tab).first(where: { $0.rawValue == "wishlist" })
        }

        OGNavigationCoreContainer.shared.basketTab.register {
          navigationItems.map(\.tab).first(where: { $0.rawValue == "basket" })
        }
        return nil
      }
    }
  }

  public actor Connector: OGDomainConnector {
    private let baseURL: OGBaseUrlFeatureAdaptable
    private let tabBar: TabBarFeatureAdaptable
    private let storage: any AnyPersistable
    private let accountFeature: AccountFeatureAdaptable
    private let assortmentFeature: AssortmentFeatureAdaptable
    private let welcomeFeature: WelcomeConfigurable
    private let loginFeature: LoginFeatureAdaptable
    private let globalWebBridge: OGWebBridgeable

    private var cancellables = Set<AnyCancellable>()

    public init(
      baseURL: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl(),
      tabBar: TabBarFeatureAdaptable = TabBarContainer.shared.tabBar(),
      storage: any AnyPersistable = OGCoreContainer.shared.storage(),
      accountFeature: AccountFeatureAdaptable = AccountFeatureAdapterContainer.shared.account(),
      assortmentFeature: AssortmentFeatureAdaptable = AssortmentFeatureAdapterContainer.shared.assortment(),
      welcomeFeature: WelcomeConfigurable = WelcomeContainer.shared.featureConfigurator(),
      loginFeature: LoginFeatureAdaptable = LoginFeatureAdapterContainer.shared.login(),
      globalWebBridge: OGWebBridgeable = OGWebBridgeContainer.shared.globalWebBridge()
    ) {
      self.baseURL = baseURL
      self.tabBar = tabBar
      self.storage = storage
      self.accountFeature = accountFeature
      self.assortmentFeature = assortmentFeature
      self.welcomeFeature = welcomeFeature
      self.loginFeature = loginFeature
      self.globalWebBridge = globalWebBridge
    }

    public func configure(dispatch: @escaping (NavigatorDataAction) async -> Void) async {
      observeLoginFeatureChanges(dispatch: dispatch)
      observeNavigatorDataSources(dispatch: dispatch)

      globalWebBridge.addActionHandler(PushPromotionWebBridgeActionHandler())
    }

    private func observeLoginFeatureChanges(dispatch: @escaping (NavigatorDataAction) async -> Void) {
      loginFeature
        .configuration
        .sink { configuration in
          Task {
            await dispatch(._setShowTabBarBadge(configuration.showTabBarBadge))
          }
        }
        .store(in: &cancellables)
    }

    private func observeNavigatorDataSources(dispatch: @escaping (NavigatorDataAction) async -> Void) {
      Publishers.CombineLatest4(
        tabBar.configuration,
        assortmentFeature.configuration,
        accountFeature.configuration,
        baseURL.web
      )
      .receive(on: DispatchQueue.main)
      .sink { [weak self] tabBarConfig, assortmentConfig, accountConfig, baseUrl in
        guard let self else { return }

        Task {
          let navigationItems = await self.prepareNavigationItems(
            tabBarConfig: tabBarConfig,
            assortmentConfig: assortmentConfig,
            accountConfig: accountConfig,
            baseUrl: baseUrl
          )

          await self.globalWebBridge.addActionHandler(
            OGBadgeWebBridgeActionHandler(tabs: navigationItems.map(\.tab))
          )

          let navigatorData = NavigatorData(
            navigationItems: navigationItems,
            hideSearchOnTabs: tabBarConfig.hideSearchOnTabs
          )

          await dispatch(._setNavigatorData(navigatorData))
        }
      }
      .store(in: &cancellables)
    }

    private func prepareNavigationItems(
      tabBarConfig: TabBarFeatureConfigurable,
      assortmentConfig: AssortmentFeatureConfigurable,
      accountConfig: AccountFeatureConfigurable,
      baseUrl: URL?
    ) async -> [OGNavigationItem] {
      let disabledTabsIdentifiers = [
        !assortmentConfig.isEnabled ? OGIdentifier.assortment.value : nil,
        !accountConfig.isEnabled ? OGIdentifier.account.value : nil
      ].compactMap { $0 }

      let activeTabs = tabBarConfig.tabs.filter { !disabledTabsIdentifiers.contains($0.identifier) }
      var navigationItems: [OGNavigationItem] = activeTabs.map { $0.toNavigationItem(with: baseUrl) }

      let welcomeEnabled = welcomeFeature.configuration.value?.isEnabled ?? false
      let welcomeCompleted = storage.bool(forKey: OGPersistenceKey.Welcome.identifier)

      if welcomeEnabled, !welcomeCompleted {
        navigationItems.insert(.welcome, at: 0)
      }

      return navigationItems
    }
  }
}
