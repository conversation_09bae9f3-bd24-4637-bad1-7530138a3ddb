import AppCore
import OGAsyncImage
import Swift<PERSON>
import UICatalog

// MARK: - GameView

struct GameView: View {
  @EnvironmentObject private var deviceState: DeviceState

  @StateObject private var viewStore: Self.Store
  @SwiftUI.State private var isOnboardingVideoPlaying: Bool = true

  init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  var body: some View {
    Group {
      if viewStore.shouldShowGameContent {
        gameContent
      }

      if viewStore.shouldShowOnboarding, let url = viewStore.onboardingUrl {
        OnboardingView(
          isPlaying: $isOnboardingVideoPlaying,
          url: url
        )
        .onAppear {
          Task {
            await viewStore.dispatch(.onboardingDidAppear)
          }
        }
      }
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .onChange(of: isOnboardingVideoPlaying) { isPlaying in
      guard !isPlaying else { return }
      Task {
        await viewStore.dispatch(.didFinishOnboarding)
      }
    }
    .compositingGroup()
  }

  private var gameContent: some View {
    ZStack {
      backgroundView
        .ignoresSafeArea(edges: .horizontal)

      VStack(spacing: UILayoutConstants.Default.padding3x) {
        Spacer()

        if let title = viewStore.title {
          Text(title)
            .styledTitle(viewStore.isBackgroundBlurred ? .dark : .light)
        }

        if let game = viewStore.game {
          GameMechanicView(game: game)
        }

        if let text = viewStore.text {
          Text(text)
            .styledBody(viewStore.isBackgroundBlurred ? .dark : .light)
        }

        Spacer()

        if let revealButtonTitle = viewStore.revealDealButtonText {
          RevealDealButton(
            title: revealButtonTitle,
            style: viewStore.isBackgroundBlurred ? .dark : .light
          ) {
            Task {
              await viewStore.dispatch(.revealDealDidTap)
            }
          }
        } else {
          Spacer()
        }
      }
      .padding(.horizontal, UILayoutConstants.Default.padding4x)

      foregroundView
    }
  }
}

// MARK: GameView + Subviews

extension GameView {
  @ViewBuilder private var backgroundView: some View {
    ZStack {
      ForEach(viewStore.backgroundAssets, id: \.self) { background in
        if background == .none {
          OGColors.primaryPrimary100.color
        } else {
          background.assetView
        }
      }
    }
  }

  @ViewBuilder private var foregroundView: some View {
    ZStack {
      ForEach(viewStore.foregroundAssets, id: \.self) { foreground in
        foreground.assetView
      }
    }
    .disabled(true)
  }

  private struct RevealDealButton: View {
    var title: String
    var style: Text.ColorScheme
    var didTap: () -> Void

    var body: some View {
      Button {
        didTap()
      } label: {
        Text(title)
          .underline()
          .styledFooter(style)
      }
      .padding(.bottom, UILayoutConstants.Default.padding3x)
    }
  }
}

// MARK: GameView.GameMechanicView

extension GameView {
  struct GameMechanicView: View {
    private let game: Game

    @Environment(\.screenSize) private var screenSize
    @EnvironmentObject private var deviceState: DeviceState

    init(game: Game) {
      self.game = game
    }

    var body: some View {
      Group {
        if let scratchGame = game as? GameScratchADeal {
          ScratchView(game: scratchGame)
        } else if let shakeGame = game as? GameShakeADeal {
          ShakeView(game: shakeGame)
        } else if let tapGame = game as? GameTapADeal {
          TapView(game: tapGame)
        }
      }
      .aspectRatio(UILayoutConstants.GameView.gameImageAspectRatio, contentMode: .fit)
      .frame(maxWidth: adjustedMaxWidth)
      .clipShape(
        RoundedRectangle(cornerRadius: UILayoutConstants.DealView.dealCornerRadius)
      )
    }

    private var adjustedMaxWidth: CGFloat {
      let screenSize = deviceState.isLandscapeOrientation ? screenSize.height : screenSize.width
      return screenSize * UILayoutConstants.GameView.gameImageAspectRatio - UILayoutConstants.Default.padding4x * 2
    }
  }
}

// MARK: - GameAsset Rendering Helpers

extension GameAsset {
  @ViewBuilder fileprivate var assetView: some View {
    switch self {
    case let .color(color):
      Color(hex: color)
    case let .image(url):
      OGAsyncImage(url: url, contentMode: .fill)
    case let .animation(url):
      OGAnimationView(animationSource: .url(url))
    case .blur, .none:
      Color.clear
    }
  }
}

// MARK: - Styling Helpers

extension Text {
  fileprivate enum ColorScheme {
    case light
    case dark
  }

  fileprivate func styledTitle(_ colorScheme: ColorScheme) -> some View {
    font(for: .headlineXXL)
      .foregroundColor(textColor(for: colorScheme))
      .multilineTextAlignment(.center)
      .lineLimit(2)
  }

  fileprivate func styledBody(_ colorScheme: ColorScheme) -> some View {
    font(for: .copyL)
      .foregroundColor(textColor(for: colorScheme))
      .multilineTextAlignment(.center)
      .lineLimit(3)
  }

  fileprivate func styledFooter(_ colorScheme: ColorScheme) -> some View {
    font(for: .footnote)
      .foregroundColor(textColor(for: colorScheme))
      .multilineTextAlignment(.center)
  }

  private func textColor(for colorScheme: ColorScheme) -> Color {
    (colorScheme == .dark ? OGColors.textOnLight : OGColors.textOnDark).color
  }
}

// MARK: - UILayoutConstants.GameView

extension UILayoutConstants {
  enum GameView {
    static let gameImageAspectRatio: CGFloat = 0.8
  }
}
