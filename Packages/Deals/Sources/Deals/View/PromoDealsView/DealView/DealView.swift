import AppCore
import OGAsyncImage
import SwiftUI
import UICatalog

// MARK: - DealView

struct DealView: View {
  @Environment(\.screenSize) private var screenSize
  @EnvironmentObject private var deviceState: DeviceState

  private let isFocused: Bool
  @StateObject private var viewStore: Self.Store

  @SwiftUI.State private var showLegalText: Bool = false

  init(deal: Deal, isFocused: Bool) {
    _viewStore = StateObject(
      wrappedValue: Self.make(deal: deal)
    )

    self.isFocused = isFocused
  }

  var body: some View {
    VStack(spacing: UILayoutConstants.Default.padding2x) {
      DealImageView(deal: viewStore.deal)

      Group {
        Group {
          if let product = viewStore.deal as? DealProduct {
            ProductInfoView(product: product)
          } else if !viewStore.legalTextCallout.isEmpty {
            LegalTextView(
              text: viewStore.legalTextCallout,
              linkTapped: $showLegalText
            )
          }
        }
        .if(deviceState.isLandscapeOrientation) {
          $0.frame(width: screenSize.height)
        }

        ActivateDealButton(title: viewStore.activateDealButtonTitle) {
          Task {
            await viewStore.dispatch(.activateDealButtonTapped)
          }
        }
      }
      .padding(.horizontal, UILayoutConstants.Default.padding5x)
      .opacity(isFocused ? 1 : 0)
      .disabled(!isFocused)
    }
    .sheet(isPresented: $showLegalText) {
      DealLegalView(deal: viewStore.deal)
    }
  }
}

// MARK: DealView.ActivateDealButton

extension DealView {
  struct ActivateDealButton: View {
    private let title: String
    private let onTapAction: () -> Void

    init(
      title: String,
      onTapAction: @escaping () -> Void
    ) {
      self.title = title
      self.onTapAction = onTapAction
    }

    var body: some View {
      C2AButton(
        title: title,
        accessibilityIdentifier: "", // TODO: Accessibility
        isFullWidth: false
      ) {
        onTapAction()
      }
      .font(for: .buttonLabelM)
    }
  }
}

// MARK: DealView.LegalTextView

extension DealView {
  struct LegalTextView: View {
    let text: String
    @Binding var linkTapped: Bool

    @SwiftUI.State private var attributedText: AttributedString = ""

    var body: some View {
      Text(attributedText)
        .environment(\.openURL, OpenURLAction { _ in
          linkTapped.toggle()
          return .discarded
        })
        .font(for: .copyL)
        .multilineTextAlignment(.center)
        .foregroundStyle(OGColors.textOnLight.color)
        .tint(OGColors.textOnLight.color)
        .padding(.horizontal, UILayoutConstants.Default.padding4x)
        .onAppear {
          text.htmlToMutableAttributedString { mutableString in
            if let mutableString {
              setStyle(for: mutableString)
              attributedText = AttributedString(mutableString)
            }
          }
        }
    }

    private func setStyle(for text: NSMutableAttributedString) {
      text.addAttribute(
        .foregroundColor,
        value: OGColors.textOnPrimary.color,
        range: NSRange(0 ..< text.length)
      )
      text.addAttribute(
        .underlineColor,
        value: OGColors.textOnPrimary.color,
        range: NSRange(0 ..< text.length)
      )
      text.addAttribute(
        .font,
        value: OGFonts.copyL,
        range: NSRange(
          0 ..< text.length
        )
      )
    }
  }
}

// MARK: - UILayoutConstants.DealView

extension UILayoutConstants {
  public enum DealView {
    static let dealCornerRadius: CGFloat = 8.0
    static let dealPlaceholderSize: CGFloat = 48.0
    static let dealImageAspectRatio: CGFloat = 0.66
  }
}
