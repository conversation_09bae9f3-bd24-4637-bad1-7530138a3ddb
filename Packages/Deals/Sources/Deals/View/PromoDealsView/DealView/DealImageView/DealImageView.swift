import AppCore
import <PERSON>GAsyncImage
import SwiftUI
import UICatalog

// MARK: - DealImageView

struct DealImageView: View {
  @StateObject private var viewStore: Self.Store

  @SwiftUI.State private var isCodeCopied: Bool = false

  @Environment(\.screenSize) private var screenSize
  @EnvironmentObject private var deviceState: DeviceState

  private var imageCorners: UIRectCorner {
    viewStore.code == nil ? [
      .topLeft,
      .topRight,
      .bottomLeft,
      .bottomRight
    ] : [
      .topLeft,
      .topRight
    ]
  }

  init(deal: Deal) {
    _viewStore = StateObject(
      wrappedValue: Self.make(deal: deal)
    )
  }

  var body: some View {
    VStack(spacing: .zero) {
      OGAsyncImage(
        url: URL(string: viewStore.imageURL),
        frame: .init(isEnabled: true, alignment: .center),
        fallbackView: { fallbackImageView }
      )
      .roundedCorners(
        imageCorners,
        radius: UILayoutConstants.DealView.dealCornerRadius
      )
      .overlay {
        if let discount = viewStore.discount {
          DiscountLabel(discount: discount)
            .padding(UILayoutConstants.Default.padding2x)
        }
      }

      if let code = viewStore.code {
        PromoCodeView(
          code: code,
          style: .default,
          isCodeCopied: $isCodeCopied
        ) {
          Task {
            await viewStore.dispatch(.copyCodeButtonTapped)
          }
        }
      }
    }
    .aspectRatio(UILayoutConstants.DealView.dealImageAspectRatio, contentMode: .fit)
    .frame(maxWidth: adjustedMaxWidth)
  }

  private var adjustedMaxWidth: CGFloat {
    let screenSize = deviceState.isLandscapeOrientation ? screenSize.height : screenSize.width
    return screenSize * UILayoutConstants.DealView.dealImageAspectRatio
  }

  private var fallbackImageView: some View {
    OGColors.backgroundBackground10.color
      .overlay {
        OGImages.icon24x24PlaceholderImg.image
      }
  }
}

// MARK: DealImageView.DiscountLabel

extension DealImageView {
  private struct DiscountLabel: View {
    var discount: String

    var body: some View {
      Text(discount)
        .font(for: .headlineXL)
        .foregroundColor(OGColors.textOnDark.color)
        .padding(UILayoutConstants.Default.padding)
        .background(
          RoundedRectangle(cornerRadius: UILayoutConstants.Default.padding)
            .fill(OGColors.accentSale.color)
        )
        .frame(
          maxWidth: .infinity,
          maxHeight: .infinity,
          alignment: .bottomLeading
        )
    }
  }
}
