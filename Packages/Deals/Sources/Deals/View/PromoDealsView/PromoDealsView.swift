import AppCore
import <PERSON><PERSON>
import UICatalog

// MARK: - PromoDealsView

struct PromoDealsView: View {
  @Environment(\.screenSize) private var screenSize
  @EnvironmentObject private var deviceState: DeviceState

  @StateObject private var viewStore: Self.Store

  @SwiftUI.State private var focusedItemIndex: Int = 0
  @SwiftUI.State private var showMotionSettingSheet: Bool = false

  init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  var body: some View {
    GeometryReader { geometry in
      ScrollView(showsIndicators: false) {
        VStack(spacing: UILayoutConstants.Default.padding2x) {
          CountdownView(
            dealsTimeLeft: viewStore.dealEndTimeLeft,
            timerTitle: viewStore.timerTitle
          )

          ZStack(alignment: .top) {
            DealsList(
              deals: viewStore.dealList,
              focusedItemIndex: $focusedItemIndex
            )
            .frame(width: screenSize.width)

            if viewStore.dealList.count > 1 {
              PageNavigator(
                totalPages: viewStore.dealList.count,
                currentPageIndex: $focusedItemIndex
              )
              .offset(y: pageNavigatorOffset)
              .if(deviceState.isLandscapeOrientation) {
                $0.frame(width: screenSize.height + UILayoutConstants.Default.padding2x)
              }
            }
          }

          Spacer()
        }
        .if(!deviceState.isLandscapeOrientation) {
          $0.frame(maxHeight: geometry.size.height)
        }
        .onChange(of: focusedItemIndex) { _ in
          Task {
            await viewStore.dispatch(.dealSwiped)
          }
        }
      }
      .disableScrollBounce()
    }
  }

  private var pageNavigatorOffset: CGFloat {
    let screenSize = deviceState.isLandscapeOrientation ? screenSize.height : screenSize.width
    return screenSize / 2 - UILayoutConstants.DefaultButton.height / 2
  }
}

// MARK: PromoDealsView.CountdownView

extension PromoDealsView {
  struct CountdownView: View {
    let dealsTimeLeft: String
    let timerTitle: String

    var body: some View {
      VStack(spacing: UILayoutConstants.Default.padding) {
        Text(timerTitle)
          .font(for: .copyL)
          .foregroundStyle(OGColors.textOnLight.color)
          .frame(height: 20)
        Text(dealsTimeLeft)
          .font(for: .headlineXXXL)
          .foregroundStyle(OGColors.textOnLight.color)
          .multilineTextAlignment(.center)
          .frame(height: 40)
      }
      .padding(.top, UILayoutConstants.Default.padding2x)
    }
  }
}

// MARK: PromoDealsView.DealsList

extension PromoDealsView {
  struct DealsList: View {
    @EnvironmentObject private var deviceState: DeviceState

    private let deals: [Deal]
    @Binding private var focusedItemIndex: Int

    @Environment(\.screenSize) private var screenSize

    init(
      deals: [Deal],
      focusedItemIndex: Binding<Int>
    ) {
      self.deals = deals
      self._focusedItemIndex = focusedItemIndex
    }

    var body: some View {
      ZStack(alignment: .top) {
        ForEach(0 ..< deals.count, id: \.self) { index in
          DealView(
            deal: deals[index],
            isFocused: index == focusedItemIndex
          )
          .offset(
            x: getOffset(for: index),
            y: 0
          )
        }
      }
      .simultaneousGesture(dragGesture)
    }

    private var dragGesture: some Gesture {
      DragGesture()
        .onEnded { value in
          let threshold: CGFloat = 50
          if value.translation.width > threshold {
            withAnimation {
              focusedItemIndex = max(0, focusedItemIndex - 1)
            }
          } else if value.translation.width < -threshold {
            withAnimation {
              focusedItemIndex = min(deals.count - 1, focusedItemIndex + 1)
            }
          }
        }
    }

    private func getOffset(for index: Int) -> CGFloat {
      let indexOffset = CGFloat(index - focusedItemIndex)
      let screenSize = deviceState.isLandscapeOrientation ? screenSize.height : screenSize.width
      let widthOffset = screenSize * UILayoutConstants.DealView.dealImageAspectRatio + UILayoutConstants.Default.padding2x
      return indexOffset * widthOffset
    }
  }
}
