import AppTracker
import OGDialogCoordinator
import OGDIService
import OGDomainStore
import OGSystemKit
import UserNotifications

public typealias PushNotificationStore = OGDomainStore<PushNotificationState, PushNotificationAction>

extension PushNotificationStore {
  public static func make() -> PushNotificationStore {
    PushNotificationStore(
      reducer: PushNotificationState.Reducer.reduce,
      middlewares: PushNotificationState.Middleware()
    )
  }
}

// MARK: - PushNotificationState

public struct PushNotificationState: OGDomainState {
  public private(set) var isAwaitingUpdate = true
  public private(set) var authorizationStatus: UNAuthorizationStatus = .notDetermined

  public init(isAwaitingUpdate: Bool = true, authorizationStatus: UNAuthorizationStatus = .notDetermined) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.authorizationStatus = authorizationStatus
  }

  public static let initial: Self = .init()

  mutating func update(
    isAwaitingUpdate: Bool? = nil,
    newStatus: UNAuthorizationStatus? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate ?? self.isAwaitingUpdate
    authorizationStatus = newStatus ?? authorizationStatus
  }
}

// MARK: - PushNotificationAction

public enum PushNotificationAction: OGDomainAction, Equatable {
  public static func == (lhs: PushNotificationAction, rhs: PushNotificationAction) -> Bool {
    switch (lhs, rhs) {
    case (.requestAuthorization, .requestAuthorization):
      return true
    case (.requestPermissionStatus, .requestPermissionStatus):
      return true
    case let (._updateAuthorizationStatus(lhsValue), ._updateAuthorizationStatus(rhsValue)):
      return lhsValue == rhsValue
    case (._trackPushNotificationUserProperty, ._trackPushNotificationUserProperty):
      return true
    default:
      return false
    }
  }

  case requestPermissionStatus
  case requestAuthorization((UNAuthorizationStatus) -> Void)

  /// Private actions
  case _updateAuthorizationStatus(UNAuthorizationStatus)
  case _trackPushNotificationUserProperty
}

// MARK: - Reducer & Midleware

extension PushNotificationState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _ state: inout PushNotificationState,
      with action: PushNotificationAction
    ) {
      switch action {
      case .requestPermissionStatus:
        state.update(isAwaitingUpdate: true)
      case .requestAuthorization:
        state.update(isAwaitingUpdate: true)
      case let ._updateAuthorizationStatus(newStatus):
        state.update(isAwaitingUpdate: false, newStatus: newStatus)
      case ._trackPushNotificationUserProperty:
        break
      }
    }
  }

  public struct Middleware: OGDomainMiddleware {
    @Injected(\OGSystemKitContainer.userNotificationCenter) private var userNotificationCenter

    private let tracker: OGAppTracking

    public init(tracker: OGAppTracking = OGAppTracker()) {
      self.tracker = tracker
    }

    public func callAsFunction(
      action: PushNotificationAction,
      for state: PushNotificationState
    ) async throws -> PushNotificationAction? {
      switch action {
      case .requestPermissionStatus:
        let status = await userNotificationCenter.authorizationStatus()

        if state.authorizationStatus != status {
          tracker.trackPushNotificationPermission()
        }

        return ._updateAuthorizationStatus(status)

      case let .requestAuthorization(callback):
        _ = await userNotificationCenter.requestAuthorization(options: [.alert, .sound, .badge, .providesAppNotificationSettings])
        let status = await userNotificationCenter.authorizationStatus()
        callback(status)

        tracker.trackPushNotificationPermission()

        return nil

      case ._updateAuthorizationStatus:
        return nil

      case ._trackPushNotificationUserProperty:
        tracker.trackPushNotificationPermission()
        return nil
      }
    }
  }
}
