import SwiftUI

/// A catalog in which all custom views are stored.
// swiftlint:disable:next convenience_type
public struct UICatalog {
/// All views in this UICatalog
	public enum Key {
	{% for type in types.based.View %}
		{% if type.accessLevel|hasPrefix:"internal" %}
			{% if type.name|!contains:"StyleConfiguration" %}
				{% for method in type.methods %}
					{% if method.name|hasPrefix:"init" %}
						{% if method.parameters.count > 0 %}
/// A {{ type.name }} View that can be styled via ``UICatalog/{{ type.name }}Style``.
		case {{ type.name|lowerFirstLetter }}({% for parameter in method.parameters %}{{parameter.asSource|replace:" @escaping","" }}{{ ', ' if not forloop.last }}{% endfor %})
						{% else %}
/// A {{ type.name }} View that can be styled via ``UICatalog/{{ type.name }}Style``.
		case {{ type.name|lowerFirstLetter }}
						{% endif %}
					{% endif %}
				{% endfor %}
			{% endif %}
		{% endif %}
	{% endfor %}
	}

/// Resolves and creates a View for a given ``Key``
/// - Parameter key : A key for a specific View.
/// - Returns: A view that is represented by a key.
	@ViewBuilder
	public static func resolve(_ key: Key) -> some View {
		switch key {
			{% for type in types.based.View %}
				{% if type.accessLevel|hasPrefix:"internal" %}
					{% if type.name|!contains:"StyleConfiguration" %}
						{% for method in type.methods %}
							{% if method.name|hasPrefix:"init" %}
								{% if method.parameters.count > 0 %}
		case .{{ type.name|lowerFirstLetter }}({% for parameter in method.parameters %}let {{parameter.name}}{{ ', ' if not forloop.last }}{% endfor %}):
			{{type.name}}({% for parameter in method.parameters %}{{parameter.name}}: {{parameter.name}}{{ ', ' if not forloop.last }}{% endfor %})
								{% else %}
		case .{{ type.name|lowerFirstLetter }}:
			{{type.name}}()
								{% endif %}
							{% endif %}
						{% endfor %}
					{% endif %}
				{% endif %}
			{% endfor %}
		}
	}
}
