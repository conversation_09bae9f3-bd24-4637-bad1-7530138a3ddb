import Foundation

// MARK: - Loadable

public enum Loadable {
  case initial
  case progress
  case success
  case failure(_ error: Error)
}

// MARK: Equatable

extension Loadable: Equatable {
  public static func == (lhs: Loadable, rhs: Loadable) -> Bool {
    switch (lhs, rhs) {
    case (.initial, .initial):
      return true
    case (.progress, .progress):
      return true
    case (.success, .success):
      return true
    case (.failure, .failure):
      return true
    default:
      return false
    }
  }
}
