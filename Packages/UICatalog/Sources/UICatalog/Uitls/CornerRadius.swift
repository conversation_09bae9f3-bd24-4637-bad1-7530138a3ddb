import Foundation
import OGDIService

// MARK: - StylesContainer

public final class StylesContainer: OGDISharedContainer {
  public static var shared: StylesContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var cornerRadius: OGDIService<CornerRadius> {
    self {
      CornerRadius()
    }.cached
  }
}

// MARK: - CornerRadius

public struct CornerRadius {
  public let extraSmall: CGFloat
  public let small: CGFloat
  public let medium: CGFloat
  public let full: CGFloat
  public init(extraSmall: CGFloat = 0, small: CGFloat = 0, medium: CGFloat = 0, full: CGFloat = 0) {
    self.extraSmall = extraSmall
    self.small = small
    self.medium = medium
    self.full = full
  }
}
