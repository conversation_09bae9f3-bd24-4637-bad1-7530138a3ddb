import SwiftUI

// MARK: - ScrollViewIntrospector

public struct ScrollViewIntrospector: UIViewRepresentable {
  let maxDepth: Int
  let maxRetries: Int
  let result: (UIScrollView) -> Void

  public init(maxDepth: Int = 10, maxRetries: Int = 5, result: @escaping (UIScrollView) -> Void) {
    self.maxDepth = maxDepth
    self.maxRetries = maxRetries
    self.result = result
  }

  public func makeUIView(context: Context) -> UIView {
    let view = IntrospectorUIView(maxDepth: maxDepth, maxRetries: maxRetries, result: result)
    return view
  }

  public func updateUIView(_ uiView: UIView, context: Context) {}
}

// MARK: - IntrospectorUIView

private class IntrospectorUIView: UIView {
  let maxDepth: Int
  let maxRetries: Int
  let result: (UIScrollView) -> Void

  private var retries = 0
  private let retryInterval: TimeInterval = 0.05

  init(maxDepth: Int, maxRetries: Int, result: @escaping (UIScrollView) -> Void) {
    self.maxDepth = maxDepth
    self.maxRetries = maxRetries
    self.result = result
    super.init(frame: .zero)
    backgroundColor = .clear
    isUserInteractionEnabled = false
  }

  @available(*, unavailable)
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func didMoveToWindow() {
    super.didMoveToWindow()
    attemptToFindScrollView()
  }

  private func attemptToFindScrollView() {
    guard retries <= maxRetries else { return }

    DispatchQueue.main.asyncAfter(deadline: .now() + retryInterval) {
      if let scrollView = self.findScrollView(from: self, depth: 0) {
        self.result(scrollView)
      } else {
        self.retries += 1
        self.attemptToFindScrollView()
      }
    }
  }

  private func findScrollView(from view: UIView?, depth: Int) -> UIScrollView? {
    guard let view, depth <= maxDepth else { return nil }

    if let scrollView = view as? UIScrollView {
      return scrollView
    }

    for subview in view.subviews {
      if let found = findScrollView(from: subview, depth: depth + 1) {
        return found
      }
    }

    return findScrollView(from: view.superview, depth: depth + 1)
  }
}
