import Foundation

public enum RatingViewSize {
  case small
  case medium
  case large

  var font: OGFonts {
    switch self {
    case .small: return .copyS
    case .medium: return .copyMRegular
    case .large: return .copyL
    }
  }

  var spacing: CGFloat {
    switch self {
    case .small: return UILayoutConstants.RatingView.smallSpacing
    case .medium: return UILayoutConstants.RatingView.mediumSpacing
    case .large: return UILayoutConstants.RatingView.largeSpacing
    }
  }
}
