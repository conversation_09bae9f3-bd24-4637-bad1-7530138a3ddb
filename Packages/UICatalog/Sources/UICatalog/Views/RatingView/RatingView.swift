import SwiftUI

// MARK: - RatingView

public struct RatingView: View {
  public let rating: Float
  public let count: Int
  public let rounding: Rounding
  public let onRatingTap: (() -> Void)?
  public let size: RatingViewSize
  public let reviewsText: String

  public enum Rounding {
    case half
    case none
    case whole
  }

  private init(
    rating: Float,
    count: Int = 0,
    rounding: Rounding,
    size: RatingViewSize = .small,
    reviewsText: String = "",
    onRatingTap: (() -> Void)? = nil
  ) {
    self.rating = rating
    self.count = count
    self.rounding = rounding
    self.size = size
    self.reviewsText = reviewsText
    self.onRatingTap = onRatingTap
  }

  public init(
    rating: Rating,
    size: RatingViewSize = .medium,
    reviewsText: String = "",
    rounding: Rounding = .half,
    onRatingTap: (
      () -> Void
    )? = nil
  ) {
    self.init(
      rating: rating.averageRating,
      count: rating.numberOfRatings,
      rounding: rounding,
      size: size,
      reviewsText: reviewsText,
      onRatingTap: onRatingTap
    )
  }

  public init(
    simpleRating: Int32,
    size: RatingViewSize = .medium,
    reviewsText: String = "",
    rounding: Rounding = .half
  ) {
    self.init(
      rating: Float(simpleRating),
      rounding: rounding,
      size: size,
      reviewsText: reviewsText
    )
  }

  public var body: some View {
    if onRatingTap != nil {
      detailedRatingView
    } else {
      simpleRatingView
    }
  }

  private var detailedRatingView: some View {
    AdaptiveHStack(spacing: size.spacing) {
      starsContent
      countView
    }
    .accessibilityElement(children: .combine)
    .accessibilityLabel("Die durchschnittliche Produktbewertung ist \(String(format: "%.1f", locale: Locale.current, rating)) von 5 Sternen abgestimmt von \(count)")
  }

  private var simpleRatingView: some View {
    HStack(spacing: size.spacing) {
      starsContent
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(Int(rating)) Sterne")
    }
  }

  private var starsContent: some View {
    HStack(spacing: .zero) {
      let calculateStars = calculateStars(averageRating: rating, rounding: rounding)

      ForEach(0 ..< calculateStars.fullStars, id: \.self) { _ in
        fullStar
      }
      ForEach(0 ..< calculateStars.halfStars, id: \.self) { _ in
        halfStar
      }
      ForEach(0 ..< calculateStars.emptyStars, id: \.self) { _ in
        emptyStar
      }
    }
  }

  @ViewBuilder var fullStar: some View {
    switch size {
    case .small:
      OGImages.icon16x16RatingOn.image
        .resizable()
        .frame(width: 12, height: 12)
    case .medium:
      OGImages.icon16x16RatingOn.image
    case .large:
      OGImages.icon24x24RatingOn.image
        .resizable()
        .frame(width: 24, height: 24)
    }
  }

  @ViewBuilder var halfStar: some View {
    switch size {
    case .small:
      OGImages.icon16x16RatingHalf.image
        .resizable()
        .frame(width: 12, height: 12)
    case .medium:
      OGImages.icon16x16RatingHalf.image
    case .large:
      OGImages.icon24x24RatingHalf.image
        .resizable()
        .frame(width: 24, height: 24)
    }
  }

  @ViewBuilder var emptyStar: some View {
    switch size {
    case .small:
      OGImages.icon16x16RatingOff.image
        .resizable()
        .frame(width: 12, height: 12)
    case .medium:
      OGImages.icon16x16RatingOff.image
    case .large:
      OGImages.icon16x16RatingOff.image
        .resizable()
        .frame(width: 24, height: 24)
    }
  }

  private var countView: some View {
    Button {
      onRatingTap?()
    } label: {
      Text("\(count) \(reviewsText)")
        .underline()
        .baselineOffset(UILayoutConstants.Default.underlineOffset)
        .font(for: .copyS)
        .foregroundColor(OGColors.primaryPrimary100.color)
        .multilineTextAlignment(.leading)
        .accessibilityHidden(true)
    }
  }
}

extension RatingView {
  func calculateStars(averageRating: Float, rounding: Rounding) -> (fullStars: Int, halfStars: Int, emptyStars: Int) {
    var fullStars = 0
    var halfStars = 0
    var emptyStars = 0

    switch rounding {
    case .half, .none:
      let adjustedRating = round(averageRating * 2) / 2.0
      fullStars = Int(floor(adjustedRating))
      halfStars = (adjustedRating - Float(fullStars) == 0.5) ? 1 : 0
      emptyStars = 5 - fullStars - halfStars

    case .whole:
      fullStars = Int(round(averageRating))
      halfStars = 0
      emptyStars = 5 - fullStars
    }

    return (fullStars, halfStars, emptyStars)
  }
}

// MARK: - UILayoutConstants.RatingView

extension UILayoutConstants {
  enum RatingView {
    static let smallSpacing: CGFloat = 2
    static let mediumSpacing: CGFloat = 4
    static let largeSpacing: CGFloat = 8
  }
}
