import SwiftUI

// MARK: - C2ASecondaryButtonBorderWidthKey

private struct C2ASecondaryButtonBorderWidthKey: EnvironmentKey {
  static let defaultValue: CGFloat = 0
}

extension EnvironmentValues {
  public var c2aSecondaryButtonBorderWidth: CGFloat {
    get { self[C2ASecondaryButtonBorderWidthKey.self] }
    set { self[C2ASecondaryButtonBorderWidthKey.self] = newValue }
  }
}

extension View {
  public func c2aSecondaryButtonBorderWidth(_ value: CGFloat) -> some View {
    environment(\.c2aSecondaryButtonBorderWidth, value)
  }
}
