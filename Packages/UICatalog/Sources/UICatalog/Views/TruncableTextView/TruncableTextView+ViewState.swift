import Foundation
import OGViewStore

extension TruncableTextView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(
    text: String,
    maxLines: Int?,
    isExpanded: Bool
  )
    -> Store {
    Store(
      initialState: ViewState(maxLines: maxLines, text: text, isExpanded: isExpanded),
      reducer: Reducer.reduce
    )
  }

  struct ViewState: OGViewState {
    private(set) var isTruncated: Bool
    private(set) var text: String
    private(set) var maxLines: Int?
    private(set) var isExpanded: Bool
    private(set) var initialMaxLines: Int?

    static let initial = ViewState()

    init(maxLines: Int? = nil, text: String = "", isExpanded: Bool = false) {
      self.maxLines = isExpanded ? nil : maxLines
      self.initialMaxLines = maxLines
      self.text = text
      self.isExpanded = isExpanded
      self.isTruncated = false
    }

    mutating func update(isExpanded: Bool) {
      self.isExpanded = isExpanded
      maxLines = isExpanded ? nil : initialMaxLines
    }

    mutating func update(isTruncated: Bool) {
      self.isTruncated = isTruncated
    }
  }

  enum Event: OGViewEvent {
    case updatedIsExpanded(Bool)
    case updatedIsTruncated(Bool)
  }

  enum Reducer {
    static func reduce(state: inout ViewState, event: Event) {
      switch event {
      case let .updatedIsExpanded(isExpanded):
        state.update(isExpanded: isExpanded)
      case let .updatedIsTruncated(isTruncated):
        state.update(isTruncated: isTruncated)
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async
      -> Event? {
      switch event {
      case .updatedIsExpanded, .updatedIsTruncated:
        return nil
      }
    }
  }
}
