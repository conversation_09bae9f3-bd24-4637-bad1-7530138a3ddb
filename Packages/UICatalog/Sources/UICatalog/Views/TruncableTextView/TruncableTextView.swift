import SwiftUI

// MARK: - TruncableTextView

public struct TruncableTextView: View {
  private let font: OGFonts
  private let showMoreTitle: String
  private let showLessTitle: String
  private let trailingTitle: String?
  private let contentSpacing: CGFloat
  private let isTopAligned: Bool
  private let isExpanded: Bool

  private let showMoreAction: (() -> Void)?
  @StateObject private var viewStore: Self.Store

  public init(
    text: String,
    font: OGFonts = .copyMRegular,
    showMoreTitle: String,
    showLessTitle: String = "",
    trailingTitle: String? = nil,
    contentSpacing: CGFloat,
    maxLines: Int?,
    isExpanded: Bool = false,
    isTopAligned: Bool = true,
    showMoreAction: (() -> Void)? = nil
  ) {
    _viewStore = StateObject(
      wrappedValue: Self.makeStore(
        text: text,
        maxLines: maxLines,
        isExpanded: isExpanded
      )
    )
    self.font = font
    self.showMoreTitle = showMoreTitle
    self.showLessTitle = showLessTitle
    self.trailingTitle = trailingTitle
    self.contentSpacing = contentSpacing
    self.isTopAligned = isTopAligned
    self.showMoreAction = showMoreAction
    self.isExpanded = isExpanded
  }

  public var body: some View {
    VStack(alignment: .leading, spacing: contentSpacing) {
      Text(viewStore.text)
        .font(for: font)
        .foregroundColor(OGColors.textOnLight.color)
        .lineLimit(viewStore.maxLines)
        .multilineTextAlignment(.leading)
        .truncationMode(.tail)
        .background {
          isTruncated
        }

      if viewStore.isTruncated, !viewStore.isExpanded {
        showMoreView
      } else if viewStore.isExpanded, viewStore.isTruncated {
        showLessView
      } else if isTopAligned, trailingTitle == nil {
        Spacer()
      } else if !isTopAligned, trailingTitle != nil {
        HStack {
          Spacer()
          trailingTitleView
        }
      } else if isTopAligned, trailingTitle != nil {
        Spacer()
        HStack {
          Spacer()
          trailingTitleView
        }
      }
    }
    .frame(maxWidth: .infinity, alignment: .leading)
    .onChange(of: isExpanded) { newValue in
      Task {
        await viewStore.dispatch(.updatedIsExpanded(newValue))
      }
    }
  }

  @ViewBuilder var showMoreView: some View {
    Spacer()
    HStack {
      Button {
        if let showMoreAction {
          showMoreAction()
        } else {
          Task {
            await viewStore.dispatch(.updatedIsExpanded(true))
          }
        }
      } label: {
        Text(showMoreTitle)
          .underline()
          .baselineOffset(UILayoutConstants.Default.underlineOffset)
          .font(for: .buttonLabelS)
          .foregroundColor(OGColors.primaryPrimary100.color)
      }
      Spacer()
      trailingTitleView
    }
  }

  @ViewBuilder var showLessView: some View {
    Spacer()
    HStack {
      Button {
        Task {
          await viewStore.dispatch(.updatedIsExpanded(false))
        }
      } label: {
        Text(showLessTitle)
          .underline()
          .baselineOffset(UILayoutConstants.Default.underlineOffset)
          .font(for: .buttonLabelS)
          .foregroundColor(OGColors.primaryPrimary100.color)
      }
      Spacer()
      trailingTitleView
    }
  }

  var trailingTitleView: some View {
    Text(trailingTitle ?? "")
      .font(for: .copyS)
      .foregroundColor(OGColors.backgroundBackground60.color)
  }

  var isTruncated: some View {
    ZStack {
      Text(viewStore.text)
        .font(for: font)
        .fixedSize(horizontal: false, vertical: true)
        .background {
          GeometryReader { geometry in
            Color.clear
              .preference(
                key: TextHeightsKey.self,
                value: TextHeights(natural: geometry.size.height, constrained: 0)
              )
          }
        }
      Text(viewStore.text)
        .font(for: font)
        .lineLimit(viewStore.initialMaxLines)
        .background {
          GeometryReader { geometry in
            Color.clear
              .preference(
                key: TextHeightsKey.self,
                value: TextHeights(natural: 0, constrained: geometry.size.height)
              )
          }
        }
    }
    .hidden()
    .allowsHitTesting(false)
    .onPreferenceChange(TextHeightsKey.self) { heights in
      Task {
        await viewStore.dispatch(.updatedIsTruncated(heights.isTruncated))
      }
    }
  }
}

// MARK: - TextHeights

struct TextHeights: Equatable {
  let natural: CGFloat
  let constrained: CGFloat

  var isTruncated: Bool {
    constrained != natural
  }
}

// MARK: - TextHeightsKey

struct TextHeightsKey: PreferenceKey {
  static var defaultValue = TextHeights(natural: 0, constrained: 0)
  static func reduce(value: inout TextHeights, nextValue: () -> TextHeights) {
    let next = nextValue()
    value = TextHeights(
      natural: max(value.natural, next.natural),
      constrained: max(value.constrained, next.constrained)
    )
  }
}
