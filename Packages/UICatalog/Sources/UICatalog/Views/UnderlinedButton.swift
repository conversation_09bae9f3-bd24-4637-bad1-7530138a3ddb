import SwiftUI

// MARK: - UnderlinedButton

public struct UnderlinedButton: View {
  let title: String
  let action: () -> Void
  let font: OGFonts
  let color: OGColorAsset

  public init(title: String, action: @escaping () -> Void, font: OGFonts, color: OGColorAsset) {
    self.title = title
    self.action = action
    self.font = font
    self.color = color
  }

  public var body: some View {
    Button(action: action) {
      Text(title)
        .font(for: font)
        .foregroundColor(color.color)
        .modifier(UnderlineText(color: color.color))
    }
  }
}

// MARK: - UnderlineText

struct UnderlineText: ViewModifier {
  let color: Color

  func body(content: Content) -> some View {
    if #available(iOS 16.0, *) {
      content.underline(color: color)
    } else {
      content
        .overlay(
          Rectangle()
            .frame(height: 1)
            .foregroundColor(color)
            .offset(y: 8)
        )
    }
  }
}
