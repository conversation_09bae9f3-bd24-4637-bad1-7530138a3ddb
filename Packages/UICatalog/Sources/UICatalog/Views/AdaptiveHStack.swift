import SwiftUI

public struct AdaptiveHStack<Content>: View where Content: View {
  @Environment(\.dynamicTypeSize) var dynamicTypeSize
  let content: Content
  let spacing: CGFloat?
  let alignment: VerticalAlignment
  public init(
    alignment: VerticalAlignment = .center,
    spacing: CGFloat? = nil,
    @ViewBuilder content: @escaping () -> Content
  ) {
    self.content = content()
    self.spacing = spacing
    self.alignment = alignment
  }

  public var body: some View {
    if dynamicTypeSize.isAccessibilitySize {
      VStack(alignment: .leading, spacing: spacing) { content }
    } else {
      HStack(alignment: alignment, spacing: spacing) { content }
    }
  }
}
