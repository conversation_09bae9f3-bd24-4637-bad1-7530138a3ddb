import SwiftUI

/// A custom view that allows navigation between pages using previous and next buttons.
/// It is designed to show navigation buttons for paginated content.
public struct PageNavigator: View {
  /// The total number of pages available for navigation.
  public var totalPages: Int

  /// The current page index, which is a `Binding` to allow the parent view to update the page.
  @Binding public var currentPageIndex: Int

  public init(totalPages: Int, currentPageIndex: Binding<Int>) {
    self.totalPages = totalPages
    _currentPageIndex = currentPageIndex
  }

  public var body: some View {
    HStack {
      pageNavigationButton(isPreviousButton: true)
        .if(currentPageIndex == 0) { $0.hidden() }

      Spacer()

      pageNavigationButton(isPreviousButton: false)
        .if(currentPageIndex == totalPages - 1) { $0.hidden() }
    }
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
  }

  @ViewBuilder
  private func pageNavigationButton(isPreviousButton: Bool) -> some View {
    Button {
      withAnimation {
        currentPageIndex = isPreviousButton
          ? max(0, currentPageIndex - 1)
          : min(totalPages - 1, currentPageIndex + 1)
      }
    } label: {
      Circle()
        .fill(OGColors.backgroundButtonSecondaryDefault.color)
        .shadow(color: OGColors.primaryPrimary100.color.opacity(0.02), radius: 4.5, y: 1.5)
        .shadow(color: OGColors.primaryPrimary100.color.opacity(0.08), radius: 2.3, y: 1.0)
        .overlay {
          isPreviousButton
            ? OGImages.icon24x24ChevronLeftPrimary.image
            : OGImages.icon24x24ChevronRightPrimary.image
        }
        .frame(height: UILayoutConstants.DefaultButton.height)
    }
  }
}
