import OGAppEnvironment
import OGL10n
import SwiftUI

// MARK: - BottomSheetView

public struct BottomSheetView<Content: View>: View {
  @Environment(\.screenSize) var screenSize
  private let title: String
  private let content: Content

  @SwiftUI.State private var contentHeight: CGFloat = 0

  public init(title: String, @ViewBuilder content: () -> Content) {
    self.title = title
    self.content = content()
  }

  public var body: some View {
    if #available(iOS 16.0, *) {
      sheetContent
        .presentationDragIndicator(.visible)
        .presentationDetents(contentHeight.presentationDetents(for: screenSize))
    } else {
      sheetContent
    }
  }

  @ViewBuilder private var sheetContent: some View {
    VStack(spacing: UILayoutConstants.Default.padding) {
      BottomSheetHeader(title: title)

      content
    }
    .padding(.horizontal, UILayoutConstants.Default.padding3x)
    .background(
      SizeReader { size in
        guard size.height > contentHeight else { return }
        contentHeight = size.height
      }
    )
  }

  // MARK: - BottomSheetHeader

  private struct BottomSheetHeader: View {
    @Environment(\.dismiss) var dismiss

    let title: String

    var body: some View {
      HStack {
        Text(title)
          .font(for: .titleM)
          .foregroundStyle(OGColors.textOnLight.color)
          .offset(y: UILayoutConstants.Default.padding)

        Spacer()

        ProductDetailCloseButton(accessibilityLabel: ogL10n.General.Close, dropShadow: false) {
          dismiss()
        }
      }
      .frame(height: UILayoutConstants.BottomSheetView.headerHeight)
    }
  }
}

// MARK: - UILayoutConstants.BottomSheetView

extension UILayoutConstants {
  public enum BottomSheetView {
    public static let detentFractionHeight: CGFloat = 0.95
    public static let headerHeight: CGFloat = 52.0
    static let closeButtonSize: CGFloat = 32.0
    static let closeImageSize: CGFloat = 16.0
  }
}

// MARK: - CGFloat + PresentationDetents

@available(iOS 16.0, *)
extension CGFloat {
  fileprivate func presentationDetents(for screenSize: CGSize) -> Set<PresentationDetent> {
    let maxHeight = screenSize.height * UILayoutConstants.BottomSheetView.detentFractionHeight
    return self <= maxHeight ? [.height(self)] : [.height(maxHeight)]
  }
}
