import SwiftUI

// MARK: - DisableScrollBounce

public struct DisableScrollBounce: ViewModifier {
  let maxDepth: Int
  let maxRetries: Int

  public func body(content: Content) -> some View {
    content
      .background(
        ScrollViewIntrospector(maxDepth: maxDepth, maxRetries: maxRetries) { scrollView in
          scrollView.bounces = false
        }
      )
  }
}

extension View {
  public func disableScrollBounce(maxDepth: Int = 10, maxRetries: Int = 5) -> some View {
    modifier(DisableScrollBounce(maxDepth: maxDepth, maxRetries: maxRetries))
  }
}
