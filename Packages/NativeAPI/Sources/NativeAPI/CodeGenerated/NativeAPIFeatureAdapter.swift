// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros


public protocol NativeAPIFeatureConfigurable {
	var isEnabled: Bool { get set }
	
	var apiTenant: String { get set } 
	var graphQLApiUrl: String { get set }
  var restApiUrl: String { get set }
	var productIdRegex: String { get set }
  var dynamicYieldTrackPageViewUrl: String { get set }
}

struct NativeAPIFeatureConfig: NativeAPIFeatureConfigurable {
  var isEnabled: Bool = false
  var apiTenant: String = ""
  var graphQLApiUrl: String = ""
  var restApiUrl: String = ""
  var productIdRegex: String = ""
  var dynamicYieldTrackPageViewUrl: String = ""
}

public final class NativeAPIFeatureAdapter: OGFeatureAdapter, NativeAPIFeatureAdaptable {
	override public class var featureName: OGFeature.Name { OGIdentifier.nativeAPI.value }
	
	public let configuration: CurrentValueSubject<NativeAPIFeatureConfigurable, Never>
	private var subscriptions = Set<AnyCancellable>()

	public init(configuration: NativeAPIFeatureConfigurable?) {
    self.configuration = CurrentValueSubject(configuration ?? NativeAPIFeatureConfig())
    super.init()
    	
    receiveUpdates()
  }

	private func receiveUpdates() {
		$feature.sink { [weak self] feature in
			guard let self = self else { return }
			var updatedConfiguration = self.configuration.value
			guard let feature = feature else {
				updatedConfiguration.isEnabled = false
				self.configuration.send(updatedConfiguration)
				return
			}
			updatedConfiguration.isEnabled = feature.isEnabled
			
			let apiTenant: String = (feature.customValue(for: OGFeatureKey.CustomValues.NativeAPI.apiTenant)) ?? self.configuration.value.apiTenant
			updatedConfiguration.apiTenant = apiTenant
			let graphQLApiUrl: String = (feature.customValue(for: OGFeatureKey.CustomValues.NativeAPI.graphQLApiUrl)) ?? self.configuration.value.graphQLApiUrl
			updatedConfiguration.graphQLApiUrl = graphQLApiUrl
			let productIdRegex: String = (feature.customValue(for: OGFeatureKey.CustomValues.NativeAPI.productIdRegex)) ?? self.configuration.value.productIdRegex
			updatedConfiguration.productIdRegex = productIdRegex
      let dynamicYieldTrackPageViewUrl: String = (feature.customValue(for: OGFeatureKey.CustomValues.NativeAPI.dynamicYieldTrackPageViewUrl)) ?? self.configuration.value.dynamicYieldTrackPageViewUrl
      updatedConfiguration.dynamicYieldTrackPageViewUrl = dynamicYieldTrackPageViewUrl
      let restApiUrl: String = (feature.customValue(for: OGFeatureKey.CustomValues.NativeAPI.restApiUrl)) ?? self.configuration.value.restApiUrl
      updatedConfiguration.restApiUrl = restApiUrl   
			self.configuration.send(updatedConfiguration)
			}.store(in: &subscriptions)
	}
}

public protocol NativeAPIFeatureAdaptable: OGFeatureAdaptable {
	var configuration: CurrentValueSubject<NativeAPIFeatureConfigurable, Never> { get }
}


extension OGFeatureKey.CustomValues {
	public enum NativeAPI: String, OGKeyReceivable {
			public var value: String  {
				rawValue
			}
			
			case apiTenant = "apiTenant"
			case graphQLApiUrl = "graphQLApiUrl"
      case restApiUrl = "restApiUrl"
			case productIdRegex = "productIdRegex"
      case dynamicYieldTrackPageViewUrl = "dynamicYieldTrackPageViewUrl"
		}
}


extension OGIdentifier {
	public static let nativeAPI = #identifier("nativeAPI")
}
