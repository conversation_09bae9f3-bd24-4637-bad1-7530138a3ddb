import Combine
import Foundation
import <PERSON>GAppKitSDK
import OGCore
import OGDomainStore
import OGFeatureAdapter
import <PERSON><PERSON>outer
import OGSecret

public typealias NativeAPIStore = OGDomainStore<NativeAPIState, NativeAPIAction>
public typealias OGNativeApiTenant = __Bridge__OGNativeConfig_NativeApiTenant

extension OGDomainStoreFactory {
  static func make() -> NativeAPIStore {
    NativeAPIStore(
      reducer: NativeAPIState.Reducer.reduce,
      middlewares: NativeAPIState.Middleware(),
      connector: NativeAPIState.Connector()
    )
  }
}

// MARK: - NativeAPIState

public struct NativeAPIState: OGDomainState {
  public private(set) var isAwaitingUpdate: Bool
  public private(set) var isConfigured: Bool
  init(
    isAwaitingUpdate: Bool = false,
    isConfigured: Bool = false
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.isConfigured = isConfigured
  }

  public static let initial: Self = .init()

  mutating func update(
    isAwaitingUpdate: Bool? = nil,
    isConfigured: Bool? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate ?? self.isAwaitingUpdate
    self.isConfigured = isConfigured ?? self.isConfigured
  }
}

// MARK: - NativeAPIAction

public enum NativeAPIAction: OGDomainAction {
  /// private
  case _isConfigured(Bool)
  case _receiveConfig(
    apiTenant: String,
    graphQLApiUrl: String,
    restApiUrl: String,
    productIdRegex: String,
    user: String,
    password: String,
    dynamicYieldApiKey: String?,
    dynamicYieldTrackPageViewUrl: String?
  )
}

extension NativeAPIState {
  enum Reducer {
    static func reduce(
      _ state: inout NativeAPIState,
      with action: NativeAPIAction
    ) {
      switch action {
      case ._receiveConfig:
        state.update(isAwaitingUpdate: false)
      case let ._isConfigured(configured):
        state.update(isConfigured: configured)
      }
    }
  }

  struct Middleware: OGDomainMiddleware {
    let nativeAPI: OGNative
    let baseUrl: OGBaseUrlFeatureAdaptable
    let nativeAPICookiesBridge: CookiesBridge

    init(
      nativeAPI: OGNative = NativeAPIContainer.shared.nativeAPI(),
      baseUrl: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl(),
      nativeAPICookiesBridge: CookiesBridge = NativeAPIContainer.shared.nativeAPICookiesBridge()
    ) {
      self.nativeAPI = nativeAPI
      self.baseUrl = baseUrl
      self.nativeAPICookiesBridge = nativeAPICookiesBridge
    }

    func callAsFunction(
      action: NativeAPIAction,
      for state: NativeAPIState
    ) async throws
      -> NativeAPIAction? {
      switch action {
      case let ._receiveConfig(
        apiTenant,
        graphQLApiUrl,
        restApiUrl,
        productIdRegex,
        user,
        password,
        dynamicYieldApiKey,
        dynamicYieldTrackPageViewUrl
      ):
        guard let nativeApiTenant = OGNativeApiTenant.allCases.first(where: {
          $0.name.lowercased() == apiTenant.lowercased()
        }) else {
          return ._isConfigured(false)
        }

        var dynamicYield: OGNativeConfig.DynamicYield? {
          guard let dynamicYieldApiKey, let dynamicYieldTrackPageViewUrl else {
            return nil
          }
          return OGNativeConfig.DynamicYield(
            trackPageViewUrl: dynamicYieldTrackPageViewUrl,
            apiKey: dynamicYieldApiKey,
            cookiesUrl: baseUrl.web.value?.absoluteString ?? ""
          )
        }

        let graphQLBackend = OGNativeConfig.Backend(
          url: graphQLApiUrl,
          basicAuthUser: user,
          basicAuthPassword: password
        )

        let restBackend = OGNativeConfig.Backend(
          url: restApiUrl,
          basicAuthUser: user,
          basicAuthPassword: password
        )

        let nativeConfig = OGNativeConfig(
          graphQLBackend: graphQLBackend,
          restBackend: restBackend,
          tenant: nativeApiTenant,
          productIdRegex: productIdRegex,
          dynamicYield: dynamicYield,
          cookiesBridge: nativeAPICookiesBridge,
          debug: false
        )

        nativeAPI.configure(config: nativeConfig)
        return ._isConfigured(true)
      case ._isConfigured:
        return nil
      }
    }
  }

  actor Connector: OGDomainConnector {
    private let feature: NativeAPIFeatureAdaptable
    private let secretService: OGSecretsProvidable

    private var cancellables = Set<AnyCancellable>()

    init(
      feature: NativeAPIFeatureAdaptable = NativeAPIFeatureAdapterContainer.shared.nativeAPI(),
      secretService: OGSecretsProvidable = OGSecretServiceContainer.shared.service()
    ) {
      self.feature = feature
      self.secretService = secretService
    }

    public func configure(
      dispatch: @escaping (NativeAPIAction) async -> Void
    ) async {
      let apiTenantPublisher = feature
        .configuration
        .map(\.apiTenant)
        .compactMap {
          $0.isEmpty ? nil : $0
        }
        .removeDuplicates()

      let graphQLApiUrlPublisher = feature
        .configuration
        .map(\.graphQLApiUrl)
        .compactMap {
          $0.isEmpty ? nil : $0
        }
        .removeDuplicates()

      let restApiUrlPublisher = feature
        .configuration
        .map(\.restApiUrl)
        .compactMap {
          $0.isEmpty ? nil : $0
        }
        .removeDuplicates()

      let productIdRegexPublisher = feature
        .configuration
        .map(\.productIdRegex)
        .compactMap {
          $0.isEmpty ? nil : $0
        }
        .removeDuplicates()

      let dynamicYieldTrackPageViewUrlPublisher = feature
        .configuration
        .map(\.dynamicYieldTrackPageViewUrl)
        .compactMap {
          $0.isEmpty ? nil : $0
        }
        .removeDuplicates()

      let secretServicePublisher = secretService.secretSetDidChangePublisher
        .compactMap {
          if let apiSecret = $0.apiSecret,
             let apiUser = $0.apiUser {
            return (apiSecret: apiSecret, apiUser: apiUser, dynamicYieldApiKey: $0.dynamicYieldApiKey)
          } else {
            return nil
          }
        }

      let featuresAPIPublishers = Publishers.CombineLatest3(
        graphQLApiUrlPublisher,
        restApiUrlPublisher,
        apiTenantPublisher
      )

      let featuresPublishers = Publishers.CombineLatest(
        productIdRegexPublisher,
        dynamicYieldTrackPageViewUrlPublisher
      )

      Publishers
        .CombineLatest3(featuresAPIPublishers, featuresPublishers, secretServicePublisher)
        .map { featuresAPI, features, secrets in
          (featuresAPI.0, featuresAPI.1, featuresAPI.2, features.0, features.1, secrets)
        }
        .sink { graphQLApiUrl, restApiUrl, apiTenant, productIdRegex, dynamicYieldTrackPageViewUrl, secret in
          Task {
            await dispatch(
              ._receiveConfig(
                apiTenant: apiTenant,
                graphQLApiUrl: graphQLApiUrl,
                restApiUrl: restApiUrl,
                productIdRegex: productIdRegex,
                user: secret.apiUser,
                password: secret.apiSecret,
                dynamicYieldApiKey: secret.dynamicYieldApiKey,
                dynamicYieldTrackPageViewUrl: dynamicYieldTrackPageViewUrl
              )
            )
          }
        }
        .store(in: &cancellables)
    }
  }
}

// MARK: - OGIdentifier.SecretIdentifiers

extension OGIdentifier {
  enum SecretIdentifiers {
    enum NativeAPI {
      static let apiSecret = #identifier("graphQLApiSecret")
      static let apiUser = #identifier("graphQLApiUser")
    }

    enum DynamicYield {
      static let apiKey = #identifier("dynamicYieldApiKey")
    }
  }
}

extension Set<OGSecret> {
  var apiSecret: String? {
    first(where: { $0.identifier == OGIdentifier.SecretIdentifiers.NativeAPI.apiSecret })?.secret
  }

  var apiUser: String? {
    first(where: { $0.identifier == OGIdentifier.SecretIdentifiers.NativeAPI.apiUser })?.secret
  }

  var dynamicYieldApiKey: String? {
    first(where: { $0.identifier == OGIdentifier.SecretIdentifiers.DynamicYield.apiKey })?.secret
  }
}
