import Foundation

extension HTTPCookie {
  static func cookie(from cookieString: String, with url: String) -> HTTPCookie? {
    var properties: [HTTPCookiePropertyKey: Any] = [:]
    properties[.domain] = URL(string: url)?.host
    properties[.path] = "/"
    for component in cookieString.components(separatedBy: ";") {
      let keyValue = component.components(separatedBy: "=").map { $0.trimmingCharacters(in: .whitespaces) }
      if keyValue.count == 2,
         let firstValue = keyValue.first,
         let secondValue = keyValue.last {
        switch firstValue.lowercased() {
        case "domain": properties[.domain] = secondValue
        case "path": properties[.path] = secondValue
        case "expires": properties[.expires] = secondValue
        case "max-age": properties[.maximumAge] = secondValue
        case "version": properties[.version] = secondValue
        case "port": properties[.port] = secondValue
        case "samesite": properties[.sameSitePolicy] = secondValue
        default:
          properties[.name] = firstValue
          properties[.value] = secondValue
        }
      } else if keyValue.count == 1 {
        switch component.lowercased().trimmingCharacters(in: .whitespaces) {
        case "secure": properties[.secure] = true
        case "httponly": properties[.init(rawValue: "HttpOnly")] = true
        default:
          break
        }
      }
    }
    let cookie = HTTPCookie(properties: properties)
    return cookie
  }
}
