import OGAppKitSDK
import WebKit

final class NativeAPICookiesBridge: CookiesBridge {
  private let cookieStore = WKWebsiteDataStore.default().httpCookieStore

  func __getCookies(url: String) async throws -> [String: String] {
    await cookieStore
      .allCookies()
      .compactMap {
        $0.domain == URL(string: url)?.host ? $0 : nil
      }
      .reduce(
        into: [String: String]()
      ) {
        $0[$1.name] = $1.value
      }
  }

  @MainActor
  func __setCookie(url: String, cookie: String) async throws {
    guard let cookie = HTTPCookie.cookie(from: cookie, with: url) else { return }
    await cookieStore.setCookie(cookie)
  }
}
