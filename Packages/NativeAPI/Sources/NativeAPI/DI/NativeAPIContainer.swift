import OGAppKitSDK
import OGDIService
import OGDomainStore

public final class NativeAPIContainer: OGDISharedContainer {
  public static var shared: NativeAPIContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var nativeAPI: OGDIService<OGNative> {
    self {
      OGAppKitSdk.shared.native()
    }.cached
  }

  public var store: OGDIService<NativeAPIStore> {
    self {
      OGDomainStoreFactory.make()
    }.cached
  }

  var nativeAPICookiesBridge: OGDIService<CookiesBridge> {
    self {
      NativeAPICookiesBridge()
    }.cached
  }
}
