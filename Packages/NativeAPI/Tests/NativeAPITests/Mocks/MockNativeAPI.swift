import Combine
import OGAppKitSDK
import OGMacros
import OGMock
import <PERSON>GS<PERSON>ret
@testable import NativeAPI

// MARK: - MockNativeAPI

@OGMock
final class MockNativeAPI: OGNative {
  func getProductDetailScreen(id: String, secondaryId: String?, componentConfigsJson: String) -> OGAppKitSDK.SkieSwiftFlow<OGResult<ProductDetailScreen>> {
    mock.getProductDetailScreen(id: id, secondaryId: secondaryId, componentConfigsJson: componentConfigsJson)
  }

  func updateProductDetailScreen(screenId: String, productId: String, context: (any ProductDetailScreenRequestContext)?) {
    mock.updateProductDetailScreen(screenId: screenId, productId: productId, context: context)
  }

  func getProductDetailScreen(id: String, secondaryId: String?, componentConfigs: ComponentConfigs<__Skie__TypeDef__1__id_OGAKSDKProductDetailComponentConfig_>) -> OGAppKitSDK.SkieSwiftFlow<OGResult<ProductDetailScreen>> {
    mock.getProductDetailScreen(id: id, secondaryId: secondaryId, componentConfigs: componentConfigs)
  }

  func getProductReviewsScreen(id: String, componentConfigs: ComponentConfigs<__Skie__TypeDef__2__id_OGAKSDKProductReviewsComponentConfig_>) -> OGAppKitSDK.SkieSwiftFlow<OGResult<ProductReviewsScreen>> {
    mock.getProductReviewsScreen(id: id, componentConfigs: componentConfigs)
  }

  func getAddToBasketSuccessScreen(id: String, componentConfigs: ComponentConfigs<__Skie__TypeDef__0__id_OGAKSDKAddToBasketSuccessComponentConfig_>) -> OGAppKitSDK.SkieSwiftFlow<OGResult<AddToBasketSuccessScreen>> {
    mock.getAddToBasketSuccessScreen(id: id, componentConfigs: componentConfigs)
  }

  func getAddToBasketSuccessScreen(id: String, componentConfigsJson: String) -> OGAppKitSDK.SkieSwiftFlow<OGResult<AddToBasketSuccessScreen>> {
    mock.getAddToBasketSuccessScreen(id: id, componentConfigsJson: componentConfigsJson)
  }

  func __getBasket() async throws -> OGResult<Basket> {
    try await mock.__getBasket()
  }

  func __getWishlist() async throws -> OGResult<Wishlist> {
    try await mock.__getWishlist()
  }

  func __addProductToBasket(id: String) async throws -> OGResult<Basket> {
    try await mock.__addProductToBasket(id: id)
  }

  func __addProductToWishlist(id: String) async throws -> OGResult<Wishlist> {
    try await mock.__addProductToWishlist(id: id)
  }

  func configure(config: OGNativeConfig) {
    mock.configure(config: config)
  }

  func getProduct(id: String) -> OGAppKitSDK.SkieSwiftFlow<OGResult<Product>> {
    mock.getProduct(id: id)
  }

  func getProductDetailScreenByUrl(url: String, componentConfigsJson: String) -> OGAppKitSDK.SkieSwiftFlow<OGResult<ProductDetailScreen>> {
    mock.getProductDetailScreenByUrl(url: url, componentConfigsJson: componentConfigsJson)
  }

  func __removeProductFromWishlist(id: String) async throws -> OGResult<Wishlist> {
    try await mock.__removeProductFromWishlist(id: id)
  }

  func filterProductReviews(screenId: String, filterRating: KotlinInt?) {
    mock.filterProductReviews(screenId: screenId, filterRating: filterRating)
  }

  func getProductReviewsScreen(id: String, componentConfigsJson: String) -> OGAppKitSDK.SkieSwiftFlow<OGResult<ProductReviewsScreen>> {
    mock.getProductReviewsScreen(id: id, componentConfigsJson: componentConfigsJson)
  }

  func showMoreReviews(screenId: String) {
    mock.showMoreReviews(screenId: screenId)
  }

  func sortProductReviews(screenId: String, sortingOption: OGAppKitSDK.__Bridge__ReviewsSortingOptions_SortingOption) {
    mock.sortProductReviews(screenId: screenId, sortingOption: sortingOption)
  }

  func __addVoucherToBasket(id: String, customName: String?) async throws -> OGResult<Basket> {
    try await mock.__addVoucherToBasket(id: id, customName: customName)
  }
}

// MARK: - MockNativeAPIFeatureAdaptable

@OGMock
final class MockNativeAPIFeatureAdaptable: NativeAPIFeatureAdaptable {
  var isAdaptedFeatureEnabled: Bool {
    get {
      mock.isAdaptedFeatureEnabled.getter.record()
    }
    set {
      mock.isAdaptedFeatureEnabled.setter.record(newValue)
    }
  }

  var configuration: CurrentValueSubject<NativeAPIFeatureConfigurable, Never> {
    get {
      mock.configuration.getter.record()
    }
    set {
      mock.configuration.setter.record(newValue)
    }
  }
}

// MARK: - MockOGSecretsProvidable

@OGMock
final class MockOGSecretsProvidable: OGSecretsProvidable {
  var secretSetDidChangePublisher: AnyPublisher<Set<OGSecret>, Never> {
    get {
      mock.secretSetDidChangePublisher.getter.record()
    }
    set {
      mock.secretSetDidChangePublisher.setter.record(newValue)
    }
  }

  var secrets: Set<OGSecret> {
    get {
      mock.secrets.getter.record()
    }
    set {
      mock.secrets.setter.record(newValue)
    }
  }
}

extension NativeAPIFeatureConfig {
  static var stub = NativeAPIFeatureConfig(
    isEnabled: true,
    apiTenant: .stub,
    graphQLApiUrl: .stub,
    restApiUrl: .stub,
    productIdRegex: .stub,
    dynamicYieldTrackPageViewUrl: .stub
  )
}
