import XCTest
@testable import NativeAPI

final class NativeAPIStoreReducerTests: XCTestCase {
  /// Test: GIVEN an initial state, WHEN _receiveConfig is dispatched, THEN isAwaitingUpdate is set to false
  func test_GIVEN_initialState_WHEN_receiveConfig_THEN_isAwaitingUpdateSetToFalse() {
    // GIVEN
    var state = NativeAPIState(isAwaitingUpdate: true)

    // WHEN
    NativeAPIState.Reducer
      .reduce(
        &state,
        with: ._receiveConfig(
          apiTenant: .stub,
          graphQLApiUrl: .stub,
          restApiUrl: .stub,
          productIdRegex: .stub,
          user: .stub,
          password: .stub,
          dynamicYieldApiKey: .stub,
          dynamicYieldTrackPageViewUrl: .stub
        )
      )

    // THEN
    XCTAssertFalse(state.isAwaitingUpdate)
  }

  /// Test: GIVEN an initial state, WHEN _isConfigured(true) is dispatched, THEN isConfigured is set to true
  func test_GIVEN_initialState_WHEN_isConfiguredTrue_THEN_isConfiguredSetToTrue() {
    // GIVEN
    var state = NativeAPIState(isConfigured: false)

    // WHEN
    NativeAPIState.Reducer.reduce(&state, with: ._isConfigured(true))

    // THEN
    XCTAssertTrue(state.isConfigured)
  }

  /// Test: GIVEN an initial state, WHEN _isConfigured(false) is dispatched, THEN isConfigured is set to false
  func test_GIVEN_initialState_WHEN_isConfiguredFalse_THEN_isConfiguredSetToFalse() {
    // GIVEN
    var state = NativeAPIState(isConfigured: true)

    // WHEN
    NativeAPIState.Reducer.reduce(&state, with: ._isConfigured(false))

    // THEN
    XCTAssertFalse(state.isConfigured)
  }
}
