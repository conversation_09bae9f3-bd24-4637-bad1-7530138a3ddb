import OGAppKitSDK
import OGCoreTestsUtils
import XCTest

@testable import NativeAPI

final class NativeAPIStoreMiddlewareTests: XCTestCase {
  /// Test: GIVEN a valid API tenant and configuration details, WHEN _receiveConfig is dispatched, THEN nativeAPI.configure is called and _isConfigured(true) is returned
  func test_GIVEN_validApiTenant_WHEN_receiveConfig_THEN_configureCalledAndIsConfiguredTrue() async throws {
    // GIVEN
    let action = NativeAPIAction._receiveConfig(
      apiTenant: "lascana",
      graphQLApiUrl: .stub,
      restApiUrl: .stub,
      productIdRegex: .stub,
      user: .stub,
      password: .stub,
      dynamicYieldApiKey: nil,
      dynamicYieldTrackPageViewUrl: nil
    )

    let mockNativeAPI = MockNativeAPI()
    let mockCookiesBridge = MockCookiesBridge()
    let sut = NativeAPIState.Middleware(nativeAPI: mockNativeAPI, nativeAPICookiesBridge: mockCookiesBridge)

    // WHEN
    let resultAction = try await sut(action: action, for: .initial)
    let nativeConfig = OGNativeConfig(
      graphQLBackend: OGNativeConfig.Backend(url: .stub, basicAuthUser: .stub, basicAuthPassword: .stub),
      restBackend: OGNativeConfig.Backend(url: .stub, basicAuthUser: .stub, basicAuthPassword: .stub),
      tenant: .lascana,
      productIdRegex: .stub,
      dynamicYield: nil,
      cookiesBridge: mockCookiesBridge,
      debug: false
    )
    // THEN
    XCTAssertEqual(mockNativeAPI.mock.configureCalls.callsCount, 1)
    XCTAssertEqual(mockNativeAPI.mock.configureCalls.latestCall, nativeConfig)
    XCTAssertEqual(resultAction, ._isConfigured(true))
  }

  /// Test: GIVEN an invalid API tenant, WHEN _receiveConfig is dispatched, THEN nativeAPI.configure is not called and _isConfigured(false) is returned
  func test_GIVEN_invalidApiTenant_WHEN_receiveConfig_THEN_configureNotCalledAndIsConfiguredFalse() async throws {
    // GIVEN
    let action = NativeAPIAction._receiveConfig(
      apiTenant: .stub,
      graphQLApiUrl: .stub,
      restApiUrl: .stub,
      productIdRegex: .stub,
      user: .stub,
      password: .stub,
      dynamicYieldApiKey: .stub,
      dynamicYieldTrackPageViewUrl: .stub
    )

    let mockNativeAPI = MockNativeAPI()
    let mockCookiesBridge = MockCookiesBridge()
    let sut = NativeAPIState.Middleware(nativeAPI: mockNativeAPI, nativeAPICookiesBridge: mockCookiesBridge)

    // WHEN
    let resultAction = try await sut(action: action, for: .initial)

    // THEN
    XCTAssertEqual(mockNativeAPI.mock.configureCalls.callsCount, 0)
    XCTAssertEqual(mockNativeAPI.mock.configureCalls.latestCall, nil)
    XCTAssertEqual(resultAction, ._isConfigured(false))
  }

  /// Test: GIVEN an action of _isConfigured, WHEN the middleware processes it, THEN no further action is returned
  func test_GIVEN_isConfiguredAction_WHEN_processed_THEN_noFurtherActionReturned() async throws {
    // GIVEN
    let action = NativeAPIAction._isConfigured(true)

    let mockNativeAPI = MockNativeAPI()
    let mockCookiesBridge = MockCookiesBridge()
    let sut = NativeAPIState.Middleware(nativeAPI: mockNativeAPI, nativeAPICookiesBridge: mockCookiesBridge)

    // WHEN
    let resultAction = try await sut(action: action, for: .initial)

    // THEN
    XCTAssertNil(resultAction)
  }
}
