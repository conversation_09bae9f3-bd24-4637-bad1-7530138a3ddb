import Combine
import <PERSON>GCoreTestsUtils
import OGIdentifier
import OGSecret
import XCTest

@testable import NativeAPI

final class NativeAPIStoreConnectorRaceConditionTests: XCTestCase {
  
  /// Test: GIVEN multiple concurrent configuration updates, WHEN Connector processes them, THEN no race condition occurs
  func test_GIVEN_concurrentConfigUpdates_WHEN_connectorProcesses_THEN_noRaceCondition() async {
    await withMainSerialExecutor {
      // GIVEN
      let expectation = XCTestExpectation(description: "All dispatches should complete without crashing")
      expectation.expectedFulfillmentCount = 10 // Expect multiple rapid updates
      
      let mockFeature = MockNativeAPIFeatureAdaptable()
      let mockSecretService = MockOGSecretsProvidable()
      
      // Create a configuration subject that will emit rapid updates
      let configSubject = CurrentValueSubject<NativeAPIFeatureConfigurable, Never>(NativeAPIFeatureConfig.stub)
      mockFeature.mock.configuration.getter.mockCall { _ in configSubject }
      
      // Create a secret subject that will emit rapid updates
      let secretSubject = CurrentValueSubject<Set<OGSecret>, Never>([
        OGSecret(identifier: OGIdentifier.SecretIdentifiers.NativeAPI.apiSecret, secret: "test-secret"),
        OGSecret(identifier: OGIdentifier.SecretIdentifiers.NativeAPI.apiUser, secret: "test-user")
      ])
      mockSecretService.mock.secretSetDidChangePublisher.getter.mockCall { _ in secretSubject.eraseToAnyPublisher() }
      
      let connector = NativeAPIState.Connector(feature: mockFeature, secretService: mockSecretService)
      
      var dispatchCount = 0
      let dispatchQueue = DispatchQueue(label: "test-dispatch", attributes: .concurrent)
      
      // WHEN - Configure the connector with a dispatch function that tracks calls
      await connector.configure { action in
        dispatchQueue.async {
          dispatchCount += 1
          if dispatchCount <= 10 {
            expectation.fulfill()
          }
        }
      }
      
      // Simulate rapid concurrent updates to both configuration and secrets
      Task {
        for i in 0..<5 {
          var config = NativeAPIFeatureConfig.stub
          config.apiTenant = "tenant-\(i)"
          config.graphQLApiUrl = "https://api-\(i).example.com/graphql"
          configSubject.send(config)
          
          try? await Task.sleep(nanoseconds: 10_000_000) // 10ms
        }
      }
      
      Task {
        for i in 0..<5 {
          let secrets: Set<OGSecret> = [
            OGSecret(identifier: OGIdentifier.SecretIdentifiers.NativeAPI.apiSecret, secret: "secret-\(i)"),
            OGSecret(identifier: OGIdentifier.SecretIdentifiers.NativeAPI.apiUser, secret: "user-\(i)")
          ]
          secretSubject.send(secrets)
          
          try? await Task.sleep(nanoseconds: 15_000_000) // 15ms
        }
      }
      
      // THEN - Wait for all dispatches to complete without crashing
      await fulfillment(of: [expectation], timeout: 5.0)
      
      // Verify that we received multiple dispatch calls
      XCTAssertGreaterThanOrEqual(dispatchCount, 5, "Should have received multiple dispatch calls")
    }
  }
  
  /// Test: GIVEN connector configured multiple times, WHEN previous subscriptions exist, THEN old subscriptions are cleaned up
  func test_GIVEN_multipleConfigureCalls_WHEN_connectorReconfigured_THEN_oldSubscriptionsCleanedUp() async {
    await withMainSerialExecutor {
      // GIVEN
      let mockFeature = MockNativeAPIFeatureAdaptable()
      let mockSecretService = MockOGSecretsProvidable()
      
      let configSubject = CurrentValueSubject<NativeAPIFeatureConfigurable, Never>(NativeAPIFeatureConfig.stub)
      mockFeature.mock.configuration.getter.mockCall { _ in configSubject }
      
      let secretSubject = CurrentValueSubject<Set<OGSecret>, Never>([])
      mockSecretService.mock.secretSetDidChangePublisher.getter.mockCall { _ in secretSubject.eraseToAnyPublisher() }
      
      let connector = NativeAPIState.Connector(feature: mockFeature, secretService: mockSecretService)
      
      var firstDispatchCount = 0
      var secondDispatchCount = 0
      
      // WHEN - Configure the connector first time
      await connector.configure { action in
        firstDispatchCount += 1
      }
      
      // Send an update
      configSubject.send(NativeAPIFeatureConfig.stub)
      
      // Wait a bit for the first subscription to process
      try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
      
      // Configure the connector second time (should clean up old subscriptions)
      await connector.configure { action in
        secondDispatchCount += 1
      }
      
      // Send another update
      var newConfig = NativeAPIFeatureConfig.stub
      newConfig.apiTenant = "new-tenant"
      configSubject.send(newConfig)
      
      // Wait for processing
      try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
      
      // THEN - Only the second dispatch function should be called for the final update
      XCTAssertGreaterThan(firstDispatchCount, 0, "First dispatch should have been called initially")
      XCTAssertGreaterThan(secondDispatchCount, 0, "Second dispatch should have been called after reconfiguration")
    }
  }
  
  /// Test: GIVEN empty or nil configuration values, WHEN connector processes them, THEN no crash occurs
  func test_GIVEN_emptyConfigValues_WHEN_connectorProcesses_THEN_noCrash() async {
    await withMainSerialExecutor {
      // GIVEN
      let expectation = XCTestExpectation(description: "Dispatch should be called with empty values")
      
      let mockFeature = MockNativeAPIFeatureAdaptable()
      let mockSecretService = MockOGSecretsProvidable()
      
      // Create configuration with empty values
      var emptyConfig = NativeAPIFeatureConfig()
      emptyConfig.isEnabled = true
      emptyConfig.apiTenant = ""
      emptyConfig.graphQLApiUrl = ""
      emptyConfig.restApiUrl = ""
      emptyConfig.productIdRegex = ""
      emptyConfig.dynamicYieldTrackPageViewUrl = ""
      
      let configSubject = CurrentValueSubject<NativeAPIFeatureConfigurable, Never>(emptyConfig)
      mockFeature.mock.configuration.getter.mockCall { _ in configSubject }
      
      let secretSubject = CurrentValueSubject<Set<OGSecret>, Never>([])
      mockSecretService.mock.secretSetDidChangePublisher.getter.mockCall { _ in secretSubject.eraseToAnyPublisher() }
      
      let connector = NativeAPIState.Connector(feature: mockFeature, secretService: mockSecretService)
      
      // WHEN
      await connector.configure { action in
        expectation.fulfill()
      }
      
      // Send the empty configuration
      configSubject.send(emptyConfig)
      
      // THEN - Should not crash and should call dispatch
      await fulfillment(of: [expectation], timeout: 2.0)
    }
  }
}
