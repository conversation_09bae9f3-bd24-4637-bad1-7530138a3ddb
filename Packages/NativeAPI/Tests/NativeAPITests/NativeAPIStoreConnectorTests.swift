import Combine
import <PERSON>GCoreTestsUtils
import OGIdentifier
import OGSecret
import XCTest

@testable import NativeAPI

final class NativeAPIStoreConnectorTests: XCTestCase {
  /// Test: GIVEN valid configuration values from feature and secret service, WHEN Connector is configured, THEN _receiveConfig is dispatched with correct values
  func test_GIVEN_validConfigAndSecrets_WHEN_configure_THEN_receiveConfigDispatched() async {
    await withMainSerialExecutor {
      // GIVEN
      let expectation = XCTestExpectation(description: "Dispatch should be called")

      let mockFeature = MockNativeAPIFeatureAdaptable()
      mockFeature.mock.configuration.getter.mockCall { _ in
        CurrentValueSubject(NativeAPIFeatureConfig.stub)
      }

      let mockSecretService = MockOGSecretsProvidable()
      mockSecretService.mock.secretSetDidChangePublisher.getter.mockCall { _ in
        Just(Set([
          OGSecret(identifier: OGIdentifier.SecretIdentifiers.NativeAPI.apiSecret, secret: .stub),
          OGS<PERSON>ret(identifier: OGIdentifier.SecretIdentifiers.NativeAPI.apiUser, secret: .stub),
          OGSecret(identifier: OGIdentifier.SecretIdentifiers.DynamicYield.apiKey, secret: .stub)
        ]))
        .eraseToAnyPublisher()
      }

      let sut = NativeAPIState.Connector(feature: mockFeature, secretService: mockSecretService)
      // WHEN
      var dispatchedEvents: [NativeAPIAction] = []
      let dispatch: (NativeAPIAction) async -> Void = { event in

        if case ._receiveConfig = event {
          dispatchedEvents.append(event)
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      // THEN
      await fulfillment(of: [expectation], timeout: 0.1)
      if case ._receiveConfig = dispatchedEvents.first {
      } else {
        XCTFail("Expected _receiveConfig action to be dispatched")
      }
      XCTAssertTrue(dispatchedEvents.contains(where: {
        if case let ._receiveConfig(
          apiTenant,
          graphQLApiUrl,
          restApiUrl,
          productIdRegex,
          user,
          password,
          dynamicYieldApiKey,
          dynamicYieldTrackPageViewUrl
        ) = $0 {
          return apiTenant == .stub &&
            graphQLApiUrl == .stub &&
            restApiUrl == .stub &&
            productIdRegex == .stub &&
            user == .stub &&
            password == .stub &&
            dynamicYieldApiKey == .stub &&
            dynamicYieldTrackPageViewUrl == .stub
        }
        return false
      }))
    }
  }
}
