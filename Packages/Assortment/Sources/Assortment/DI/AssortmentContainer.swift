import Foundation
import OGDIService
import OGDomainStore

// MARK: - Assortment

final class AssortmentContainer: OGDISharedContainer {
  private(set) static var shared: AssortmentContainer = .init()
  private(set) var manager: OGDIContainerManager = .init()

  var requestBuilder: OGDIService<AssortmentRequestCreatable> {
    self {
      AssortmentRequestBuilder(headerFields: [:])
    }
  }

  var assortmentStore: OGDIService<AssortmentStore> {
    self {
      OGDomainStoreFactory.make()
    }.shared
  }

  var assortmentService: OGDIService<AssortmentServicing> {
    self {
      AssortmentService()
    }
  }

  var staticEntriesProvider: OGDIService<AssortmentStaticEntriesProviding> {
    self {
      AssortmentStaticEntriesProvider()
    }.cached
  }
}
