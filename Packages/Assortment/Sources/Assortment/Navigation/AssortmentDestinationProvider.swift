import OGDIService
import OGIdentifier
import OGL10n
import OGMacros
import OGNavigation
import OGTenantKit
import Search
import SwiftUI

// MARK: - AssortmentDestinationProvider

public struct AssortmentDestinationProvider: OGDestinationProvidable {
  @OGInjected(\OGTenantContainer.selectionService) private var tenantSelection
  @OGInjected(\OGRoutingContainer.routePublisher) private var router

  public private(set) var identifier: OGIdentifier = .assortment

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    AssortmentRootView()
  }
}

// MARK: - AssortmentDetailDestinationProvider

public struct AssortmentDetailDestinationProvider: OGDestinationProvidable {
  @OGInjected(\OGRoutingContainer.routePublisher) private var router
  public private(set) var identifier: OGIdentifier = .assortmentDetail

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    if let navigationEntry: AssortmentState.NavigationEntry = route.getData() {
      AssortmentDetailView(navigationEntry: navigationEntry)
    }
  }
}

extension OGIdentifier {
  static let assortmentDetail = #identifier("assortment/detail")
  static let staticEntries = #identifier("staticEntries")
}
