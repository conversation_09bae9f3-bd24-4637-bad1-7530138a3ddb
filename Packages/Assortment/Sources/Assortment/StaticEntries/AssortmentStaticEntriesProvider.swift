import Combine
import Foundation
import OGDIService

// MARK: - AssortmentStaticEntriesProviding

public protocol AssortmentStaticEntriesProviding {
  var appendableStaticNavigationEntries: Published<[AssortmentNavigationEntry]>.Publisher { get }
  var insertableNavigationEntries: Published<[AssortmentNavigationEntry]>.Publisher { get }
}

// MARK: - AssortmentStaticEntriesProvider

public final class AssortmentStaticEntriesProvider: AssortmentStaticEntriesProviding {
  private var getAppendableStaticNavigationEntries: [AssortmentNavigationEntry] {
    _appendableStaticNavigationEntries
  }

  private var getInsertableNavigationEntries: [AssortmentNavigationEntry] {
    _insertableNavigationEntries
  }

  @Published private(set) var _appendableStaticNavigationEntries: [AssortmentNavigationEntry] = []
  public var appendableStaticNavigationEntries: Published<[AssortmentNavigationEntry]>.Publisher {
    $_appendableStaticNavigationEntries
  }

  @Published private(set) var _insertableNavigationEntries: [AssortmentNavigationEntry] = []
  public var insertableNavigationEntries: Published<[AssortmentNavigationEntry]>.Publisher {
    $_insertableNavigationEntries
  }

  @OGInjected(\AssortmentFeatureAdapterContainer.assortment) private var assortmentFeature

  private var cancellables = Set<AnyCancellable>()

  public init() {
    assortmentFeature
      .configuration
      .map(\.staticEntriesBottom)
      .removeDuplicates()
      .sink { [weak self] staticEntries in
        self?._appendableStaticNavigationEntries = staticEntries
      }
      .store(in: &cancellables)

    assortmentFeature
      .configuration
      .map(\.staticEntriesTop)
      .removeDuplicates()
      .sink { [weak self] staticEntries in
        self?._insertableNavigationEntries = staticEntries
      }
      .store(in: &cancellables)
  }
}

extension AssortmentNavigation {
  func appendStaticEntries(staticEntries: [AssortmentNavigationEntry]) -> AssortmentNavigation {
    var entries = navigationEntries
    entries.append(contentsOf: staticEntries)
    return AssortmentNavigation(meta: meta, navigationEntries: entries)
  }

  func insertStaticEntries(staticEntries: [AssortmentNavigationEntry]) -> AssortmentNavigation {
    var entries = navigationEntries
    staticEntries
      .enumerated()
      .forEach { index, entry in
        let index = index <= entries.endIndex ? index : entries.endIndex
        entries.insert(entry, at: index)
      }
    return AssortmentNavigation(meta: meta, navigationEntries: entries)
  }

  func insertDealsEntries(dealsEntries: [DealsEntry]) -> AssortmentNavigation {
    var entries = navigationEntries
    dealsEntries
      .forEach { dealsEntry in
        let index = dealsEntry.index <= entries.endIndex ? dealsEntry.index : entries.endIndex
        entries.insert(
          AssortmentNavigationEntry.dealsEntry(dealsEntry),
          at: index
        )
      }
    return AssortmentNavigation(meta: meta, navigationEntries: entries)
  }
}
