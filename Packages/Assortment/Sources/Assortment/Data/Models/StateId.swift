import Foundation

// MARK: StateId

public enum StateId: String, Codable, Sendable, Hashable {
  case active
  case inactive
  case unknown

  public init(from decoder: Decoder) throws {
    let container = try decoder.singleValueContainer()
    let rawValue = try container.decode(String.self)

    switch rawValue {
    case "active", "ios_active":
      self = .active
    case "inactive", "ios_inactive":
      self = .inactive
    default:
      self = .unknown
    }
  }
}
