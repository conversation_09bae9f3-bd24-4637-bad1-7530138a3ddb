import Foundation

// MARK: - AssortmentNavigationEntry

public enum AssortmentNavigationEntry: Equatable, Sendable, Hashable {
  case assortmentEntry(AssortmentEntry)
  case bannerEntry(BannerEntry)
  case dealsEntry(DealsEntry)
  case sectionEntry(AssortmentEntry)
}

// MARK: Codable

extension AssortmentNavigationEntry: Codable {
  enum CodingKeys: String, CodingKey {
    case type
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    let singleContainer = try decoder.singleValueContainer()

    let type = try container.decode(EntryType.self, forKey: .type)
    switch type {
    case .banner:
      let entry = try singleContainer.decode(BannerEntry.self)
      self = .bannerEntry(entry)
    case .categoryLink, .link, .node:
      let entry = try singleContainer.decode(AssortmentEntry.self)
      self = .assortmentEntry(entry)
    case .section:
      let entry = try singleContainer.decode(AssortmentEntry.self)
      self = .sectionEntry(entry)
    }
  }

  public func encode(to encoder: Encoder) throws {
    var singleContainer = encoder.singleValueContainer()
    switch self {
    case let .assortmentEntry(assortmentEntry):
      try singleContainer.encode(assortmentEntry)
    case let .bannerEntry(bannerEntry):
      try singleContainer.encode(bannerEntry)
    case .dealsEntry:
      throw Error.notCodable
    case let .sectionEntry(sectionEntry):
      try singleContainer.encode(sectionEntry)
    }
  }

  enum Error: Swift.Error {
    case notCodable
  }
}
