import Foundation
import OGL10n

// MARK: AssortmentEntry - Common entry

public struct AssortmentEntry: Codable, Equatable, Sendable, Hashable {
  let children: [AssortmentNavigationEntry]?
  let displayType: EntryDisplayType?
  private let label: String
  private let l10n: String?
  let type: EntryType
  let teaser: Teaser?
  let icon: Icon?
  let url: URL?

  var localizedLabel: String {
    guard let l10n else {
      return label
    }
    return ogL10n.resolve(key: l10n)
  }

  public init(
    children: [AssortmentNavigationEntry]?,
    displayType: EntryDisplayType?,
    label: String,
    l10n: String? = nil,
    type: EntryType,
    teaser: Teaser?,
    icon: Icon?,
    url: URL?
  ) {
    self.children = children
    self.displayType = displayType
    self.label = label
    self.l10n = l10n
    self.type = type
    self.teaser = teaser
    self.icon = icon
    self.url = url
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.children = try container.decodeIfPresent(
      [AssortmentNavigationEntry].self, forKey: .children
    )
    self.displayType = try container.decodeIfPresent(EntryDisplayType.self, forKey: .displayType)
    self.l10n = try container.decodeIfPresent(String.self, forKey: .l10n)
    if let l10n {
      self.label = ogL10n.resolve(key: l10n)
    } else {
      self.label = try container.decode(String.self, forKey: .label)
    }
    self.teaser = try container.decodeIfPresent(Teaser.self, forKey: .teaser)
    self.type = try container.decode(EntryType.self, forKey: .type)
    self.icon = try container.decodeIfPresent(Icon.self, forKey: .icon)
    self.url = try container.decodeIfPresent(URL.self, forKey: .url)
  }
}
