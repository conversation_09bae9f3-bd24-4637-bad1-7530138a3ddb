import Foundation

// MARK: AssortmentNavigation - Root

public struct AssortmentNavigation: Codable, Equatable, Sendable {
  public struct Meta: Codable, Equatable, Sendable {
    var baseURL: URL

    public init(baseURL: URL) {
      self.baseURL = baseURL
    }
  }

  let meta: Meta?
  public let navigationEntries: [AssortmentNavigationEntry]

  public init(meta: Meta?, navigationEntries: [AssortmentNavigationEntry]) {
    self.meta = meta
    self.navigationEntries = navigationEntries
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.meta = try container.decodeIfPresent(Meta.self, forKey: .meta)
    if let navigationMeta = decoder.userInfo[.navigationMeta] as? NavigationMeta {
      navigationMeta.baseURL = meta?.baseURL
    }
    self.navigationEntries = try container.decode(
      [AssortmentNavigationEntry].self, forKey: .navigationEntries
    )
  }
}
