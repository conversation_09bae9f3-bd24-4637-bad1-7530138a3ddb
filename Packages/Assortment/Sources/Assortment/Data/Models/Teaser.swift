import Foundation

// MARK: Teaser

public struct Teaser: Codable, Equatable, Sendable, Hashable {
  let url: URL?
  let imageName: String?
  let style: TeaserStyle?

  public init(url: URL?, style: TeaserStyle?, imageName: String? = nil) {
    self.url = url
    self.style = style
    self.imageName = imageName
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.url = try container.decodeIfPresent(URL.self, forKey: .url)
    self.imageName = try container.decodeIfPresent(String.self, forKey: .imageName)
    self.style = try container.decodeIfPresent(TeaserStyle.self, forKey: .style)
  }
}
