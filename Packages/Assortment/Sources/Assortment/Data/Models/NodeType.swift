import Foundation

public enum NodeType: Equatable, Codable {
  case root
  case branch
  case leaf

  init(for entry: AssortmentNavigationEntry) {
    switch entry {
    case let .assortmentEntry(entry):
      self = entry.children?.isEmpty ?? true ? .leaf : .branch
    case .bannerEntry, .dealsEntry:
      self = .leaf
    case let .sectionEntry(entry):
      self = entry.children?.isEmpty ?? true ? .leaf : .branch
    }
  }
}
