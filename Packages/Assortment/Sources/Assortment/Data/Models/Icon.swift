import Foundation

// MARK: Icon

public struct Icon: Codable, Equatable, Sendable, Hashable {
  let url: URL?

  public init(url: URL?) {
    self.url = url
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    let urlString = try container.decodeIfPresent(String.self, forKey: .url)
    if let urlString, let validUrl = URL(string: urlString) {
      self.url = validUrl
    } else {
      self.url = nil
    }
  }
}
