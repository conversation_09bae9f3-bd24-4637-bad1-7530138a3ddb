import Foundation

// MARK: DisplayType

public struct EntryDisplayType: Codable, Equatable, Sendable, Hashable {
  public enum DisplayType: String, Codable, Equatable, Sendable, Hashable {
    case highlighted
    case tab
    case unknown

    public init(from decoder: Decoder) throws {
      let container = try decoder.singleValueContainer()
      self = try Self(rawValue: container.decode(RawValue.self)) ?? .unknown
    }
  }

  let type: DisplayType
  let defaultSelection: Bool?

  public init(type: DisplayType, defaultSelection: Bool?) {
    self.type = type
    self.defaultSelection = defaultSelection
  }
}
