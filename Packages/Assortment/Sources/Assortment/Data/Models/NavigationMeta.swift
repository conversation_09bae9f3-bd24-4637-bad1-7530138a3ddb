import Foundation
import OGDIService
import OGFeatureKit

extension CodingUserInfoKey {
  static let navigationMeta = CodingUserInfoKey(rawValue: "meta")!
}

// MARK: - NavigationMeta

class NavigationMeta {
  var baseURL: URL?
}

extension JSONDecoder {
  convenience init(with navigationMeta: NavigationMeta) {
    self.init()
    userInfo[.navigationMeta] = navigationMeta
  }
}

// MARK: - URLResolver

enum URLResolver {
  static func resolve(url: URL?, against baseUrl: URL?) -> URL? {
    guard let url else { return nil }
    guard url.scheme == nil else { return url }

    var resolvedComponents = URLComponents(url: url, resolvingAgainstBaseURL: true)
    resolvedComponents?.scheme = baseUrl?.scheme
    resolvedComponents?.host = baseUrl?.host

    return resolvedComponents?.url
  }
}
