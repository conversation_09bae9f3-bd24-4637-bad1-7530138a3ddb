import Foundation
import OGCore
import OGDIService
import OGHTTPClient
import OGStorage
import OGTenantKit

// MARK: - AssortmentServicing

protocol AssortmentServicing {
  func fetch() async throws -> AssortmentNavigation
}

// MARK: - AssortmentService

struct AssortmentService: AssortmentServicing {
  private let client: OGRemoteDataRequestable
  private let logger: any OGLoggingDistributable
  private let requestBuilder: AssortmentRequestCreatable
  @OGInjected(\OGCoreContainer.storage) private var storage

  init(
    client: OGRemoteDataRequestable = OGHTTPClientContainer.shared.client(),
    logger: any OGLoggingDistributable = OGCoreContainer.shared.logger(),
    requestBuilder: AssortmentRequestCreatable = AssortmentContainer.shared.requestBuilder()
  ) {
    self.client = client
    self.logger = logger
    self.requestBuilder = requestBuilder
  }

  func fetch() async throws -> AssortmentNavigation {
    let request = try requestBuilder.make()

    do {
      let result = try await client.data(for: request)
      let navigation = try JSONDecoder().decode(AssortmentNavigation.self, from: result.data)
      storage.persist(
        value: result.data,
        forKey: OGStorage.PersistedKey.navigationStoreKey
      )
      return navigation
    } catch {
      logger.log(.warning, domain: .api, message: error.localizedDescription)
      return try getCachedNavigation()
    }
  }

  private func getCachedNavigation() throws -> AssortmentNavigation {
    guard
      let cachedData = storage.value(forKey: OGStorage.PersistedKey.navigationStoreKey) as? Data
    else { throw URLError(.resourceUnavailable) }
    return try JSONDecoder(with: NavigationMeta()).decode(
      AssortmentNavigation.self, from: cachedData
    )
  }
}
