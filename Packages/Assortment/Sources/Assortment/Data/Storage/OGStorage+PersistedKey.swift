import OGStorage
import OGTenantKit

// MARK: - OGStorage.PersistedKey

extension OGStorage {
  enum PersistedKey: RawValueable {
    var rawValue: String {
      switch self {
      case .selectedTabStoreKey:
        "com.ognavigation.horizontal.selectedTab.key"
      case .navigationStoreKey:
        "com.ognavigation.horizontal.selectedTab.key"
          + (OGTenantContainer.shared.selectionService().tenant?.identifier.value ?? "")
      }
    }

    case selectedTabStoreKey
    case navigationStoreKey
  }
}
