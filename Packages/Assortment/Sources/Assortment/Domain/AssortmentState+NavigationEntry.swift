import Foundation

// MARK: - AssortmentState.NavigationEntry

extension AssortmentState {
  public struct NavigationEntry: Equatable, Codable, Hashable, Identifiable {
    public let id: String
    let assortmentNavigationEntry: AssortmentNavigationEntry
    let nodeType: NodeType

    var isRoot: Bool { nodeType == .root }
    var isBranch: Bool { nodeType == .branch }
    var isLeaf: Bool { nodeType == .leaf }
    var hasTeaser: Bool { teaser != nil }

    var imageUrl: URL? {
      switch assortmentNavigationEntry {
      case .bannerEntry:
        bannerState?.imageUrl
      case .assortmentEntry, .sectionEntry:
        teaser?.url
      case let .dealsEntry(entry):
        entry.imageUrl
      }
    }

    var teaserImageUrl: URL? {
      teaser?.url
    }

    var subTitle: String? {
      switch assortmentNavigationEntry {
      case let .dealsEntry(entry):
        entry.subtitle
      case .assortmentEntry, .bannerEntry, .sectionEntry:
        nil
      }
    }

    var teaser: Teaser? {
      switch assortmentNavigationEntry {
      case let .assortmentEntry(entry):
        entry.teaser
      case let .sectionEntry(entry):
        entry.teaser
      case .bannerEntry, .dealsEntry:
        nil
      }
    }

    var url: URL? {
      switch assortmentNavigationEntry {
      case let .assortmentEntry(entry),
           let .sectionEntry(entry):
        entry.url
      case let .dealsEntry(entry):
        entry.url
      case .bannerEntry:
        bannerState?.url
      }
    }

    public init(
      uuid: String = UUID().uuidString,
      assortmentNavigationEntry: AssortmentNavigationEntry,
      nodeType: NodeType
    ) {
      self.id = uuid
      self.assortmentNavigationEntry = assortmentNavigationEntry
      self.nodeType = nodeType
    }
  }
}

extension AssortmentState.NavigationEntry {
  var bannerState: BannerState? {
    guard
      case let .bannerEntry(entry) = assortmentNavigationEntry
    else { return nil }
    let stateId: StateId = .unknown
    return entry.states.first { $0.id == stateId }
  }

  private var children: [AssortmentNavigationEntry]? {
    switch assortmentNavigationEntry {
    case let .assortmentEntry(entry):
      entry.children
    case let .sectionEntry(entry):
      entry.children
    case .bannerEntry, .dealsEntry:
      nil
    }
  }

  var childEntries: [AssortmentState.NavigationEntry] {
    children?
      .compactMap {
        AssortmentState.NavigationEntry(
          assortmentNavigationEntry: $0,
          nodeType: NodeType(for: $0)
        )
      } ?? []
  }

  var displayType: EntryDisplayType? {
    switch assortmentNavigationEntry {
    case let .assortmentEntry(entry):
      entry.displayType
    case let .sectionEntry(entry):
      entry.displayType
    case .bannerEntry, .dealsEntry:
      nil
    }
  }

  var entryType: EntryType {
    switch assortmentNavigationEntry {
    case let .assortmentEntry(entry):
      return entry.type
    case let .bannerEntry(entry):
      return entry.type
    case let .dealsEntry(entry):
      return entry.type
    case let .sectionEntry(entry):
      return entry.type
    }
  }

  var iconURL: URL? {
    switch assortmentNavigationEntry {
    case let .assortmentEntry(assortmentEntry):
      assortmentEntry.icon?.url
    case .bannerEntry, .dealsEntry, .sectionEntry:
      nil
    }
  }

  var imageName: String? {
    bannerState?.imageName
  }

  var title: String {
    switch assortmentNavigationEntry {
    case let .assortmentEntry(entry):
      entry.localizedLabel
    case .bannerEntry:
      ""
    case let .dealsEntry(entry):
      entry.title ?? ""
    case let .sectionEntry(entry):
      entry.localizedLabel
    }
  }
}
