import Combine
import Deals
import Foundation
import OGCore
import OGDomainStore
import OGFeatureAdapter
import OGStorage
import OGTenantKit
import OGTracker

public typealias AssortmentStore = OGDomainStore<AssortmentState, AssortmentState.Action>

extension OGDomainStoreFactory {
  static func make() -> AssortmentStore {
    AssortmentStore(
      reducer: AssortmentState.Reducer.reduce,
      middlewares: AssortmentState.Middleware(),
      connector: AssortmentState.Connector()
    )
  }
}

// MARK: - AssortmentState

public struct AssortmentState: OGDomainState {
  private(set) var assortmentNavigation: AssortmentNavigation?
  public private(set) var isAwaitingUpdate = false
  private(set) var navigationEntries: [NavigationEntry] = []
  private(set) var selectedTabEntry: NavigationEntry?

  private var topNavigationEntries: [AssortmentNavigationEntry] = []
  private var bottomNavigationEntries: [AssortmentNavigationEntry] = []
  private var dealsCampaigns: [Campaign] = []

  var tabEntries: [NavigationEntry] {
    navigationEntries.filter { $0.displayType?.type == .tab }
  }

  public init(
    isAwaitingUpdate: Bool = false,
    assortmentNavigation: AssortmentNavigation? = nil,
    bottomNavigationEntries: [AssortmentNavigationEntry] = [],
    topNavigationEntries: [AssortmentNavigationEntry] = [],
    dealsCampaigns: [Campaign] = [],
    selectedTabEntry: NavigationEntry? = nil
  ) {
    self.navigationEntries = []
    self.isAwaitingUpdate = isAwaitingUpdate
    self.assortmentNavigation = assortmentNavigation
    self.bottomNavigationEntries = bottomNavigationEntries
    self.topNavigationEntries = topNavigationEntries
    self.dealsCampaigns = dealsCampaigns
    self.selectedTabEntry = selectedTabEntry

    update()
  }

  fileprivate mutating func update(
    assortmentNavigation: AssortmentNavigation? = nil,
    bottomNavigationEntries: [AssortmentNavigationEntry]? = nil,
    topNavigationEntries: [AssortmentNavigationEntry]? = nil,
    dealsCampaigns: [Campaign]? = nil,
    selectedTabEntry: NavigationEntry? = nil
  ) {
    self.assortmentNavigation = assortmentNavigation ?? self.assortmentNavigation
    self.bottomNavigationEntries = bottomNavigationEntries ?? self.bottomNavigationEntries
    self.topNavigationEntries = topNavigationEntries ?? self.topNavigationEntries
    self.dealsCampaigns = dealsCampaigns ?? self.dealsCampaigns
    self.selectedTabEntry = selectedTabEntry ?? self.selectedTabEntry

    let dealsEntries: [DealsEntry] = self.dealsCampaigns
      .compactMap { campaign in
        guard
          let banner = campaign.banner,
          let imageUrl = URL(string: banner.image.url)
        else { return nil }
        return DealsEntry(
          bannerId: banner.id,
          campaignId: campaign.id,
          imageUrl: imageUrl,
          index: Int(banner.index),
          title: banner.title?.value,
          subtitle: banner.subtitle?.value
        )
      }

    let assortmentNavigationEntries = self.assortmentNavigation?
      .appendStaticEntries(staticEntries: self.bottomNavigationEntries)
      .insertStaticEntries(staticEntries: self.topNavigationEntries)
      .insertDealsEntries(dealsEntries: dealsEntries)

    navigationEntries = assortmentNavigationEntries?
      .navigationEntries
      .compactMap {
        NavigationEntry(
          assortmentNavigationEntry: $0,
          nodeType: .root
        )
      } ?? []
  }

  public static var initial: AssortmentState = .init()
}

// MARK: AssortmentState.Action

extension AssortmentState {
  public enum Action: OGDomainAction {
    case fetch
    case didSelectTabEntry(NavigationEntry)
    case didSelectEntry(AssortmentState.NavigationEntry)

    /// Private actions
    case _update(AssortmentNavigation)
    case _updateBottomNavigationEntries([AssortmentNavigationEntry])
    case _updateTopNavigationEntries([AssortmentNavigationEntry])
    case _updateDealsCampaigns([Campaign])
    case _updateSelectedTabEntry(NavigationEntry)
  }
}

// MARK: AssortmentState.Reducer

extension AssortmentState {
  enum Reducer {
    static func reduce(
      state: inout AssortmentState,
      action: AssortmentState.Action
    ) {
      switch action {
      case .didSelectEntry, .didSelectTabEntry, .fetch:
        break

      case let ._update(assortmentNavigation):
        state.update(assortmentNavigation: assortmentNavigation)

      case let ._updateBottomNavigationEntries(bottomNavigationEntries):
        state.update(bottomNavigationEntries: bottomNavigationEntries)

      case let ._updateTopNavigationEntries(topNavigationEntries):
        state.update(topNavigationEntries: topNavigationEntries)

      case let ._updateDealsCampaigns(campaigns):
        state.update(dealsCampaigns: campaigns)

      case let ._updateSelectedTabEntry(tabEntry):
        state.update(selectedTabEntry: tabEntry)
      }
    }
  }
}

// MARK: AssortmentState.Middleware

extension AssortmentState {
  struct Middleware: OGDomainMiddleware {
    private let assortmentService: AssortmentServicing
    private let storage: any AnyPersistable
    private let tracker: OGTrackerProtocol

    init(
      assortmentService: AssortmentServicing = AssortmentContainer.shared.assortmentService(),
      storage: any AnyPersistable = OGCoreContainer.shared.storage(),
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()
    ) {
      self.assortmentService = assortmentService
      self.storage = storage
      self.tracker = tracker
    }

    func callAsFunction(
      action: AssortmentState.Action,
      for state: AssortmentState
    ) async throws -> AssortmentState.Action? {
      switch action {
      case let .didSelectEntry(entry):
        trackEntitySelectedEvent(for: entry)
        return nil

      case let .didSelectTabEntry(entry):
        storage.persist(value: entry.title, forKey: OGStorage.PersistedKey.selectedTabStoreKey)

        let event = InteractionEvent.CategoryMenuEntry(categoryLabel: entry.title)
        tracker.multiplatformTrack(event: event)
        return ._updateSelectedTabEntry(entry)

      case .fetch:
        let navigation = try await assortmentService.fetch()
        return ._update(navigation)

      case ._update:
        return getSelectedTab(state: state)

      case ._updateBottomNavigationEntries,
           ._updateDealsCampaigns,
           ._updateSelectedTabEntry,
           ._updateTopNavigationEntries:
        return nil
      }
    }

    private func getSelectedTab(state: AssortmentState) -> AssortmentState.Action? {
      guard
        !state.tabEntries.isEmpty
      else { return nil }

      let selectedTabTitle = storage.value(
        forKey: OGStorage.PersistedKey.selectedTabStoreKey
      ) as? String

      let userSelected = state.tabEntries.first { $0.title == selectedTabTitle }

      let defaultSelected = state.tabEntries.first {
        $0.displayType?.defaultSelection == true
      }

      guard
        let selectedTabEntry = userSelected ?? defaultSelected ?? state.tabEntries.first
      else { return nil }
      return ._updateSelectedTabEntry(selectedTabEntry)
    }

    private func trackEntitySelectedEvent(for navigationEntry: AssortmentState.NavigationEntry) {
      var event: TrackingEvent
      switch navigationEntry.assortmentNavigationEntry {
      case let .sectionEntry(entry):
        event = InteractionEvent.CategoryMenuEntry(categoryLabel: entry.localizedLabel)

      case let .assortmentEntry(entry):
        if entry.teaser?.url != nil || navigationEntry.isRoot || entry.type == .banner {
          event = InteractionEvent.CategoryMenuEntry(categoryLabel: entry.localizedLabel)
        } else {
          event = InteractionEvent.CategorySubMenuEntry(categoryLabel: entry.localizedLabel)
        }

      case let .bannerEntry(entry):
        event = InteractionEvent.CategoryBannerEntry(categoryLabel: entry.id)

      case let .dealsEntry(entry):
        event = InteractionEvent.CategoryBannerEntry(categoryLabel: entry.campaignId)
      }

      tracker.multiplatformTrack(event: event)
    }
  }
}

// MARK: AssortmentState.Connector

extension AssortmentState {
  actor Connector: OGDomainConnector {
    private var cancellables: Set<AnyCancellable> = []
    private let campaignsListStore: any OGDomainStateObservable<CampaignsState>
    private let dealsFeature: DealsFeatureAdaptable
    private let staticEntriesProvider: AssortmentStaticEntriesProviding
    private let baseUrlFeatureAdapter: OGBaseUrlFeatureAdaptable

    init(
      campaignsListStore: any OGDomainStateObservable<CampaignsState> = DealsContainer.shared.campaignsListStore(),
      dealsFeature: DealsFeatureAdaptable = DealsFeatureAdapterContainer.shared.deals(),
      staticEntriesProvider: AssortmentStaticEntriesProviding = AssortmentContainer.shared.staticEntriesProvider(),
      baseUrlFeatureAdapter: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl()
    ) {
      self.campaignsListStore = campaignsListStore
      self.dealsFeature = dealsFeature
      self.staticEntriesProvider = staticEntriesProvider
      self.baseUrlFeatureAdapter = baseUrlFeatureAdapter
    }

    func configure(
      dispatch: @escaping (Action) async -> Void
    ) async {
      watchStaticEntries(dispatch: dispatch)
      watchDealsCampaigns(dispatch: dispatch)
      watchBaseUrl(dispatch: dispatch)
      await dispatch(.fetch)
    }

    private func watchStaticEntries(
      dispatch: @escaping (Action) async -> Void
    ) {
      staticEntriesProvider
        .appendableStaticNavigationEntries
        .removeDuplicates()
        .sink { navigationEntries in
          Task {
            await dispatch(._updateBottomNavigationEntries(navigationEntries))
          }
        }
        .store(in: &cancellables)

      staticEntriesProvider
        .insertableNavigationEntries
        .removeDuplicates()
        .sink { navigationEntries in
          Task {
            await dispatch(._updateTopNavigationEntries(navigationEntries))
          }
        }
        .store(in: &cancellables)
    }

    private func watchDealsCampaigns(
      dispatch: @escaping (Action) async -> Void
    ) {
      Task {
        await Publishers.CombineLatest(
          dealsFeature
            .configuration
            .map(\.isEnabled)
            .removeDuplicates()
            .filter { $0 },
          campaignsListStore
            .watch(keyPath: \.activeCampaigns)
            .removeDuplicates()
            .compactMap { $0 }
        )
        .subscribe(
          Subscribers.Sink(
            receiveCompletion: { _ in
            },
            receiveValue: { _, campaigns in
              Task {
                await dispatch(._updateDealsCampaigns(campaigns))
              }
            }
          )
        )
      }
    }

    private func watchBaseUrl(
      dispatch: @escaping (Action) async -> Void
    ) {
      Task {
        baseUrlFeatureAdapter
          .api
          .removeDuplicates()
          .dropFirst()
          .sink { _ in
            Task {
              await dispatch(.fetch)
            }
          }
          .store(in: &cancellables)
      }
    }
  }
}
