import AsyncAlgorithms
import Combine
import Foundation
import OGDomainStore
import OGL10n
import OGTracker
import OGViewStore

// MARK: - AssortmentRootView.Store

extension AssortmentRootView {
  typealias Store = OGViewStore<State, Event>
}

// MARK: - Factory

extension AssortmentRootView {
  static func make() -> Store {
    AssortmentRootView.Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector()
    )
  }
}

// MARK: - AssortmentRootView.Event

extension AssortmentRootView {
  enum Event: OGViewEvent {
    case didAppear
    case _updateIsEnabled(Bool)
    case _updateNavigationEntries([AssortmentState.NavigationEntry])
  }
}

// MARK: - AssortmentRootView.State

extension AssortmentRootView {
  struct State: OGViewState {
    private(set) var isEnabled: Bool
    private(set) var hasTabEntries: Bool

    var title: String {
      ogL10n.Navigation.Assortment.Title
    }

    init(
      hasTabEntries: Bool = false,
      isEnabled: Bool = true
    ) {
      self.hasTabEntries = hasTabEntries
      self.isEnabled = isEnabled
    }

    mutating func updateNavigationEntries(
      isFeatureEnabled: Bool? = nil,
      navigationEntries: [AssortmentState.NavigationEntry]? = nil
    ) {
      if let navigationEntries {
        let tabEntries = navigationEntries.filter { $0.displayType?.type == .tab }
        hasTabEntries = !tabEntries.isEmpty
      }
      isEnabled = isFeatureEnabled ?? isEnabled
    }

    static let initial: State = .init()
  }
}

// MARK: - AssortmentRootView.Reducer

extension AssortmentRootView {
  enum Reducer {
    static func reduce(state: inout State, with event: Event) {
      switch event {
      case .didAppear:
        break

      case let ._updateIsEnabled(isFeatureEnabled):
        state.updateNavigationEntries(isFeatureEnabled: isFeatureEnabled)

      case let ._updateNavigationEntries(navigationEntries):
        state.updateNavigationEntries(navigationEntries: navigationEntries)
      }
    }
  }
}

// MARK: - AssortmentRootView.Middleware

extension AssortmentRootView {
  struct Middleware: OGViewStoreMiddleware {
    private let tracker: OGTrackerProtocol

    init(tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()) {
      self.tracker = tracker
    }

    func callAsFunction(event: Event, for state: State) async -> Event? {
      switch event {
      case .didAppear:
        tracker.multiplatformTrack(event: ViewEvent.ScreenCategoryMenu())
        return nil
      case ._updateIsEnabled, ._updateNavigationEntries:
        return nil
      }
    }
  }
}

// MARK: - AssortmentRootView.Connector

extension AssortmentRootView {
  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    private let feature: AssortmentFeatureAdaptable
    private let store: any OGDomainStoreViewStoreConsumable<AssortmentState>

    init(
      feature: AssortmentFeatureAdaptable = AssortmentFeatureAdapterContainer.shared.assortment(),
      store: any OGDomainStoreViewStoreConsumable<AssortmentState> = AssortmentContainer.shared.assortmentStore()
    ) {
      self.feature = feature
      self.store = store
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      feature.configuration
        .map(\.isEnabled)
        .removeDuplicates()
        .sink { isEnabled in
          Task {
            await dispatch(._updateIsEnabled(isEnabled))
          }
        }
        .store(in: &cancellables)

      Task {
        await store.watch(keyPath: \.navigationEntries)
          .removeDuplicates()
          .sink { navigationEntries in
            Task {
              await dispatch(._updateNavigationEntries(navigationEntries))
            }
          }
          .store(in: &cancellables)
      }
    }
  }
}
