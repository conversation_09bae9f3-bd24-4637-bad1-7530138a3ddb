import AppCore
import Combine
import OGL10n
import SwiftUI
import UICatalog

// MARK: - AssortmentItemView

public struct AssortmentItemView: View {
  let index: Int
  let count: Int
  let navigationEntry: AssortmentState.NavigationEntry
  @EnvironmentObject private var deviceState: DeviceState

  public init(
    index: Int,
    count: Int,
    navigationEntry: AssortmentState.NavigationEntry
  ) {
    self.index = index
    self.count = count
    self.navigationEntry = navigationEntry
  }

  public var body: some View {
    switch navigationEntry.assortmentNavigationEntry {
    case .assortmentEntry:
      assortmentEntryView()
    case .bannerEntry:
      AssortmentBannerView(navigationEntry: navigationEntry)
    case .dealsEntry:
      assortmentDealsEntryView()
    case .sectionEntry:
      sectionView()
    }
  }

  @ViewBuilder
  private func assortmentEntryView() -> some View {
    if navigationEntry.isRoot, navigationEntry.hasTeaser, !deviceState.isLandscapeOrientation {
      assortmentImageView(index: index, count: count)
    } else {
      AssortmentTitleView(
        index: index,
        count: count,
        navigationEntry: navigationEntry
      )
    }
  }

  @ViewBuilder
  private func assortmentDealsEntryView() -> some View {
    if deviceState.isLandscapeOrientation {
      AssortmentTitleView(
        index: index,
        count: count,
        navigationEntry: navigationEntry
      )
    } else {
      AssortmentDealsView(navigationEntry: navigationEntry)
    }
  }

  @ViewBuilder
  private func sectionView() -> some View {
    if navigationEntry.isRoot, !deviceState.isLandscapeOrientation {
      if navigationEntry.hasTeaser {
        assortmentImageView(index: index, count: count)
      } else {
        AssortmentGridView(navigationEntry: navigationEntry)
      }
    } else if deviceState.isLandscapeOrientation {
      compactEntries
    } else {
      AssortmentTitleView(
        index: index,
        count: count,
        navigationEntry: navigationEntry
      )
    }
  }

  @ViewBuilder private var compactEntries: some View {
    LazyVStack {
      if !navigationEntry.title.isEmpty {
        Text(navigationEntry.title)
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.backgroundBackground100.color)
          .frame(maxWidth: .infinity, alignment: .leading)
          .padding(UILayoutConstants.AssortmentTitleView.defaulTextPadding)
      }
      ForEach(navigationEntry.childEntries.indices, id: \.self) { index in
        if let entry = navigationEntry.childEntries[safe: index] {
          AssortmentItemView(
            index: index,
            count: navigationEntry.childEntries.count,
            navigationEntry: entry
          )
        }
      }
    }
  }

  @ViewBuilder
  private func assortmentImageView(
    index: Int,
    count: Int
  ) -> some View {
    let accessibilityLabel = ogL10n.Assortment.CategoriesList.Item.Accessibility(
      itemName: navigationEntry.title,
      itemNumber: "\(index + 1)",
      itemCount: "\(count)"
    )

    switch navigationEntry.teaser?.style?.class {
    case .square:
      AssortmentSquareImageView(navigationEntry: navigationEntry)
    default:
      AssortmentWideImageView(
        accessibilityLabel: accessibilityLabel,
        navigationEntry: navigationEntry
      )
    }
  }
}

// MARK: - AssortmentListItem_Previews

struct AssortmentListItem_Previews: PreviewProvider {
  static var previews: some View {
    let dummyEntry = AssortmentEntry(
      children: [],
      displayType: nil,
      label: "Dummy",
      l10n: nil,
      type: .banner,
      teaser: Teaser(
        url: URL(string: "google.com"),
        style: nil
      ),
      icon: nil,
      url: nil
    )
    let navigationEntry = AssortmentState.NavigationEntry(
      assortmentNavigationEntry: .assortmentEntry(dummyEntry),
      nodeType: .root
    )
    AssortmentItemView(index: 0, count: 1, navigationEntry: navigationEntry)
  }
}
