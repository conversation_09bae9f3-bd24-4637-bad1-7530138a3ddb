import OGAsyncImage
import OGDIService
import OGIdentifier
import OGL10n
import OGRouter
import SwiftUI
import UICatalog

// MARK: - AssortmentTitleView

struct AssortmentTitleView: View {
  private let index: Int
  private let count: Int
  @StateObject private var viewStore: AssortmentItemView.Store

  init(
    index: Int,
    count: Int,
    navigationEntry: AssortmentState.NavigationEntry
  ) {
    self.index = index
    self.count = count
    self._viewStore = StateObject(wrappedValue: AssortmentItemView.make(navigationEntry: navigationEntry))
  }

  private var titleColor: Color {
    viewStore.navigationEntry.displayType?.type == .highlighted
      ? OGColors.accentSale.color : OGColors.backgroundBackground100.color
  }

  var body: some View {
    titleView()
  }

  @ViewBuilder
  private func titleView() -> some View {
    Button {
      Task {
        switch viewStore.navigationEntry.assortmentNavigationEntry {
        case .dealsEntry:
          await viewStore.dispatch(.didSelectDeals)
        default:
          await viewStore.dispatch(.didSelectEntry)
        }
      }
    } label: {
      HStack(alignment: .center, spacing: 0) {
        if let iconURL = viewStore.navigationEntry.iconURL {
          OGAsyncImage(url: iconURL, contentMode: .fit)
            .frame(
              width: UILayoutConstants.AssortmentTitleView.iconSize,
              height: UILayoutConstants.AssortmentTitleView.iconSize
            )
            .padding(.leading, UILayoutConstants.Default.padding2x)
        }
        Text(viewStore.navigationEntry.title)
          .lineLimit(1)
          .font(for: .copyMRegular)
          .foregroundColor(titleColor)
          .frame(maxWidth: .infinity, alignment: .leading)
          .padding(.horizontal, UILayoutConstants.Default.padding2x)

        Spacer()
        if [.node, .section].contains(viewStore.navigationEntry.entryType) {
          OGImages.icon24x24ChevronRightPrimary.image
            .accessibilityHidden(true)
            .padding(.trailing, UILayoutConstants.Default.padding2x)
        }
      }
      .background(OGColors.backgroundBackground0.color)
      .frame(
        height: UILayoutConstants.AssortmentTitleView.height
      )
      .separator()
    }
    .accessibilityHidden(viewStore.isAccessibilityHidden)
    .accessibilityLabel(accessibilityLabel)
  }

  private var accessibilityLabel: String {
    ogL10n.Assortment.CategoriesList.Item.Accessibility(
      itemName: viewStore.navigationEntry.title,
      itemNumber: "\(index + 1)",
      itemCount: "\(count)"
    )
  }
}

// MARK: - UILayoutConstants.AssortmentTitleView

extension UILayoutConstants {
  enum AssortmentTitleView {
    static let height = 44.0
    static let iconSize = 24.0
    static let defaulTextPadding = EdgeInsets(
      top: 11.0,
      leading: UILayoutConstants.Default.padding2x,
      bottom: 11.0,
      trailing: UILayoutConstants.Default.padding
    )
  }
}
