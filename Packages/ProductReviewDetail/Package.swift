// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "ProductReviewDetail",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "ProductReviewDetail",
      targets: ["ProductReviewDetail"]
    )
  ],
  dependencies: [
    .package(path: "../ExternalDependencies"),
    .package(path: "../UICatalog"),
    .package(path: "../OGAsyncImage"),
    .package(path: "../NativeAPI")
  ],
  targets: [
    .target(
      name: "ProductReviewDetail",
      dependencies: [
        "ExternalDependencies",
        "UICatalog",
        "OGAsyncImage",
        "NativeAPI"
      ]
    ),
    .testTarget(
      name: "ProductReviewDetailTests",
      dependencies: ["ProductReviewDetail"]
    )
  ]
)
