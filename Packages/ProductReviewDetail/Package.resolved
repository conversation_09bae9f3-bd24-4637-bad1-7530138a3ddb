{"pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "194a6706acbd25e4ef639bcaddea16e8758a3e27", "version": "1.2024011602.0"}}, {"identity": "adjust_signature_sdk", "kind": "remoteSourceControl", "location": "https://github.com/adjust/adjust_signature_sdk.git", "state": {"revision": "be8480912886a247e41711a4dbdd0ffebcf8eee3", "version": "3.35.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "3b62f154d00019ae29a71e9738800bb6f18b236d", "version": "10.19.2"}}, {"identity": "bettercodable", "kind": "remoteSourceControl", "location": "https://github.com/marksands/BetterCodable", "state": {"revision": "61153170668db7a46a20a87e35e70f80b24d4eb5", "version": "0.4.0"}}, {"identity": "factory", "kind": "remoteSourceControl", "location": "https://github.com/hmlongco/Factory", "state": {"revision": "061b3afe0358a0da7ce568f8272c847910be3dd7", "version": "2.2.0"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "42eae77a0af79e9c3f41df04a23c76f05cfdda77", "version": "10.24.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "51ba746a9d51a4bd0774b68499b0c73ef6e8570d", "version": "10.24.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "a637d318ae7ae246b02d7305121275bc75ed5565", "version": "9.4.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "57a1d307f42df690fdef2637f3e5b776da02aad6", "version": "7.13.3"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "e9fad491d0673bdda7063a0341fb6b47a30c5359", "version": "1.62.2"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "2d12673670417654f08f5f90fdd62926dc3a2648", "version": "100.0.0"}}, {"identity": "ios-library", "kind": "remoteSourceControl", "location": "https://github.com/urbanairship/ios-library", "state": {"revision": "53040c77617a2acc5d9a7b69cf5bbbf36f94ad4b", "version": "17.7.3"}}, {"identity": "ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/phrase/ios-sdk", "state": {"revision": "845e8b1c75d03c2b483a40e15771f6cd3c40fdfd", "version": "4.4.2"}}, {"identity": "ios_sdk", "kind": "remoteSourceControl", "location": "https://github.com/adjust/ios_sdk", "state": {"revision": "81037ba7ccb3c56494ab19295ac5ca747c234e02", "version": "5.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "lottie-ios", "kind": "remoteSourceControl", "location": "https://github.com/airbnb/lottie-ios", "state": {"revision": "fe4c6fe3a0aa66cdeb51d549623c82ca9704b9a5", "version": "4.5.0"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "navigationbackport", "kind": "remoteSourceControl", "location": "https://github.com/johnpatrickmorgan/NavigationBackport", "state": {"revision": "fb860a404f8c0aabeca1016400e41ece5363a369", "version": "0.9.0"}}, {"identity": "og-dx_aac-ios-module-otto_group_commerce_kit", "kind": "remoteSourceControl", "location": "**************:aacml/og-dx_aac-ios-module-otto_group_commerce_kit.git", "state": {"branch": "OGAK-1382-iOS-PoC-for-Native-Components", "revision": "f63905e1c949ea1477bf78def54dea1d7b2d962b"}}, {"identity": "og-dx_aac-multiplatform-sdk", "kind": "remoteSourceControl", "location": "https://github.com/aacml/og-dx_aac-multiplatform-sdk.git", "state": {"revision": "1ce40d4b96eff74cd402294db4a0b6a7891dde51", "version": "3.0.0-alpha.37"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "snowplow-ios-tracker", "kind": "remoteSourceControl", "location": "https://github.com/snowplow/snowplow-ios-tracker", "state": {"revision": "20b1fea9c58334e569cb63d71875d3c2d0243483", "version": "6.0.7"}}, {"identity": "swift-async-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-async-algorithms", "state": {"revision": "5c8bd186f48c16af0775972700626f0b74588278", "version": "1.0.2"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "671108c96644956dddcd89dd59c203dcdb36cec7", "version": "1.1.4"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "bb5059bde9022d69ac516803f4f227d8ac967f71", "version": "1.1.0"}}, {"identity": "swift-http-types", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-http-types.git", "state": {"revision": "1827dc94bdab2eb5f2fc804e9b0cb43574282566", "version": "1.0.2"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "ebc7251dd5b37f627c93698e4374084d98409633", "version": "1.28.2"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-syntax.git", "state": {"revision": "0687f71944021d616d34d922343dcef086855920", "version": "600.0.1"}}, {"identity": "swiftbackports", "kind": "remoteSourceControl", "location": "https://github.com/shaps80/SwiftBackports", "state": {"revision": "ddca6a237c1ba2291d5a3cc47ec8480ce6e9f805", "version": "1.0.3"}}, {"identity": "swiftuibackports", "kind": "remoteSourceControl", "location": "https://github.com/shaps80/SwiftUIBackports.git", "state": {"revision": "3042f0cf4b9f0d5b0bb08ad17f742a43bc4fdfc5", "version": "2.8.0"}}, {"identity": "ziparchive", "kind": "remoteSourceControl", "location": "https://github.com/ZipArchive/ZipArchive.git", "state": {"revision": "38e0ce0598e06b034271f296a8e15b149c91aa19", "version": "2.4.3"}}], "version": 2}