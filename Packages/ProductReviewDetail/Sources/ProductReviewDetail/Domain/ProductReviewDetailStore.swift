import Combine
import Foundation
import OGAppKitSDK
import OGCore
import OGDIService
import OGDomainStore
import OGRouter

typealias ProductReviewDetailStore = OGDomainStore<ProductReviewDetailState, ProductReviewDetailAction>

extension OGDomainStoreFactory {
  static func make(productId: String) -> ProductReviewDetailStore {
    let service = ProductReviewDetailContainer.shared.service()
    return ProductReviewDetailStore(
      reducer: ProductReviewDetailState.Reducer.reduce,
      middlewares: ProductReviewDetailState.Middleware(productId: productId, service: service),
      connector: ProductReviewDetailState.ProductReviewDetailConnector(service: service)
    )
  }
}

// MARK: - OGProductReviewDetailComponent + Sendable

extension OGProductReviewDetailComponent: @retroactive @unchecked Sendable {}

// MARK: - ProductReviewDetailState

struct ProductReviewDetailState: OGDomainState {
  private(set) var isAwaitingUpdate: Bool
  private(set) var components: [OGProductReviewDetailComponent]
  private(set) var componentConfigsJson: String
  private(set) var currentSortOption: OGSortingOption
  private(set) var currentFilterRating: Int?

  init(
    isAwaitingUpdate: Bool = false,
    components: [OGProductReviewDetailComponent] = [],
    componentConfigsJson: String = "",
    currentSortOption: ReviewsSortingOptions.SortingOption = .recent,
    currentFilterRating: Int? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.components = components
    self.componentConfigsJson = componentConfigsJson
    self.currentSortOption = currentSortOption
    self.currentFilterRating = currentFilterRating
  }

  static let initial: Self = .init()

  mutating func update(
    isAwaitingUpdate: Bool? = nil,
    components: [OGProductReviewDetailComponent]? = nil,
    componentConfigsJson: String? = nil,
    currentSortOption: OGSortingOption? = nil,
    currentFilterRating: Int? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate ?? self.isAwaitingUpdate
    self.components = components ?? self.components
    self.componentConfigsJson = componentConfigsJson ?? self.componentConfigsJson
    self.currentSortOption = currentSortOption ?? self.currentSortOption
    self.currentFilterRating = currentFilterRating ?? self.currentFilterRating
  }

  mutating func update(currentFilterRating: Int?) {
    self.currentFilterRating = currentFilterRating
  }
}

// MARK: Sendable

extension ProductReviewDetailState: Sendable {}

// MARK: - ProductReviewDetailAction

enum ProductReviewDetailAction: OGDomainAction {
  case getReviewScreen(String)
  case filterReviews(String, Int?)
  case sortReviews(String, OGSortingOption)
  case loadMoreReviews(String)
  /// private
  case _setComponentConfigsJson(String)
  case _setReviewScreen([OGProductReviewDetailComponent])
  case _throwError(ProductReviewDetailError)
}

extension ProductReviewDetailState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductReviewDetailState,
      with action: ProductReviewDetailAction
    ) {
      switch action {
      case .getReviewScreen:
        state.update(isAwaitingUpdate: true)

      case let ._setReviewScreen(components):
        state.update(
          isAwaitingUpdate: false,
          components: components
        )

      case let ._setComponentConfigsJson(componentConfigsJson):
        state.update(componentConfigsJson: componentConfigsJson)

      case let .filterReviews(_, rating):
        state.update(currentFilterRating: rating)

      case let .sortReviews(_, option):
        state.update(currentSortOption: option)

      case ._throwError, .loadMoreReviews:
        break
      }
    }
  }

  // MARK: - ProductReviewDetailMiddleware

  struct Middleware: OGDomainMiddleware {
    private let service: ProductReviewDetailServing
    private let logger: any OGLoggingDistributable
    private let productId: String

    init(
      productId: String,
      service: ProductReviewDetailServing,
      logger: any OGLoggingDistributable = OGCoreContainer.shared.logger()
    ) {
      self.service = service
      self.logger = logger
      self.productId = productId
    }

    func callAsFunction(
      action: ProductReviewDetailAction,
      for state: ProductReviewDetailState
    ) async throws -> ProductReviewDetailAction? {
      switch action {
      case let .getReviewScreen(id):
        await service.fetchProductReview(
          id: id,
          componentConfigsJson: state.componentConfigsJson
        )
        return nil

      case let .filterReviews(screenId, rating):
        await service.filterProductReviews(
          screenId: screenId,
          filterRating: rating
        )
        return nil

      case let .sortReviews(screenId, option):
        await service
          .sortProductReviews(
            screenId: screenId,
            sortingOption: option
          )
        return nil

      case let .loadMoreReviews(screenId):
        await service
          .showMoreReviews(
            screenId: screenId
          )
        return nil

      case ._setComponentConfigsJson:
        guard !state.componentConfigsJson.isEmpty else { return nil }
        return .getReviewScreen(productId)

      case let ._throwError(error):
        logger
          .log(
            .critical,
            domain: .service,
            message: error.localizedDescription
          )
        throw error

      case ._setReviewScreen:
        return nil
      }
    }
  }

  actor ProductReviewDetailConnector: OGDomainConnector {
    private let service: ProductReviewDetailServing
    private let feature: ProductReviewDetailFeatureAdaptable
    private var cancellables = Set<AnyCancellable>()
    private var successTask: Task<Void, Never>?
    private var errorTask: Task<Void, Never>?

    init(
      service: ProductReviewDetailServing,
      feature: ProductReviewDetailFeatureAdaptable = ProductReviewDetailFeatureAdapterContainer.shared.productReviewDetail()
    ) {
      self.service = service
      self.feature = feature
    }

    public func configure(
      dispatch: @escaping (ProductReviewDetailAction) async -> Void
    ) async {
      feature.configuration
        .map(\.components)
        .removeDuplicates()
        .sink { components in
          Task {
            await dispatch(._setComponentConfigsJson(components))
          }
        }.store(in: &cancellables)

      let service = service
      successTask = Task.detached { [service] in
        for await components in await service.successStream {
          await dispatch(._setReviewScreen(components))
        }
      }

      errorTask = Task.detached { [service] in
        for await error in await service.errorStream {
          await dispatch(._throwError(.unknown(error)))
        }
      }
    }

    deinit {
      successTask?.cancel()
      errorTask?.cancel()
    }
  }
}

// MARK: - ProductReviewDetailError

public enum ProductReviewDetailError: Error, Equatable {
  public static func == (lhs: ProductReviewDetailError, rhs: ProductReviewDetailError) -> Bool {
    switch (lhs, rhs) {
    case (.notFound, .notFound):
      return true
    case let (.unknown(lhsError), .unknown(rhsError)):
      return lhsError.localizedDescription == rhsError.localizedDescription
    default:
      return false
    }
  }

  case notFound
  case unknown(Error)
}
