import AsyncAlgorithms
import Combine
import Foundation
import NativeAPI
import OGAppKitSDK
import OGRouter

typealias OGProductReviewDetailComponent = Skie.com_ottogroup_ogappkit_nativeui__api.ProductReviewsComponent.__Sealed
typealias OGNativeApiTenant = __Bridge__OGNativeConfig_NativeApiTenant
typealias OGProductReviewsScreen = SkieSwiftFlow<OGResult<ProductReviewsScreen>>
typealias OGSortingOption = __Bridge__ReviewsSortingOptions_SortingOption

// MARK: - ProductReviewDetailServing

protocol ProductReviewDetailServing {
  func filterProductReviews(screenId: String, filterRating: Int?) async
  func sortProductReviews(screenId: String, sortingOption: OGSortingOption) async
  func showMoreReviews(screenId: String) async
  func fetchProductReview(id: String, componentConfigsJson: String) async
  var successStream: AsyncChannel<[OGProductReviewDetailComponent]> { get async }
  var errorStream: AsyncChannel<Error> { get async }
}

// MARK: - ProductReviewDetailService

actor ProductReviewDetailService: ProductReviewDetailServing {
  private let ogNative: OGNative
  private var productIdTask: Task<Void, Never>?

  let successStream: AsyncChannel<[OGProductReviewDetailComponent]> = .init()
  let errorStream: AsyncChannel<Error> = .init()

  init(
    ogNative: OGNative = NativeAPIContainer.shared.nativeAPI()
  ) {
    self.ogNative = ogNative
  }

  func filterProductReviews(screenId: String, filterRating: Int?) async {
    if let filterRating {
      ogNative.filterProductReviews(screenId: screenId, filterRating: KotlinInt(int: Int32(filterRating)))
    } else {
      ogNative.filterProductReviews(screenId: screenId, filterRating: nil)
    }
  }

  func sortProductReviews(screenId: String, sortingOption: OGSortingOption) async {
    ogNative.sortProductReviews(screenId: screenId, sortingOption: sortingOption)
  }

  func showMoreReviews(screenId: String) async {
    ogNative.showMoreReviews(screenId: screenId)
  }

  func fetchProductReview(id: String, componentConfigsJson: String) async {
    productIdTask?.cancel()
    let screen = ogNative.getProductReviewsScreen(
      id: id,
      componentConfigsJson: componentConfigsJson
    )
    productIdTask = observe(detailScreen: screen)
  }

  private func observe(detailScreen: OGProductReviewsScreen) -> Task<Void, Never> {
    Task.detached { [weak self] in
      for await screenResult in detailScreen {
        guard let self else { return }
        switch onEnum(of: screenResult) {
        case let .failure(error):
          await self.errorStream.send(error.failure.asError())
        case let .success(screen):
          let newComponents = screen.value.components.map { onEnum(of: $0) }
          await self.successStream.send(newComponents)
        }
      }
    }
  }

  deinit {
    productIdTask?.cancel()
  }
}
