import OGAppKitSDK
import OGCore
import OGRouter
import SwiftUI

// MARK: - ProductReviewDetailDestinationProvider

public struct ProductReviewDetailDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.productReviewDetail
  }

  public func provide(_ route: OGRoute) -> some SwiftUI.View {
    ProductReviewDetailView(route: route)
  }
}

extension OGRoute {
  public static let productReviewDetail = OGRoute(OGIdentifier.productReviewDetail.value)
}
