// Generated using OGFeatureAdapterMaker
import OGDIService

public final class ProductReviewDetailFeatureAdapterContainer: OGDISharedContainer {

  public static var shared: ProductReviewDetailFeatureAdapterContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var productReviewDetail: OGDIService<ProductReviewDetailFeatureAdaptable> {
    self {
      ProductReviewDetailFeatureAdapter(configuration: nil)
    }.shared
  }
}
