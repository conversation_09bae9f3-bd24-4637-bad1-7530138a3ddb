// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros


public protocol ProductReviewDetailFeatureConfigurable {
  var isEnabled: Bool { get set }
  var components: String { get set }
}

struct ProductReviewDetailFeatureConfig: ProductReviewDetailFeatureConfigurable {
  public var isEnabled: Bool = false
  public var components: String = ""
}

struct ProductReviewDetailComponents: Codable {
  public let components: [AnyJSONType]
}

public final class ProductReviewDetailFeatureAdapter: OGFeatureAdapter, ProductReviewDetailFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.productReviewDetail.value }

  public let configuration: CurrentValueSubject<ProductReviewDetailFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: ProductReviewDetailFeatureConfigurable?) {
  
    self.configuration = CurrentValueSubject(configuration ?? ProductReviewDetailFeatureConfig())
    super.init()

    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let self = self else { return }
      var updatedConfiguration = self.configuration.value
      guard let feature = feature else {
        updatedConfiguration.isEnabled = false
        self.configuration.send(updatedConfiguration)
        return
      }
      updatedConfiguration.isEnabled = feature.isEnabled

      do {
        let componentModel = try feature.customValue([AnyJSONType].self, for: OGFeatureKey.CustomValues.ProductReviewDetail.components) ?? []
        let productReviewDetailComponents = ProductReviewDetailComponents(components: componentModel)
        let jsonData = try JSONEncoder().encode(productReviewDetailComponents)
        updatedConfiguration.components = String(data: jsonData, encoding: .utf8) ?? updatedConfiguration.components
      } catch {
        logger.log(.critical, domain: .service, message: error.localizedDescription)
      }

      self.configuration.send(updatedConfiguration)
      }.store(in: &subscriptions)
  }
}

public protocol ProductReviewDetailFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<ProductReviewDetailFeatureConfigurable, Never> { get }
}


extension OGFeatureKey.CustomValues {
  public enum ProductReviewDetail: String, OGKeyReceivable {
      public var value: String  {
        rawValue
      }

      case components = "components"
    }
}


extension OGIdentifier {
  public static let productReviewDetail = #identifier("productReviewDetail")
}
