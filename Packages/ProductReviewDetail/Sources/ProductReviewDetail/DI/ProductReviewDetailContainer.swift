import OGAppKitSDK
import OGDIService
import OGDomainStore
import UICatalog

public final class ProductReviewDetailContainer: OGDISharedContainer {
  public static var shared: ProductReviewDetailContainer = .init()
  public var manager: OGDIContainerManager = .init()

  var service: OGDIService<ProductReviewDetailServing> {
    self {
      ProductReviewDetailService()
    }.unique
  }

  var domainStore: OGDIService<ProductReviewDetailStore> {
    self {
      OGDomainStoreFactory.make(productId: "")
    }.shared
  }

  public var buttonStyleResolver: OGDIService<ButtonStyleFactory> {
    self {
      ButtonStyleFactory()
    }.shared
  }
}
