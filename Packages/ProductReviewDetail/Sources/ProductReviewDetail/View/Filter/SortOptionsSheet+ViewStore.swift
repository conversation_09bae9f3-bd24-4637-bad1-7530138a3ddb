import Combine
import OGAppKitSDK
import OGViewStore

extension SortOptionsSheet {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(screenId: String) -> Store {
    Store(
      initialState: .init(screenId: screenId),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(),
      connector: ViewState.Connector()
    )
  }
}

// MARK: - ViewState

extension SortOptionsSheet {
  struct ViewState: OGViewState {
    private(set) var selectedSortingOption: OGSortingOption = .recent
    private let screenId: String
    init(screenId: String = "") {
      self.screenId = screenId
    }

    mutating func update(selectedSortingOption: OGSortingOption) {
      self.selectedSortingOption = selectedSortingOption
    }

    static let initial = ViewState()
  }

  // MARK: - Event

  enum Event: OGViewEvent {
    case selectedSortingOption(option: OGSortingOption)
    /// Private Events
    case _receivedSortingOption(option: OGSortingOption)
  }
}

// MARK: - Reducer

extension SortOptionsSheet.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout SortOptionsSheet.ViewState,
      with event: SortOptionsSheet.Event
    ) {
      switch event {
      case let ._receivedSortingOption(option):
        state.update(selectedSortingOption: option)
      case .selectedSortingOption:
        break
      }
    }
  }

  // MARK: - Middleware

  struct Middleware: OGViewStoreMiddleware {
    let productReviewDetailStore: ProductReviewDetailStore
    init(productReviewDetailStore: ProductReviewDetailStore = ProductReviewDetailContainer.shared.domainStore()) {
      self.productReviewDetailStore = productReviewDetailStore
    }

    func callAsFunction(
      event: SortOptionsSheet.Event,
      for state: SortOptionsSheet.ViewState
    ) async -> SortOptionsSheet.Event? {
      switch event {
      case let .selectedSortingOption(option):
        productReviewDetailStore.dispatchDetached(ProductReviewDetailAction.sortReviews(state.screenId, option))
        return nil
      case ._receivedSortingOption:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    let productReviewDetailStore: ProductReviewDetailStore

    init(
      productReviewDetailStore: ProductReviewDetailStore = ProductReviewDetailContainer.shared.domainStore()
    ) {
      self.productReviewDetailStore = productReviewDetailStore
    }

    func configure(
      dispatch: @escaping (SortOptionsSheet.Event) async -> Void
    ) async {
      await productReviewDetailStore
        .statePublisher
        .removeDuplicates()
        .map(\.currentSortOption)
        .sink { currentSortOption in
          Task {
            await dispatch(._receivedSortingOption(option: currentSortOption))
          }
        }
        .store(in: &cancellables)
    }
  }
}
