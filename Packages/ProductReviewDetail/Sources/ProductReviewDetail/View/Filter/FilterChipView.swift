import OGL10n
import SwiftUI
import UICatalog

struct FilterChipView: View {
  let text: String
  let onClose: () -> Void

  var body: some View {
    HStack(spacing: UILayoutConstants.ProductReview.smallSpacing) {
      Text(text)
        .font(for: .caption)
        .accessibilityAddTraits(.isHeader)
        .dynamicTypeSize(...DynamicTypeSize.xLarge)

      ProductDetailCloseButton(accessibilityLabel: ogL10n.General.Close, dropShadow: false) {
        onClose()
      }
    }
    .padding(.horizontal, UILayoutConstants.ProductReview.filterChipHorizontalPadding)
    .padding(.vertical, UILayoutConstants.ProductReview.filterChipVerticalPadding)
    .background(
      Capsule()
        .fill(OGColors.backgroundBackground0.color)
    )
    .overlay(
      Capsule()
        .stroke(OGColors.backgroundBackground100.color, lineWidth: 1)
    )
  }
}
