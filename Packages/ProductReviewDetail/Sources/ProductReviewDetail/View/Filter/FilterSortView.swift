import OGAppKitSDK
import OGDIService
import OGL10n
import Swift<PERSON>
import UICatalog

// MARK: - FilterSortView

struct FilterSortView: SwiftUI.View {
  @StateObject private var viewStore: Store
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  private let onSortTapped: () -> Void
  init(screenId: String, onSortTapped: @escaping () -> Void) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(screenId: screenId))
    self.onSortTapped = onSortTapped
  }

  var body: some SwiftUI.View {
    AdaptiveHStack(alignment: .firstTextBaseline, spacing: UILayoutConstants.Default.padding) {
      Spacer()
      Text(ogL10n.ProductReview.Rating.SortByLabel)
        .font(for: .titleS)
        .foregroundColor(OGColors.textOnLight.color)
      button
    }
    .accessibilityElement(children: .combine)
  }

  private var button: some SwiftUI.View {
    Button {
      onSortTapped()
    } label: {
      HStack {
        Text(viewStore.selectedSortingOption.title)
          .font(for: OGFonts.copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
        OGImages.icon16x16ChevronDownPrimary.image
          .accessibilityHidden(true)
      }
      .padding(.horizontal, UILayoutConstants.FilterSortView.innerButtonPaddingHorizontal)
      .frame(minHeight: UILayoutConstants.FilterSortView.buttonHeight)
      .background(OGColors.backgroundBackground0.color)
      .cornerRadius(cornerRadius.full)
      .overlay(
        RoundedRectangle(cornerRadius: cornerRadius.full)
          .stroke(OGColors.backgroundBackground100.color, lineWidth: UILayoutConstants.FilterSortView.borderWidth)
      )
    }
    .frame(minHeight: UILayoutConstants.FilterSortView.buttonHeight)
  }
}

// MARK: - UILayoutConstants.FilterSortView

extension UILayoutConstants {
  enum FilterSortView {
    static let innerButtonPaddingHorizontal: CGFloat = 16
    static let borderWidth: CGFloat = 1
    static let buttonHeight: CGFloat = 32.0
  }
}
