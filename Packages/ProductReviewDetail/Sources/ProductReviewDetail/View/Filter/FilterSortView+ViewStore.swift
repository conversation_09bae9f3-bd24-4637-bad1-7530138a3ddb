import Combine
import OGAppKitSDK
import OGViewStore

extension FilterSortView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(screenId: String) -> Store {
    Store(
      initialState: .init(screenId: screenId),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(),
      connector: ViewState.Connector()
    )
  }
}

// MARK: - ViewState

extension FilterSortView {
  struct ViewState: OGViewState {
    private(set) var selectedRatingFilter: Int?
    private(set) var selectedSortingOption: OGSortingOption = .recent
    private let screenId: String
    init(screenId: String = "") {
      self.screenId = screenId
    }

    mutating func update(selectedSortingOption: OGSortingOption) {
      self.selectedSortingOption = selectedSortingOption
    }

    mutating func update(selectedRatingFilter: Int?) {
      self.selectedRatingFilter = selectedRatingFilter
    }

    static let initial = ViewState()
  }

  // MARK: - Event

  enum Event: OGViewEvent {
    case clearFilterRatingTapped
    /// Private Events
    case _receivedFilterRating(rating: Int?)
    case _receivedSortingOption(option: OGSortingOption)
  }
}

// MARK: - Reducer

extension FilterSortView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout FilterSortView.ViewState,
      with event: FilterSortView.Event
    ) {
      switch event {
      case let ._receivedFilterRating(rating):
        state.update(selectedRatingFilter: rating)
      case .clearFilterRatingTapped:
        state.update(selectedRatingFilter: nil)
      case let ._receivedSortingOption(option):
        state.update(selectedSortingOption: option)
      }
    }
  }

  // MARK: - Middleware

  struct Middleware: OGViewStoreMiddleware {
    let productReviewDetailStore: ProductReviewDetailStore
    init(productReviewDetailStore: ProductReviewDetailStore = ProductReviewDetailContainer.shared.domainStore()) {
      self.productReviewDetailStore = productReviewDetailStore
    }

    func callAsFunction(
      event: FilterSortView.Event,
      for state: FilterSortView.ViewState
    ) async -> FilterSortView.Event? {
      switch event {
      case .clearFilterRatingTapped:
        productReviewDetailStore.dispatchDetached(ProductReviewDetailAction.filterReviews(state.screenId, nil))
        return nil
      case ._receivedFilterRating, ._receivedSortingOption:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    let productReviewDetailStore: ProductReviewDetailStore

    init(
      productReviewDetailStore: ProductReviewDetailStore = ProductReviewDetailContainer.shared.domainStore()
    ) {
      self.productReviewDetailStore = productReviewDetailStore
    }

    func configure(
      dispatch: @escaping (FilterSortView.Event) async -> Void
    ) async {
      await productReviewDetailStore
        .statePublisher
        .removeDuplicates()
        .map(\.currentFilterRating)
        .sink { currentFilterRating in
          Task {
            await dispatch(._receivedFilterRating(rating: currentFilterRating))
          }
        }
        .store(in: &cancellables)

      await productReviewDetailStore
        .statePublisher
        .removeDuplicates()
        .map(\.currentSortOption)
        .sink { currentSortOption in
          Task {
            await dispatch(._receivedSortingOption(option: currentSortOption))
          }
        }
        .store(in: &cancellables)
    }
  }
}
