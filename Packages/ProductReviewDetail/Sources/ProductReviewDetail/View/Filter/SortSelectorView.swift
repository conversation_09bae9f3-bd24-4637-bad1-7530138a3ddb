import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

struct SortSelectorView: SwiftUI.View {
  let sortOption: OGSortingOption
  let onSortTapped: () -> Void

  var body: some SwiftUI.View {
    AdaptiveHStack(spacing: UILayoutConstants.ProductReview.smallSpacing) {
      Text(ogL10n.ProductReview.Rating.SortByLabel())
        .foregroundStyle(OGColors.textOnLight.color)
        .font(for: .titleS)
      HStack(spacing: UILayoutConstants.ProductReview.smallSpacing) {
        Text(sortOption.title)
          .font(for: .copyS)
          .foregroundStyle(OGColors.textOnLight.color)
        OGImages.icon16x16ChevronDownPrimary.image
          .accessibilityHidden(true)
      }
      .onTapGesture(perform: onSortTapped)
    }
    .accessibilityElement(children: .combine)
    .accessibilityAddTraits(.isButton)
  }
}
