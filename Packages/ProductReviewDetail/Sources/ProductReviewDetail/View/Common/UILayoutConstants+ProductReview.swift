import UICatalog
import UIKit

extension UILayoutConstants {
  enum ProductReview {
    static let verticalSpacing: CGFloat = 16
    static let horizontalSpacing: CGFloat = 12
    static let smallSpacing: CGFloat = 7
    static let ratingBarHeight: CGFloat = 8
    static let cornerRadius: CGFloat = 24
    static let filterChipHorizontalPadding: CGFloat = 14
    static let filterChipVerticalPadding: CGFloat = 3
    static let ratingLabelWidth: CGFloat = 60
    static let reviewCountWidth: CGFloat = 30
    static let screenPadding: CGFloat = 32
    static let cardSpacing: CGFloat = 8
    static let cardTextPadding: CGFloat = 36
    static let cardMinHeight: CGFloat = 100
    static let headerSpacing: CGFloat = 8
    static let buttonHorizontalPadding: CGFloat = 16
    static let buttonVerticalPadding: CGFloat = 4
    static let rowSpacing: CGFloat = 10
    static let rowPadding: CGFloat = 16
  }
}
