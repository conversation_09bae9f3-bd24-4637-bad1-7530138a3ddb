import Combine
import OGAppKitSDK
import OGL10n
import OGViewStore

extension PaginationView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(
    displayedCount: Int,
    totalReviews: Int,
    screenId: String
  ) -> Store {
    Store(
      initialState: .init(
        screenId: screenId,
        displayedCount: displayedCount,
        totalReviews: totalReviews
      ),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware()
    )
  }
}

// MARK: - ViewState

extension PaginationView {
  struct ViewState: OGViewState {
    let displayedCount: Int
    let totalReviews: Int
    var displayText: String {
      ogL10n.ProductDetail.Review.Pagination.Copy(count: String(displayedCount), max: String(totalReviews))
    }

    private let screenId: String
    init(
      screenId: String = "",
      displayedCount: Int = 0,
      totalReviews: Int = 0
    ) {
      self.screenId = screenId
      self.displayedCount = displayedCount
      self.totalReviews = totalReviews
    }

    static let initial = ViewState()
  }

  // MARK: - Event

  enum Event: OGViewEvent {
    case loadMoreTapped
  }
}

// MARK: - Reducer

extension PaginationView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout PaginationView.ViewState,
      with event: PaginationView.Event
    ) {
      switch event {
      case .loadMoreTapped:
        break
      }
    }
  }

  // MARK: - Middleware

  struct Middleware: OGViewStoreMiddleware {
    let productReviewDetailStore: ProductReviewDetailStore
    init(productReviewDetailStore: ProductReviewDetailStore = ProductReviewDetailContainer.shared.domainStore()) {
      self.productReviewDetailStore = productReviewDetailStore
    }

    func callAsFunction(
      event: PaginationView.Event,
      for state: PaginationView.ViewState
    ) async -> PaginationView.Event? {
      switch event {
      case .loadMoreTapped:
        productReviewDetailStore.dispatchDetached(ProductReviewDetailAction.loadMoreReviews(state.screenId))
        return nil
      }
    }
  }
}
