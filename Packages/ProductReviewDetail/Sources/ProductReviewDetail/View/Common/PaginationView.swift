import OGL10n
import SwiftUI
import UICatalog

struct PaginationView: View {
  @StateObject private var viewStore: Store
  let displayedCount: Int
  let totalReviews: Int
  @Environment(\.screenSize) var screenSize
  init(
    displayedCount: Int,
    totalReviews: Int,
    screenId: String
  ) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(displayedCount: displayedCount, totalReviews: totalReviews, screenId: screenId))
    self.displayedCount = displayedCount
    self.totalReviews = totalReviews
  }

  var body: some View {
    VStack(spacing: .zero) {
      ProgressView(value: CGFloat(displayedCount), total: CGFloat(totalReviews))
        .frame(width: screenSize.width / 2, height: 2)
        .tint(OGColors.primaryPrimary100.color)
        .background(OGColors.primaryPrimary50.color)
        .padding(.bottom, UILayoutConstants.Default.padding2x)
        .accessibilityHidden(true)

      Text(viewStore.displayText)
        .font(for: .buttonLabelS)
        .foregroundColor(OGColors.textOnLight.color)
        .accessibilityHidden(true)
        .padding(.bottom, UILayoutConstants.Default.padding3x)
      Button {
        Task {
          await viewStore.dispatch(.loadMoreTapped)
        }

      } label: {
        Text(ogL10n.ProductReview.Pagination.ShowMore)
          .font(for: .buttonLabelS)
          .padding(.horizontal, UILayoutConstants.ProductReview.buttonHorizontalPadding)
          .padding(.vertical, UILayoutConstants.ProductReview.buttonVerticalPadding)
          .overlay(
            RoundedRectangle(cornerRadius: UILayoutConstants.ProductReview.cornerRadius)
              .stroke(OGColors.primaryPrimary100.color, lineWidth: 1)
          )
      }
      .padding(.bottom, UILayoutConstants.Default.padding3x)
      .accessibilityLabel(ogL10n.ProductDetail.Review.Pagination.Accessibility(count: String(displayedCount), max: String(totalReviews)))
    }
  }
}
