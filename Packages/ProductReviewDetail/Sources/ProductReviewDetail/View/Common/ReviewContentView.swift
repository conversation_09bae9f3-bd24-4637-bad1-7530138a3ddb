import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ReviewContentView

struct ReviewContentView: SwiftUI.View {
  let reviews: [Review]
  let expandedReviewIndex: Int?
  var body: some SwiftUI.View {
    VStack(spacing: UILayoutConstants.ProductReview.verticalSpacing) {
      ForEach(Array(reviews.enumerated()), id: \.offset) { index, review in
        var accessibilityLabel: String {
          var rating = ogL10n.ProductDetail.Review.Rating.Accessibility(rating: String(review.rating), date: review.formattedDate)
          if let reviewerName = review.reviewerName {
            rating += " " + ogL10n.ProductDetail.Review.ReviewerName.Accessibility(reviewerName: reviewerName)
          }
          rating += " \(review.title ?? "") \(review.text)"
          return rating
        }
        ProductReviewCardView(
          review: review,
          isExpanded: expandedReviewIndex == index,
          minHeight: .zero
        )
        .id(index)
        .accessibilityElement(children: .ignore)
        .accessibilityLabel(accessibilityLabel)
      }
    }
  }
}

// MARK: - UILayoutConstants.ReviewContentView

extension UILayoutConstants {
  enum ReviewContentView {
    static let minHeight: CGFloat = 168
  }
}
