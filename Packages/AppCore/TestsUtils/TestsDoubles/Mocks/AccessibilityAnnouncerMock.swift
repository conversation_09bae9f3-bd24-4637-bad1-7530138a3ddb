import Foundation
import OGKit
import XCTest

@testable import AppCore

@OGMock
public final class AccessibilityAnnouncerMock: AccessibilityAnnouncing {
  public init() {}

  public func announce(_ message: String) {
    mock.announce(message)
  }

  public func announceAndDelay(_ message: String, config: AppCore.AnnouncementConfig) async {
    await mock.announceAndDelay(message, config: config)
  }

  public func announceScreenChange() {
    mock.announceScreenChange()
  }

  public func announceScreenAppearance(message: String, config: AnnouncementConfig) {
    mock.announceScreenAppearance(message: message, config: config)
  }
}
