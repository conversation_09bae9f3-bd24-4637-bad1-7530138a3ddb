import AppCore
import UIKit
import XCTest

// MARK: - Test Doubles

public final class NotificationPosterMock {
  public var postedNotifications: [(notification: UIAccessibility.Notification, argument: Any?)] = []
  public var expectation: XCTestExpectation?
  public var expectedNotificationCount: Int = 0

  public init(postedNotifications: [(notification: UIAccessibility.Notification, argument: Any?)]) {
    self.postedNotifications = postedNotifications
  }

  public convenience init() {
    self.init(postedNotifications: [])
  }

  public convenience init(expectation: XCTestExpectation, expectedNotificationCount: Int) {
    self.init()
    self.expectation = expectation
    self.expectedNotificationCount = expectedNotificationCount
  }

  public func reset() {
    postedNotifications = []
  }

  @MainActor
  public func post(notification: UIAccessibility.Notification, argument: Any?) {
    postedNotifications.append((notification: notification, argument: argument))

    if let expectation, postedNotifications.count == expectedNotificationCount {
      expectation.fulfill()
    }
  }
}
