import AppCoreTestsUtils
import OGCoreTestsUtils
import XCTest

@testable import AppCore

final class MotionSettingSheetStoreConnectorTests: XCTestCase {
  func test_WHEN_appStore_updates_THEN_dispatch_didAppear() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected setCampaign event to be received")

      let appStore = AppStoreMock()
      let sut = MotionSettingSheet.Connector(appStore: appStore)
      var actualEvents: [MotionSettingSheet.Event] = []

      let dispatch: (MotionSettingSheet.Event) -> Void = { event in
        actualEvents.append(event)
        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      await fulfillment(of: [expectation], timeout: 1)
      XCTAssertEqual(expectation.expectedFulfillmentCount, 1)
      XCTAssertEqual([._didAppear(wasMotionSheetShown: false)], actualEvents)
    }
  }
}
