import AppCoreTestsUtils
import OGUserCore
import XCTest

@testable import AppCore

final class MotionSettingSheetStoreMiddlewareTests: XCTestCase {
  func test_WHEN_didAppear_THEN_setWasMotionAlertShown_true_and_dispatch_setMotionSheetShown() async throws {
    var userPreferences = UserPreferences.initial

    let appStore = AppStoreMock()
    let sut = MotionSettingSheet.Middleware(appStore: appStore)
    let nextEvent = await sut.callAsFunction(event: ._didAppear(wasMotionSheetShown: true), for: .initial)
    appStore.dispatchDetached(AppAction.userPreferences(.setMotionSheetShown(true)))

    XCTAssertEqual(
      appStore.dispatchDetachedArg,
      AppAction.userPreferences(
        UserPreferencesAction.setMotionSheetShown(true)
      )
    )
    XCTAssertEqual(appStore.dispatchDetachedCallCount, 1)

    XCTAssertNil(nextEvent)
  }
}
