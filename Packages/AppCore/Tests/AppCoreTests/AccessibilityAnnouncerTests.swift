import AppCoreTestsUtils
import XCTest

@testable import AppCore

final class AccessibilityAnnouncerTests: XCTestCase {
  // MARK: - Properties

  private var mockNotificationPoster: NotificationPosterMock!
  private var voiceOverRunning: Bool!
  private var sut: AccessibilityAnnouncer!

  // MARK: - Setup & Teardown

  override func setUp() {
    super.setUp()
    mockNotificationPoster = NotificationPosterMock()
    mockNotificationPoster.reset()
    voiceOverRunning = true
    sut = AccessibilityAnnouncer(
      postNotification: mockNotificationPoster.post(notification:argument:),
      isVoiceOverRunning: { self.voiceOverRunning }
    )
  }

  override func tearDown() {
    mockNotificationPoster = nil
    voiceOverRunning = nil
    sut = nil
    super.tearDown()
  }

  // MARK: - Basic Announcement Tests

  @MainActor
  func testAnnounce_AlwaysPostsNotification_RegardlessOfVoiceOverState() {
    voiceOverRunning = true
    mockNotificationPoster.reset()

    sut.announce("Message with VoiceOver on")

    XCTAssertEqual(mockNotificationPoster.postedNotifications.count, 1)
    XCTAssertEqual(mockNotificationPoster.postedNotifications.first?.notification, .announcement)

    voiceOverRunning = false
    mockNotificationPoster.reset()

    sut.announce("Message with VoiceOver off")

    XCTAssertEqual(mockNotificationPoster.postedNotifications.count, 1)
    XCTAssertEqual(mockNotificationPoster.postedNotifications.first?.notification, .announcement)
  }

  // MARK: - Screen Appearance Tests - VoiceOver State

  @MainActor
  func test_WHEN_announceScreenChangeCalledWithVoiceOverOn_THEN_bothNotificationsPosted() async {
    voiceOverRunning = true
    let title = "Screen Title"
    let exp = expectation(description: "Both notifications posted")

    mockNotificationPoster = NotificationPosterMock(expectation: exp, expectedNotificationCount: 2)

    sut = AccessibilityAnnouncer(
      postNotification: mockNotificationPoster.post(notification:argument:),
      isVoiceOverRunning: { self.voiceOverRunning }
    )

    sut.announceScreenAppearance(message: title, config: .standard)

    await fulfillment(of: [exp], timeout: 1.0)

    XCTAssertEqual(mockNotificationPoster.postedNotifications.count, 2)

    let screenChangedNotification = mockNotificationPoster.postedNotifications.first
    XCTAssertEqual(screenChangedNotification?.notification, .screenChanged)
    XCTAssertNil(screenChangedNotification?.argument)

    let announcementNotification = mockNotificationPoster.postedNotifications.last
    XCTAssertEqual(announcementNotification?.notification, .announcement)
    XCTAssertEqual(announcementNotification?.argument as? String, title)
  }

  @MainActor
  func testannounceScreenChange_WhenVoiceOverNotRunning_DoesNothing() {
    voiceOverRunning = false
    mockNotificationPoster.reset()

    sut.announceScreenAppearance(message: "Screen Title", config: .standard)

    XCTAssertEqual(mockNotificationPoster.postedNotifications.count, 0)
  }

  // MARK: - VoiceOver State Change Tests

  @MainActor
  func testVoiceOverStateChange_AffectsAnnouncementBehavior() {
    voiceOverRunning = true
    mockNotificationPoster.reset()

    sut.announceScreenAppearance(message: "Screen with VoiceOver on", config: .standard)

    voiceOverRunning = false
    mockNotificationPoster.reset()

    sut.announceScreenAppearance(message: "Screen with VoiceOver off", config: .standard)

    XCTAssertEqual(mockNotificationPoster.postedNotifications.count, 0)
  }

  // MARK: - Edge Cases

  @MainActor
  func testAnnounceEmptyString_StillPostsNotification() {
    let emptyMessage = ""
    mockNotificationPoster.reset()

    sut.announce(emptyMessage)

    XCTAssertEqual(mockNotificationPoster.postedNotifications.count, 1)
    XCTAssertEqual(mockNotificationPoster.postedNotifications.first?.argument as? String, emptyMessage)
  }
}
