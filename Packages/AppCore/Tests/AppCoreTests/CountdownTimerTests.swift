import XCTest

@testable import AppCore

final class CountdownTimerTests: XCTestCase {
  func test_WHEN_startTimer_THEN_isNotFinished() async {
    let timer = CountdownTimer(count: 3)
    await timer.start()

    var receivedValues: [Int] = []
    let streamTask = Task {
      for await remainingTime in await timer.timeRemainingStream {
        receivedValues.append(remainingTime)
        if remainingTime == 0 {
          break
        }
      }
    }

    await streamTask.value
    XCTAssertEqual(receivedValues, [2, 1, 0])
    await timer.stop()
  }

  func test_WHEN_resetTimer_THEN_timeRemainingIsReset() async {
    let timer = CountdownTimer(count: 5)
    await timer.start()
    try? await Task.sleep(nanoseconds: 1)
    await timer.reset()

    var receivedValues: [Int] = []
    let streamTask = Task {
      for await remainingTime in await timer.timeRemainingStream {
        receivedValues.append(remainingTime)
        if remainingTime == 5 {
          break
        }
      }
    }

    await streamTask.value
    XCTAssertEqual(receivedValues.last, 5)
  }

  func test_WHEN_restartTimer_THEN_timeRemainingResetsAndStarts() async {
    let timer = CountdownTimer(count: 5)
    await timer.start()
    try? await Task.sleep(nanoseconds: 1)
    await timer.restart()

    var receivedValues: [Int] = []
    let streamTask = Task {
      for await remainingTime in await timer.timeRemainingStream {
        receivedValues.append(remainingTime)
        if remainingTime == 4 {
          break
        }
      }
    }

    await streamTask.value
    XCTAssertEqual(receivedValues.last, 4)
  }

  func test_WHEN_timerCompletes_THEN_isRunning() async {
    let exp = expectation(description: "Timer completed")

    let timer = CountdownTimer(count: 2)
    await timer.start()

    var receivedValues: [Int] = []
    let streamTask = Task {
      for await remainingTime in await timer.timeRemainingStream {
        receivedValues.append(remainingTime)
        if remainingTime == 0 {
          exp.fulfill()
          break
        }
      }
    }

    await fulfillment(of: [exp], timeout: 2.1)

    await streamTask.value
    XCTAssertEqual(receivedValues, [1, 0])
  }
}
