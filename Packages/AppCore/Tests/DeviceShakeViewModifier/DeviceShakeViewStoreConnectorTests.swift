import AppCoreTestsUtils
import OGCoreTestsUtils
import XCTest

@testable import AppCore

// MARK: - DeviceShakeViewStoreConnectorTests

final class DeviceShakeViewStoreConnectorTests: XCTestCase {
  func test_WHEN_appStore_updates_THEN_dispatch_setMotionEnabled() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected setCampaign event to be received")
      let appstore = AppStoreMock()
      let sut = DeviceShakeViewModifier.Connector(appStore: appstore)
      var actualEvents: [DeviceShakeViewModifier.Event] = []

      let dispatch: (DeviceShakeViewModifier.Event) -> Void = { event in
        actualEvents.append(event)
        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      await fulfillment(of: [expectation], timeout: 1)
      XCTAssertEqual(expectation.expectedFulfillmentCount, 1)
      XCTAssertEqual(
        [._setMotionEnabled(true)],
        actualEvents
      )
    }
  }
}
