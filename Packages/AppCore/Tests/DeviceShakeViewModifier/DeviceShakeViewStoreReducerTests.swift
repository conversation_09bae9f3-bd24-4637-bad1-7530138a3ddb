import OGCore
import XCTest

@testable import AppCore

// MARK: - OGKitAppViewStoreReducerTests

final class OGKitAppViewStoreReducerTests: XCTestCase {
  func test_WHEN_setMotionEnabled_THEN_update_state() async throws {
    var state = DeviceShakeViewModifier.ViewState.initial
    DeviceShakeViewModifier.Reducer.reduce(&state, with: ._setMotionEnabled(false))

    XCTAssertEqual(state, .init(isMotionEnabled: false))
  }
}
