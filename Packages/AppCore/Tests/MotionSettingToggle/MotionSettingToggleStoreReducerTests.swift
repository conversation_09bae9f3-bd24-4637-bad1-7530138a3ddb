import AppCoreTestsUtils
import OGCoreTestsUtils
import OG<PERSON><PERSON><PERSON>ore
import XCTest

@testable import AppCore

final class MotionSettingToggleStoreReducerTests: XCTestCase {
  func test_WHEN_setMotionEnabled_THEN_update_state() {
    var state = MotionSettingToggle.ViewState.initial
    MotionSettingToggle.Reducer.reduce(&state, with: ._setMotionEnabled(true))

    XCTAssertEqual(state, .init(isMotionEnabled: true))
  }

  func test_WHEN_didToggleMotion_THEN_no_state_update() {
    var state = MotionSettingToggle.ViewState.initial
    MotionSettingToggle.Reducer.reduce(&state, with: .didToggleMotion(true))

    XCTAssertEqual(state, .initial)
  }
}
