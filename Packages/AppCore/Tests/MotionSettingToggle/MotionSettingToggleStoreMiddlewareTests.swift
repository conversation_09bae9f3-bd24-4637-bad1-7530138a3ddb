import AppCoreTestsUtils
import OGCoreTestsUtils
import OGUser<PERSON>ore
import XCTest

@testable import AppCore

final class MotionSettingToggleStoreMiddlewareTests: XCTestCase {
  func test_WHEN_didToggleMotion_true_THEN_dispatchDetached_updatePreferences_isMotionEnabled_true_and_return_nil() async throws {
    let appStore = AppStoreMock()
    let sut = MotionSettingToggle.Middleware(appStore: appStore)
    let nextEvent = await sut.callAsFunction(
      event: .didToggleMotion(true),
      for: .initial
    )

    XCTAssertEqual(
      appStore.dispatchDetachedArg,
      AppAction.userPreferences(UserPreferencesAction.setMotionEnabled(true))
    )
    XCTAssertEqual(appStore.dispatchDetachedCallCount, 1)

    XCTAssertNil(nextEvent)
  }

  func test_WHEN_setMotionEnabled_THEN_return_nil() async throws {
    let sut = MotionSettingToggle.Middleware()
    let nextEvent = await sut.callAsFunction(event: ._setMotionEnabled(true), for: .initial)

    XCTAssertNil(nextEvent)
  }
}
