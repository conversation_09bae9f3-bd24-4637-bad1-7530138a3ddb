import AppCoreTestsUtils
import OGCoreTestsUtils
import OGUser<PERSON>ore
import XCTest

@testable import AppCore

final class MotionSettingToggleStoreConnectorTests: XCTestCase {
  func test_WHEN_appStore_updates_THEN_dispatch_setUserPreferences() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected setCampaign event to be received")

      let appStore = AppStoreMock()
      let sut = MotionSettingToggle.Connector(appStore: appStore)
      var actualEvents: [MotionSettingToggle.Event] = []

      let dispatch: (MotionSettingToggle.Event) -> Void = { event in
        actualEvents.append(event)
        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      await fulfillment(of: [expectation], timeout: 1)
      XCTAssertEqual(expectation.expectedFulfillmentCount, 1)
      XCTAssertEqual([._setMotionEnabled(true)], actualEvents)
    }
  }
}
