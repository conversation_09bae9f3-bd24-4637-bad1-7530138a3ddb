// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "AppCore",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "AppCore",
      targets: ["AppCore"]
    ),
    .library(
      name: "AppCoreTestsUtils",
      targets: ["AppCoreTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../TenantChooserCore"),
    .package(path: "../ExternalDependencies"),
    .package(path: "../UICatalog")
  ],
  targets: [
    .target(
      name: "AppCore",
      dependencies: [
        "TenantChooserCore",
        "ExternalDependencies",
        "UICatalog"
      ],
      path: "Sources"
    ),
    .target(
      name: "AppCoreTestsUtils",
      dependencies: [
        .product(name: "TenantChooserCoreTestsUtils", package: "TenantChooserCore"),
        "AppCore"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "AppCoreTests",
      dependencies: ["AppCoreTestsUtils"],
      path: "Tests"
    )
  ]
)
