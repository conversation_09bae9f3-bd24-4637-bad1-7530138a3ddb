import SwiftUI

// MARK: - DeviceShakeViewModifier

public struct DeviceShakeViewModifier: ViewModifier {
  @StateObject private var viewStore: Self.Store

  private let action: () -> Void

  public init(action: @escaping () -> Void) {
    _viewStore = StateObject(wrappedValue: Self.make())
    self.action = action
  }

  public func body(content: Content) -> some View {
    content
      .onAppear()
      .onReceive(NotificationCenter.default.publisher(for: UIDevice.deviceDidShakeNotification)) { _ in
        guard viewStore.isMotionEnabled else { return }
        action()
      }
  }
}

extension View {
  public func onShake(perform action: @escaping () -> Void) -> some View {
    modifier(DeviceShakeViewModifier(action: action))
  }
}

extension UIDevice {
  public static let deviceDidShakeNotification = Notification.Name(rawValue: "deviceDidShakeNotification")
}

extension UIWindow {
  override open func motionEnded(_ motion: UIEvent.EventSubtype, with event: UIEvent?) {
    if motion == .motionShake {
      NotificationCenter.default.post(name: UIDevice.deviceDidShakeNotification, object: nil)
    }
  }
}
