import Combine
import <PERSON>GCore
import OGL10n
import OG<PERSON><PERSON><PERSON>ore
import <PERSON>GViewStore
import UICatalog

// MARK: - DeviceShakeViewModifier.Store

extension DeviceShakeViewModifier {
  typealias Store = OGViewStore<ViewState, Event>
}

extension DeviceShakeViewModifier {
  static func make() -> Store {
    Store(
      reducer: Reducer.reduce,
      connector: Connector()
    )
  }
}

// MARK: - DeviceShakeViewModifier.Event

extension DeviceShakeViewModifier {
  enum Event: OGViewEvent, Hashable {
    case _setMotionEnabled(Bool)
  }
}

// MARK: - DeviceShakeViewModifier.ViewState

extension DeviceShakeViewModifier {
  struct ViewState: OGViewState {
    private(set) var isMotionEnabled = true

    mutating func set(isMotionEnabled: Bool? = nil) {
      self.isMotionEnabled = isMotionEnabled ?? self.isMotionEnabled
    }

    static var initial: DeviceShakeViewModifier.ViewState = .init()
  }
}

extension DeviceShakeViewModifier {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case let ._setMotionEnabled(isEnabled):
        state.set(isMotionEnabled: isEnabled)
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let appStore: any AppStoreProtocol

    private var cancellables: Set<AnyCancellable> = []

    init(
      appStore: any AppStoreProtocol = AppContainer.shared.appStore()
    ) {
      self.appStore = appStore
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await appStore.watch(keyPath: \.userPreferencesState.isMotionEnabled)
        .sink { isMotionEnabled in
          Task {
            await dispatch(._setMotionEnabled(isMotionEnabled))
          }
        }
        .store(in: &cancellables)
    }
  }
}
