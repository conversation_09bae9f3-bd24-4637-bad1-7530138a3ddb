import SwiftUI

// MARK: - DeviceState

public final class DeviceState: ObservableObject {
  @Published public private(set) var isLandscapeOrientation: Bool

  public var isPhoneInLandsape: Bool {
    isLandscapeOrientation && UIDevice.current.userInterfaceIdiom == .phone
  }

  public init() {
    self.isLandscapeOrientation = DeviceState.isLandscapeOrientation
    setupOrientationNotification()
  }

  private func setupOrientationNotification() {
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(updateOrientation),
      name: UIDevice.orientationDidChangeNotification,
      object: nil
    )
  }

  @objc
  private func updateOrientation() {
    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
      let newValue = windowScene.interfaceOrientation.isLandscape
      if isLandscapeOrientation != newValue {
        isLandscapeOrientation = newValue
      }
    }
  }

  private static var isLandscapeOrientation: Bool {
    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
      return windowScene.interfaceOrientation.isLandscape
    }
    return false
  }

  deinit {
    NotificationCenter.default.removeObserver(self)
  }
}
