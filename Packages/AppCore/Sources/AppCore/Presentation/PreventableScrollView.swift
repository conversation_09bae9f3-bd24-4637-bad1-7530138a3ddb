import SwiftUI

public struct PreventableScrollView<Content: View>: View {
  @EnvironmentObject private var deviceState: DeviceState

  private let canScroll: Bool?
  private let content: () -> Content
  private let scrollDirection: Axis.Set

  public init(
    canScroll: Bool? = nil,
    scrollDirection: Axis.Set = .vertical,
    @ViewBuilder content: @escaping () -> Content
  ) {
    self.canScroll = canScroll
    self.scrollDirection = scrollDirection
    self.content = content
  }

  public var body: some View {
    if canScroll ?? deviceState.isLandscapeOrientation {
      ScrollView(scrollDirection, content: content)
    } else {
      content()
    }
  }
}
