import Foundation
import UIKit

// MARK: - AnnouncementConfig

public struct AnnouncementConfig: Equatable {
  let screenChangedDelay: UInt64
  let announcementDelay: UInt64

  public init(
    screenChangedDelay: UInt64,
    announcementDelay: UInt64
  ) {
    self.screenChangedDelay = screenChangedDelay
    self.announcementDelay = announcementDelay
  }

  public static let standard = AnnouncementConfig(
    screenChangedDelay: 0,
    announcementDelay: 500_000_000
  )

  public static let extended = AnnouncementConfig(
    screenChangedDelay: 400_000_000,
    announcementDelay: 10_000_000
  )
}

// MARK: - AccessibilityAnnouncing

public protocol AccessibilityAnnouncing {
  /// Announces a message to the user
  /// - Parameter message: The message to announce
  @MainActor
  func announce(_ message: String)

  /// Announces a message to the user, returning with a delay after the announcement
  /// - Parameters:
  /// - message: The message to announce
  /// - config: The timing configuration to use
  @MainActor
  func announceAndDelay(_ message: String, config: AnnouncementConfig) async

  /// Announces a screen change to the user
  @MainActor
  func announceScreenChange()

  /// Announces a screen with a title, using the specified configuration
  /// - Parameters:
  ///   - title: The announcement text to be read
  ///   - config: The timing configuration to use
  @MainActor
  func announceScreenAppearance(message: String, config: AnnouncementConfig)
}

// MARK: - AccessibilityAnnouncer

public struct AccessibilityAnnouncer: AccessibilityAnnouncing {
  private let postNotification: @MainActor (UIAccessibility.Notification, Any?) -> Void
  private let isVoiceOverRunning: () -> Bool

  public init(
    postNotification: @escaping @MainActor (UIAccessibility.Notification, Any?) -> Void = UIAccessibility.post(notification:argument:),
    isVoiceOverRunning: @escaping () -> Bool = { UIAccessibility.isVoiceOverRunning }
  ) {
    self.postNotification = postNotification
    self.isVoiceOverRunning = isVoiceOverRunning
  }

  @MainActor
  public func announce(_ message: String) {
    postNotification(.announcement, message)
  }

  @MainActor
  public func announceAndDelay(
    _ message: String,
    config: AnnouncementConfig
  ) async {
    announce(message)
    try? await Task.sleep(nanoseconds: config.announcementDelay)
  }

  @MainActor
  public func announceScreenChange() {
    postNotification(.screenChanged, nil)
  }

  @MainActor
  public func announceScreenAppearance(
    message: String,
    config: AnnouncementConfig
  ) {
    if isVoiceOverRunning() {
      Task {
        if config.screenChangedDelay > 0 {
          try? await Task.sleep(nanoseconds: config.screenChangedDelay)
        }

        announceScreenChange()

        try? await Task.sleep(nanoseconds: config.announcementDelay)

        announce(message)
      }
    }
  }
}
