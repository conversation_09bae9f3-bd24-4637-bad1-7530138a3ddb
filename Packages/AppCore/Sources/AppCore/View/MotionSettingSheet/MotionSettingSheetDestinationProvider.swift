import Foundation
import OGCore
import OGDIService
import OGRouter
import SwiftUI

// MARK: - MotionSettingSheetDestinationProvider

public struct MotionSettingSheetDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.motionSettingSheet
  }

  public func provide(_ route: OGRoute) -> some View {
    MotionSettingSheet()
  }

  public func presentationType() -> OGPresentationType {
    .sheet()
  }
}

extension OGIdentifier {
  public static let motionSettingSheet = #identifier("motionSettingSheet")
}

extension OGRoute {
  public static let motionSettingSheet = OGRoute(OGIdentifier.motionSettingSheet.value)
}
