import Combine
import OGCore
import OGL10n
import OGUserCore
import <PERSON>GViewStore

// MARK: - MotionSettingSheet.Store

extension MotionSettingSheet {
  typealias Store = OGViewStore<ViewState, Event>
}

extension MotionSettingSheet {
  static func make() -> Store {
    Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector()
    )
  }
}

// MARK: - MotionSettingSheet.Event

extension MotionSettingSheet {
  enum Event: OGViewEvent, Hashable {
    case _didAppear(wasMotionSheetShown: Bool)
  }
}

// MARK: - MotionSettingSheet.ViewState

extension MotionSettingSheet {
  struct ViewState: OGViewState {
    var titleText: String {
      ogL10n.General.MotionSetting.Sheet.Title
    }

    var copyText: String {
      ogL10n.General.MotionSetting.Sheet.Copy
    }

    static var initial: MotionSettingSheet.ViewState = .init()
  }
}

extension MotionSettingSheet {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {}
  }

  final class Middleware: OGViewStoreMiddleware {
    private let appStore: any AppStoreProtocol

    init(
      appStore: any AppStoreProtocol = AppContainer.shared.appStore()
    ) {
      self.appStore = appStore
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch event {
      case let ._didAppear(wasMotionAlertShown):
        guard !wasMotionAlertShown else { return nil }
        appStore.dispatchDetached(
          AppAction.userPreferences(
            UserPreferencesAction.setMotionSheetShown(true)
          )
        )
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let appStore: any AppStoreProtocol

    private var cancellables: Set<AnyCancellable> = []

    init(
      appStore: any AppStoreProtocol = AppContainer.shared.appStore()
    ) {
      self.appStore = appStore
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await appStore.watch(keyPath: \.userPreferencesState.wasMotionSheetShown)
        .sink { wasMotionSheetShown in
          Task {
            await dispatch(._didAppear(wasMotionSheetShown: wasMotionSheetShown))
          }
        }
        .store(in: &cancellables)
    }
  }
}
