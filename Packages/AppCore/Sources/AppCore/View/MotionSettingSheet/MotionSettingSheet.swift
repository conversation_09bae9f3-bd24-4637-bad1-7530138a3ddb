import OGL10n
import SwiftUI
import UICatalog

// MARK: - MotionSettingSheet

public struct MotionSettingSheet: View {
  @StateObject private var viewStore: Self.Store

  public init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  public var body: some View {
    BottomSheetView(title: viewStore.titleText) {
      VStack(spacing: UILayoutConstants.Default.padding2x) {
        HStack {
          Text(viewStore.copyText)
            .font(for: .copyMRegular)
            .foregroundStyle(OGColors.textOnLight.color)
          Spacer()
        }

        MotionSettingToggle()
      }
      .padding(.top, UILayoutConstants.Default.padding2x)
    }
  }
}
