import Combine
import OGCore
import OGL10n
import OGUserCore
import <PERSON>GViewStore
import UICatalog

// MARK: - MotionSettingToggle.Store

extension MotionSettingToggle {
  typealias Store = OGViewStore<ViewState, Event>
}

extension MotionSettingToggle {
  static func make() -> Store {
    Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector()
    )
  }
}

// MARK: - MotionSettingToggle.Event

extension MotionSettingToggle {
  enum Event: OGViewEvent, Hashable {
    /// Public Events
    case didToggleMotion(Bool)
    /// Private Events
    case _setMotionEnabled(Bool)
  }
}

// MARK: - MotionSettingToggle.ViewState

extension MotionSettingToggle {
  struct ViewState: OGViewState {
    private(set) var isMotionEnabled: Bool = true

    static var initial: MotionSettingToggle.ViewState = .init()

    var motionSettingTitle: String {
      ogL10n.General.MotionSetting.Toggle.Title
    }

    mutating func update(
      isMotionEnabled: Bool? = nil
    ) {
      self.isMotionEnabled = isMotionEnabled ?? self.isMotionEnabled
    }
  }
}

extension MotionSettingToggle {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case .didToggleMotion:
        break
      case let ._setMotionEnabled(isMotionEnabled):
        state.update(isMotionEnabled: isMotionEnabled)
      }
    }
  }

  final class Middleware: OGViewStoreMiddleware {
    private let appStore: any AppStoreProtocol

    init(
      appStore: any AppStoreProtocol = AppContainer.shared.appStore()
    ) {
      self.appStore = appStore
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch event {
      case let .didToggleMotion(isOn):
        appStore.dispatchDetached(
          AppAction.userPreferences(
            UserPreferencesAction.setMotionEnabled(isOn)
          )
        )
        return nil
      case ._setMotionEnabled:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let appStore: any AppStoreProtocol

    private var cancellables: Set<AnyCancellable> = []

    init(
      appStore: any AppStoreProtocol = AppContainer.shared.appStore()
    ) {
      self.appStore = appStore
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await appStore
        .watch(keyPath: \.userPreferencesState.isMotionEnabled)
        .sink { isMotionEnabled in
          Task {
            await dispatch(._setMotionEnabled(isMotionEnabled))
          }
        }
        .store(in: &cancellables)
    }
  }
}
