import SwiftUI
import UICatalog

public struct MotionSettingToggle: View {
  @StateObject private var viewStore: Self.Store

  private var isMotionEnabled: Binding<Bool> {
    Binding(
      get: {
        viewStore.state.isMotionEnabled
      },
      set: { newValue in
        Task {
          await viewStore.dispatch(.didToggleMotion(newValue))
        }
      }
    )
  }

  public init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  public var body: some View {
    DefaultToggleView(title: viewStore.motionSettingTitle, isOn: isMotionEnabled)
  }
}
