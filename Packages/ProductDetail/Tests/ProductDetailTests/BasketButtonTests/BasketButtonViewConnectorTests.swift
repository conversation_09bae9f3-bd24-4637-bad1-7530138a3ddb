import Combine
import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class BasketButtonViewConnectorTests: XCTestCase {
  func test_GIVEN_connector_WHEN_configureCalled_THEN_dispatchSuccessEvent() async {
    await withMainSerialExecutor {
      // Arrange

      let middlewareMock = BasketStoreMiddlewareMock()
      let basketStore = BasketStore(
        initialState: BasketState(didAddToBasket: .stub),
        reducer: BasketStoreReducerMock.reduce,
        middlewares: middlewareMock
      )

      middlewareMock.mock.callAsFunctionCalls.mockCall { _, _ in
        .addToBasket(.stub)
      }

      let sut = BasketButtonView.ViewState.Connector(basketStore: basketStore, price: nil)
      let expectation = expectation(description: "Expected _success event to be dispatched")

      var dispatchedEvents: [BasketButtonView.Event] = []
      let dispatch: (BasketButtonView.Event) async -> Void = { event in
        dispatchedEvents.append(event)
        if case ._success = event {
          expectation.fulfill()
        }
      }

      // Act
      await sut.configure(dispatch: dispatch)

      // Assert
      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertTrue(dispatchedEvents.contains { if case ._success = $0 { return true } else { return false } })
    }
  }
}
