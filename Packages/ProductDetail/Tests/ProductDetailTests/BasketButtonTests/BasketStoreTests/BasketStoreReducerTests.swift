import XCTest
@testable import ProductDetail

final class BasketStoreReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_didAddToBasket_THEN_isAwaitingUpdateTrue() {
    // GIVEN
    var state = BasketState()

    // WHEN
    BasketState.Reducer.reduce(&state, with: .addToBasket(.stub))

    // THEN
    XCTAssertTrue(state.isAwaitingUpdate)
    XCTAssertEqual(state.didAddToBasket, nil)
  }

  func test_GIVEN_initialState_WHEN_didAddToBasket_THEN_isAwaitingUpdateFalse() {
    // GIVEN
    var state = BasketState()

    // WHEN
    BasketState.Reducer.reduce(&state, with: ._didAddToBasket(.stub))

    // THEN
    XCTAssertFalse(state.isAwaitingUpdate)
    XCTAssertEqual(state.didAddToBasket, .stub)
  }
}
