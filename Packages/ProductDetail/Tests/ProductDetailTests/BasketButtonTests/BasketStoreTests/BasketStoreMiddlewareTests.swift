import Combine
import OGCoreTestsUtils
import OGRouter
import XCTest
@testable import ProductDetail

final class BasketStoreMiddlewareTests: XCTestCase {
  func test_GIVEN_validResponse_WHEN_addToBasket_THEN_didAddToBasket() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = BasketState.Middleware(service: mockService)
    let state = BasketState()
    mockService.mock.addProductToBasketCalls.mockCall { _ in
      1
    }
    // WHEN
    let action = try? await sut.callAsFunction(action: .addToBasket(.stub), for: state)

    // THEN
    XCTAssertEqual(action, ._didAddToBasket(.stub))
    XCTAssertEqual(mockService.mock.addProductToBasketCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.addProductToBasketCalls.latestCall, .stub)
  }
}
