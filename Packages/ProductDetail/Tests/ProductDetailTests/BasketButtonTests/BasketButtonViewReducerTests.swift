import OGAppKitSDK
import XCTest
@testable import ProductDetail

final class BasketButtonViewReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_eventReceived_THEN_noStateChange() throws {
    var state = BasketButtonView.ViewState.initial
    BasketButtonView.ViewState.Reducer.reduce(&state, with: ._success(.stub))
    XCTAssertEqual(state, BasketButtonView.ViewState.initial)
  }

  func test_GIVEN_state_WHEN_addToBasket_THEN_loadingStateUpdated() throws {
    var state = BasketButtonView.ViewState.initial
    BasketButtonView.ViewState.Reducer.reduce(&state, with: .addToBasket(.stub))
    XCTAssertTrue(state.isLoading)
  }

  func test_GIVEN_state_WHEN_failed_THEN_loadingStateUpdated() throws {
    var state = BasketButtonView.ViewState.initial
    BasketButtonView.ViewState.Reducer.reduce(&state, with: ._failed(BasketError.failed(.stub)))
    XCTAssertFalse(state.isLoading)
  }
}
