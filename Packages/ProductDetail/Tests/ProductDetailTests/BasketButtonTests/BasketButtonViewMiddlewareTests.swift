import Combine
import O<PERSON>ppKitSDK
import OGRouter
import OGRouterTestsUtils
import XCTest
@testable import ProductDetail

final class BasketButtonViewMiddlewareTests: XCTestCase {
  func test_GIVEN_addToBasket_WHEN_called_THEN_dispatchNoEvent() async {
    let basketStoreMiddlewareMock = BasketStoreMiddlewareMock()
    basketStoreMiddlewareMock.mock.callAsFunctionCalls.mockCall { action, _ in
      action
    }
    let basketStore = BasketStore(
      reducer: BasketStoreReducerMock.reduce,
      middlewares: basketStoreMiddlewareMock
    )
    let routerMock = OGRoutePublisherMock()
    let sut = BasketButtonView.ViewState.Middleware(basketStore: basketStore, router: routerMock)

    let event = await sut.callAsFunction(event: .addToBasket(.stub), for: .initial)

    XCTAssertNil(event)
  }

  func test_GIVEN_success_WHEN_called_THEN_showAddToBasketSuccess() async {
    let basketStoreMiddlewareMock = BasketStoreMiddlewareMock()
    basketStoreMiddlewareMock.mock.callAsFunctionCalls.mockCall { action, _ in
      action
    }
    let basketStore = BasketStore(
      reducer: BasketStoreReducerMock.reduce,
      middlewares: basketStoreMiddlewareMock
    )
    let routerMock = OGRoutePublisherMock()
    let sut = BasketButtonView.ViewState.Middleware(basketStore: basketStore, router: routerMock)

    let event = await sut.callAsFunction(event: ._success(.stub), for: .initial)

    XCTAssertNil(event)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall, OGRoute(.addToBasketSuccess, data: String.stub))
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
  }

  func test_GIVEN_failedEvent_WHEN_called_THEN_showErrorBanner() async {
    let basketStore = BasketStore(
      reducer: BasketStoreReducerMock.reduce,
      middlewares: BasketStoreMiddlewareMock()
    )
    let routerMock = OGRoutePublisherMock()
    let sut = BasketButtonView.ViewState.Middleware(basketStore: basketStore, router: routerMock)

    let event = await sut.callAsFunction(event: ._failed(BasketError.failed(.stub)), for: .initial)

    XCTAssertNil(event)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall?.id, OGRoute.errorBanner.id)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
  }
}
