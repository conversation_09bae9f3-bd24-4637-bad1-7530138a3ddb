import Foundation
import OGAppKitSDK

@testable import ProductDetail

extension ProductDimensionsVariant {
  static func stub(
    id: String = "1",
    name: String = "Small",
    productId: String = "p1",
    isSelected: Bool = false,
    availabilityState: AvailabilityState = .inStock,
    formattedPrice: String = "$10"
  ) -> ProductDimensionsVariant {
    ProductDimensionsVariant(
      id: id,
      name: name,
      productId: productId,
      isSelected: isSelected,
      formattedPrice: formattedPrice,
      availabilityColorName: availabilityState.colorName,
      availabilityInfo: availabilityState.availabilityInfo,
      isAvailable: availabilityState.isAvailable,
      hasDiscount: false,
      notifyMeUrl: nil
    )
  }
}
