import OGAppKitSDK
import OGCoreTestsUtils
@testable import ProductDetail

extension [OGProductDetailComponent] {
  static let stub: [OGProductDetailComponent] = [
    .productColor(.stub),
    .productDimensions(.stub),
    .addToBasketButton(.stub),
    .productPrice(.stub),
    .productHeader(.stub)
  ]
}

extension ProductHeader {
  static let stub = ProductHeader(
    state: LoadingComponentStateDone(
      content: ProductHeader.Content(
        title: .stub,
        brandName: .stub,
        sharingData: nil,
        isWishlisted: false,
        productIdForWishlisting: .stub,
        productId: .stub
      )
    )
  )
}

extension ProductGallery {
  static let stub = ProductGallery(
    state: LoadingComponentStateDone(
      content: ProductGallery.Content(
        images: [],
        flags: [],
        isWishlisted: false,
        productIdForWishlisting: .stub
      )
    )
  )
}

extension ProductDimensions {
  static let stub = ProductDimensions(
    state: LoadingComponentStateDone(
      content: ProductDimensions
        .FlatDimension(
          dimension: ProductDimensions
            .ProductDimension(
              name: .stub,
              variants: []
            ),
          isWishlisted: false,
          productIdForWishlisting: .stub,
          sizeAdvisorUrl: .stub
        )
    ),
    config: ProductDimensions
      .Config(
        style: .flat
      )
  )
}

extension ProductColor {
  static let stub = ProductColor(
    state: LoadingComponentStateDone(
      content: ProductColor.Content(
        colorName: .stub
      )
    )
  )
}

extension AddToBasketButton {
  static let stub = AddToBasketButton(
    state: LoadingComponentStateDone(
      content: AddToBasketButtonContentAddToBasket(
        productId: .stub
      )
    )
  )
}

extension SelectedVariant {
  static func stub(userDidChangeVariant: Bool = false) -> SelectedVariant {
    SelectedVariant(
      productId: .stub,
      userDidChangeVariant: userDidChangeVariant,
      isVoucher: false,
      customName: nil
    )
  }
}
