import Foundation
import OGAppKitSDK

@testable import ProductDetail

extension VariantSelectionModel {
  static func stub(
    variants: [ProductDimensionsVariant] = [.stub()],
    selectedVariantName: String = "Small",
    dimensionStyle: DimensionStyle = .flat,
    nestedDimensions: NestedDimensions? = nil
  ) -> VariantSelectionModel {
    VariantSelectionModel(
      variants: variants,
      selectedVariantName: selectedVariantName,
      dimensionStyle: dimensionStyle,
      nestedDimensions: nestedDimensions,
      customName: nil,
      customNameMaxLength: nil,
      isWishlisted: false,
      productIdForWishlisting: .stub
    )
  }
}
