import OGAppKitSDK
import OGCoreTestsUtils
@testable import ProductDetail

extension [OGAddToBasketSuccessComponent] {
  static let stub: [OGAddToBasketSuccessComponent] = [
    .addedProduct(AddedProduct.stub),
    .continueShoppingButton(.stub),
    .showBasketButton(.stub)
  ]
}

extension AddedProduct {
  static let stub = AddedProduct(
    state: LoadingComponentStateDone(
      content: AddedProduct.Content(
        productId: .stub, brandName: .stub, title: .stub, selectedDimensionValues: [], price: .stub, image: Image(url: .stub, thumbnailUrl: .stub)
      )
    )
  )
}

extension ContinueShoppingButton {
  static let stub = ContinueShoppingButton(
    state: LoadingComponentStateDone(
      content: ContinueShoppingButton.Content(continueShoppingUrl: .stub)
    ),
    config: ContinueShoppingButton.Config(
      continueShoppingUrl: nil
    )
  )
}

extension ShowBasketButton {
  static let stub = ShowBasketButton(
    state: LoadingComponentStateDone(
      content: ShowBasketButton.Content(basketUrl: .stub)
    ),
    config: ShowBasketButton.Config(
      basketUrl: ""
    )
  )
}
