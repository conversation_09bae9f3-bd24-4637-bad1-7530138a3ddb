import Foundation
import OGAppKitSDK

@testable import ProductDetail

extension NestedDimensions {
  static func stub(
    name: String = "Color",
    entries: [NestedDimension] = [.stub(), .stub(name: "Blue", productDimension: .stub(name: "<PERSON><PERSON>", entries: [.stub(id: "2", name: "Medium", productId: "p2")]))]
  ) -> NestedDimensions {
    NestedDimensions(name: name, entries: entries)
  }
}
