import OGAppKitSDK
import XCTest

@testable import ProductDetail

// MARK: - Test Stubs

extension Review {
  static let stub = Review(
    rating: 5,
    text: "Great product",
    title: "Amazing",
    dateTime: .none,
    reviewerName: "<PERSON>"
  )
}

extension ProductReviews_.Config {
  static let stub = ProductReviews_.Config(
    allReviewsUrl: "https://example.com/reviews/{productId}",
    ratingRounding: .whole,
    reviewCount: 10
  )
}

extension ProductReviews_ContentEmpty {
  static let stub = ProductReviews_ContentEmpty(
    writeReviewUrl: "https://example.com/write/{productId}"
  )
}

extension ProductReviews_ContentReviews {
  static let stub = ProductReviews_ContentReviews(
    totalCount: 10,
    rating: Rating(
      averageRating: 4.5,
      count: 100,
      ratingDistribution: [:]
    ),
    reviews: [Review.stub, Review.stub],
    allReviewsUrl: "https://example.com/reviews/123"
  )
}

extension ProductReviews_ {
  static let stub = ProductReviews_(state: LoadingComponentStateDone(content: ProductReviews_ContentReviews.stub), config: ProductReviews_.Config.stub)
}
