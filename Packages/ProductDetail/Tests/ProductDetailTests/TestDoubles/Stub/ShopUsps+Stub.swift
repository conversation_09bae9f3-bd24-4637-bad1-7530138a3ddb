import OGAppKitSDK
import OGCoreTestsUtils

extension ShopUsps {
  public static var stub: ShopUsps {
    ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 100),
      config: ShopUsps.Config(paybackPointsIndex: 2)
    )
  }
  
  public static var stubLoading: ShopUsps {
    ShopUsps(
      isLoading: true,
      content: ShopUsps.Content(paybackPoints: 0),
      config: ShopUsps.Config(paybackPointsIndex: 0)
    )
  }
  
  public static var stubWithoutPayback: ShopUsps {
    ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: nil),
      config: ShopUsps.Config(paybackPointsIndex: 3)
    )
  }
  
  public static var stubWithHighPaybackIndex: ShopUsps {
    ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 250),
      config: ShopUsps.Config(paybackPointsIndex: 10)
    )
  }
}

extension ShopUsps.Content {
  public static var stub: ShopUsps.Content {
    ShopUsps.Content(paybackPoints: 100)
  }
  
  public static var stubWithoutPayback: ShopUsps.Content {
    ShopUsps.Content(paybackPoints: nil)
  }
}

extension ShopUsps.Config {
  public static var stub: ShopUsps.Config {
    ShopUsps.Config(paybackPointsIndex: 2)
  }
  
  public static var stubHighIndex: ShopUsps.Config {
    ShopUsps.Config(paybackPointsIndex: 10)
  }
}
