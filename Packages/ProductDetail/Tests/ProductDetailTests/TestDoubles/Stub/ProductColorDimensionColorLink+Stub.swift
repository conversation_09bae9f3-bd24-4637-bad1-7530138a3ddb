import OGAppKitSDK
import OGCoreTestsUtils

extension ProductColorDimension.ColorLink {
  static func stub(isSelected: Bool = true, availabilityState: __Bridge__Availability_State = .inStock) -> ProductColorDimension.ColorLink {
    ProductColorDimension.ColorLink(colorName: .stub, productId: .stub, preview: ProductColorDimension.ColorLinkPreviewThumbnail(url: .stub), availability: Availability(state: availabilityState, quantity: 1, message: nil, deliveryTime: nil, notifyMeUrl: nil), isSelected: isSelected)
  }
}
