import OGAppKitSDK
import XCTest
@testable import ProductDetail

extension OGAppKitSDK.Price {
  static let stub = OGAppKitSDK.Price(
    value: 9_999,
    oldValue: nil,
    currency: "USD",
    priceRange: false,
    discounted: false
  )
}

extension OGAppKitSDK.Price_ {
  static let stub = OGAppKitSDK.Price_(
    currency: "USD",
    value: 9_999,
    oldValue: nil,
    isStartPrice: false
  )
}

extension OGAppKitSDK.ProductPrice.Config {
  static let stub = OGAppKitSDK.ProductPrice.Config(conditionsUrl: "")
}

extension OGAppKitSDK.ProductPrice {
  static let stub = OGAppKitSDK.ProductPrice(state: LoadingComponentStateDone(content: Price.stub), config: .stub)
}

extension ProductRecommendationsContent {
  static let stub = ProductRecommendationsContent(
    products: [
      ProductRecommendationsRecommendedProduct.stub,
      ProductRecommendationsRecommendedProduct.stub,
      ProductRecommendationsRecommendedProduct.stub
    ],
    image: nil,
    trackingId: ""
  )
}

extension OGAppKitSDK.LoadingComponentStateDone where C == OGAppKitSDK.ProductRecommendationsContent {
  static func mockState(products: [OGAppKitSDK.ProductRecommendationsRecommendedProduct]) -> any OGAppKitSDK.LoadingComponentState {
    OGAppKitSDK
      .LoadingComponentStateDone(
        content: OGAppKitSDK.ProductRecommendationsContent(
          products: products,
          image: nil,
          trackingId: ""
        )
      )
  }
}

extension ProductRecommendationsRecommendedProduct {
  static let mockProduct = ProductRecommendationsRecommendedProduct(
    productId: "prod_123",
    secondaryId: nil,
    brandName: "Test Brand",
    title: "Test Product",
    price: Price_(
      currency: "EUR",
      value: 9_999,
      oldValue: nil,
      isStartPrice: false
    ),
    image: .init(url: "", thumbnailUrl: nil),
    isWishlisted: false,
    productIdForWishlisting: "prod_123"
  )

  static let stub = ProductRecommendationsRecommendedProduct(
    productId: "prod_123",
    secondaryId: nil,
    brandName: "Test Brand",
    title: "Test Product",
    price: Price_(
      currency: "EUR",
      value: 9_999,
      oldValue: nil,
      isStartPrice: false
    ),
    image: .init(url: "", thumbnailUrl: nil),
    isWishlisted: false,
    productIdForWishlisting: "prod_123"
  )
}

extension StaticProductRecommendations {
  static let stub = StaticProductRecommendations(
    state: LoadingComponentStateDone(content: ProductRecommendationsContent.stub),
    config: Config(
      productIds: [
        "prod_123",
        "prod_124",
        "prod_125"
      ],
      titleL10n: nil,
      subtitleL10n: nil,
      maxEntries: 3
    )
  )

  static let emptyStub = StaticProductRecommendations(
    state: LoadingComponentStateDone(content: ProductRecommendationsContent(products: [], image: nil, trackingId: "")),
    config: Config(
      productIds: [],
      titleL10n: nil,
      subtitleL10n: nil,
      maxEntries: 0
    )
  )
}
