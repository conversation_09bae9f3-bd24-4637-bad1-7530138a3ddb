import Foundation
import OGAppKitSDK

@testable import ProductDetail

extension ProductDimensionsView.ViewState {
  static func stub(
    dimensionTitleName: String = "Size",
    variantName: String = "Small",
    variantSelectionModel: VariantSelectionModel = .stub()
  ) -> ProductDimensionsView.ViewState {
    ProductDimensionsView.ViewState(
      dimensionTitleName: dimensionTitleName,
      variantName: variantName
    )
  }
}
