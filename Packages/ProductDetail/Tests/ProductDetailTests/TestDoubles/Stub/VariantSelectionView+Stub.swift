import Foundation
import OGAppKitSDK

@testable import ProductDetail

extension VariantSelectionView.ViewState {
  static func stub(
    variants: [ProductDimensionsVariant] = [
      .stub(),
      .stub(
        id: "2",
        name: "Medium",
        productId: "p2",
        isSelected: true
      )
    ],
    dimensionStyle: DimensionStyle = .flat,
    nestedDimensions: NestedDimensions? = nil,
    selectedNestedDimensionEntry: NestedDimension? = nil,
    dimensionName: String = "Size",
    addToBasket: Bool = false,
    userDidSelectVariant: Bool = false
  ) -> VariantSelectionView.ViewState {
    VariantSelectionView.ViewState(
      variantSelectionModel: VariantSelectionModel(
        variants: variants,
        selectedVariantName: "",
        dimensionStyle: dimensionStyle,
        nestedDimensions: nestedDimensions,
        customName: nil,
        customNameMaxLength: nil,
        isWishlisted: false,
        productIdForWishlisting: .stub
      ),
      userDidSelectVariant: userDidSelectVariant
    )
  }
}
