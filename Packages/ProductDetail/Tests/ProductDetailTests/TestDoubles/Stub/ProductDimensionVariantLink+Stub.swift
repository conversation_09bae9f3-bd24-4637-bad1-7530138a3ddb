import Foundation
import OGAppKitSDK

@testable import ProductDetail

extension ProductDimensions.ProductDimensionVariantLink {
  static func stub(
    name: String = "Small",
    productId: String = "p1",
    availabilityState: AvailabilityState = .inStock,
    price: Price_ = .stub(),
    isSelected: Bool = true
  ) -> ProductDimensions.ProductDimensionVariantLink {
    ProductDimensions.ProductDimensionVariantLink(
      name: name,
      productId: productId,
      availability: Availability(
        state: availabilityState,
        quantity: 1,
        message: nil,
        deliveryTime: nil,
        notifyMeUrl: nil
      ),
      price: price,
      isSelected: isSelected
    )
  }
}
