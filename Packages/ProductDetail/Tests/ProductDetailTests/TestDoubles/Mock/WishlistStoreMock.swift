import OGDomainStore
import OGMacros
import OGMock
@testable import ProductDetail

// MARK: - WishlistStoreMiddlewareMock

@OGMock
class WishlistStoreMiddlewareMock: OGDomainMiddleware {
  func callAsFunction(
    action: WishlistAction,
    for state: WishlistState
  ) async throws
    -> WishlistAction? {
    try await mock.callAsFunction(action: action, for: state)
  }
}

// MARK: - WishlistStoreReducerMock

enum WishlistStoreReducerMock {
  static func reduce(
    _ state: inout WishlistState,
    with action: WishlistAction
  ) {}
}
