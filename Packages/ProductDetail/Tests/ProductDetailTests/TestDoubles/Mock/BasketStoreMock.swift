import OGDomainStore
import OGMacros
import OGMock
@testable import ProductDetail

// MARK: - BasketStoreMiddlewareMock

@OGMock
class BasketStoreMiddlewareMock: OGDomainMiddleware {
  func callAsFunction(
    action: BasketAction,
    for state: BasketState
  ) async throws
    -> BasketAction? {
    try await mock.callAsFunction(action: action, for: state)
  }
}

// MARK: - BasketStoreReducerMock

enum BasketStoreReducerMock {
  static func reduce(
    _ state: inout BasketState,
    with action: BasketAction
  ) {}
}
