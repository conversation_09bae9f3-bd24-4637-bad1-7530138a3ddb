import OGDomainStore
import OGMacros
import OGMock
@testable import ProductDetail

// MARK: - ProductDetailStoreMiddlewareMock

@OGMock
class ProductDetailStoreMiddlewareMock: OGDomainMiddleware {
  func callAsFunction(
    action: ProductDetailAction,
    for state: ProductDetailState
  ) async throws
    -> ProductDetailAction? {
    try await mock.callAsFunction(action: action, for: state)
  }
}

// MARK: - ProductDetailStoreReducerMock

enum ProductDetailStoreReducerMock {
  static func reduce(
    _ state: inout ProductDetailState,
    with action: ProductDetailAction
  ) {}
}
