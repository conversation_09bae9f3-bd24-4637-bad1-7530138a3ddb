import AsyncAlgorithms
import Combine
import Foundation
import OGMacros
import OGMock
@testable import ProductDetail

@OGMock
final class AddToBasketSuccessServiceMock: AddToBasketSuccessServing {
  var successStream: AsyncChannel<[OGAddToBasketSuccessComponent]> {
    get { mock.successStream.getter.record() }
    set { mock.successStream.setter.record(newValue) }
  }

  var errorStream: AsyncChannel<any Error> {
    get { mock.errorStream.getter.record() }
    set { mock.errorStream.setter.record(newValue) }
  }

  func getAddToBasketSuccess(productId: String, componentConfigsJson: String) async {
    await mock.getAddToBasketSuccess(productId: productId, componentConfigsJson: componentConfigsJson)
  }
}
