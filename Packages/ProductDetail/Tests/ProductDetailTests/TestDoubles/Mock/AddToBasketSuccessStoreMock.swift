import OGDomainStore
import OGMacros
import OGMock
@testable import ProductDetail

// MARK: - AddToBasketSuccessStoreMiddlewareMock

@OGMock
class AddToBasketSuccessStoreMiddlewareMock: OGDomainMiddleware {
  func callAsFunction(
    action: AddToBasketSuccessAction,
    for state: AddToBasketSuccessState
  ) async throws
    -> AddToBasketSuccessAction? {
    try await mock.callAsFunction(action: action, for: state)
  }
}

// MARK: - AddToBasketSuccessStoreReducerMock

enum AddToBasketSuccessStoreReducerMock {
  static func reduce(
    _ state: inout AddToBasketSuccessState,
    with action: AddToBasketSuccessAction
  ) {}
}
