import AsyncAlgorithms
import Combine
import Foundation
import OGMacros
import OGMock
import OGRouter
@testable import ProductDetail

@OGMock
class ProductDetailServiceMock: ProductDetailServing {
  var successStream: AsyncChannel<(components: [ProductDetail.OGProductDetailComponent], screenId: String)> {
    get { mock.successStream.getter.record() }
    set { mock.successStream.setter.record(newValue) }
  }

  var errorStream: AsyncChannel<any Error> {
    get { mock.errorStream.getter.record() }
    set { mock.errorStream.setter.record(newValue) }
  }

  func fetchProductDetail(productId: String, secondaryId: String?, componentConfigsJson: String) async {
    await mock.fetchProductDetail(productId: productId, secondaryId: secondaryId, componentConfigsJson: componentConfigsJson)
  }

  func fetchProductDetailScreen(for url: URL, componentConfigsJson: String) async {
    await mock.fetchProductDetailScreen(for: url, componentConfigsJson: componentConfigsJson)
  }

  func addProductToBasket(id: String) async throws -> Int {
    try await mock.addProductToBasket(id: id)
  }

  func addProductToWishlist(id: String) async throws -> [String] {
    try await mock.addProductToWishlist(id: id)
  }

  func removeProductFromWishlist(id: String) async throws -> [String] {
    try await mock.removeProductFromWishlist(id: id)
  }

  func refreshWishlistCount() async throws -> [String] {
    try await mock.refreshWishlistCount()
  }

  func refreshBasketCount() async throws -> Int {
    try await mock.refreshBasketCount()
  }

  func addVoucherToBasket(id: String, customName: String?) async throws -> Int {
    try await mock.addVoucherToBasket(id: id, customName: customName)
  }

  func updateProductColorSelection(productId: String, screenId: String) async {
    await mock.updateProductColorSelection(productId: productId, screenId: screenId)
  }

  func updateProductVariantSelection(productId: String, screenId: String) async {
    await mock.updateProductVariantSelection(productId: productId, screenId: screenId)
  }
}
