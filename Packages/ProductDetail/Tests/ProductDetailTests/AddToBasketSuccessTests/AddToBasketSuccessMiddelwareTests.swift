import Combine
import <PERSON>GAppKitSDK
import <PERSON>GCoreTestsUtils
import OGFeatureAdapterTestsUtils
import <PERSON>GRouter
import OGRouterTestsUtils
import XCTest
@testable import ProductDetail

final class AddToBasketSuccessMiddlewareTests: XCTestCase {
  private var sut: AddToBasketSuccessView.Middleware!
  private var routerMock: OGRoutePublisherMock!
  private var baseUrlMock: OGBaseUrlFeatureAdapterMock!

  override func setUp() {
    super.setUp()
    routerMock = OGRoutePublisherMock()
    baseUrlMock = OGBaseUrlFeatureAdapterMock()
    sut = AddToBasketSuccessView.Middleware(router: routerMock, baseUrl: baseUrlMock)
  }

  override func tearDown() {
    sut = nil
    routerMock = nil
    baseUrlMock = nil
    super.tearDown()
  }

  func test_GIVEN_initialState_WHEN_eventIsDismiss_THEN_shouldDismissAndSendContinueShoppingRoute() async {
    let expectation = expectation(description: "Expected dismiss and send continue shopping route")

    let continueShoppingUrl = "/continue-shopping"
    baseUrlMock.urlRelativeToWebUrlHandler = {
      URL(string: continueShoppingUrl)!
    }
    let state = AddToBasketSuccessView.ViewState(continueShoppingUrl: continueShoppingUrl)

    let nextEvent = await sut.callAsFunction(event: .dismiss, for: state)

    XCTAssertEqual(routerMock.mock.dismissRouteCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.dismissRouteCalls.latestCall?.id, OGRoute.addToBasketSuccess.id)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall?.url.absoluteString, URL(string: continueShoppingUrl)?.absoluteString)
    XCTAssertNil(nextEvent)

    expectation.fulfill()
    await fulfillment(of: [expectation], timeout: 0.1)
  }

  func test_GIVEN_initialState_WHEN_eventIsGoToBasket_THEN_shouldDismissAndSendBasketRoute() async {
    let expectation = expectation(description: "Expected dismiss and send basket route")

    let basketPath = "/basket"
    baseUrlMock.urlRelativeToWebUrlHandler = {
      URL(string: basketPath)!
    }
    let state = AddToBasketSuccessView.ViewState(basketPath: basketPath, continueShoppingUrl: nil)

    let nextEvent = await sut.callAsFunction(event: .goToBasket, for: state)

    XCTAssertEqual(routerMock.mock.dismissRouteCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.dismissRouteCalls.latestCall?.id, OGRoute.addToBasketSuccess.id)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall?.url.absoluteString, URL(string: basketPath)?.absoluteString)
    XCTAssertNil(nextEvent)

    expectation.fulfill()
    await fulfillment(of: [expectation], timeout: 0.1)
  }

  func test_GIVEN_initialState_WHEN_eventIsReceived_THEN_NoNextEvent() async {
    let state = AddToBasketSuccessView.ViewState(basketPath: "", continueShoppingUrl: nil)
    let nextEvent = await sut.callAsFunction(event: ._received([]), for: state)
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventIsReceivedButtons_THEN_NoNextEvent() async {
    let state = AddToBasketSuccessView.ViewState(basketPath: "", continueShoppingUrl: nil)
    let nextEvent = await sut.callAsFunction(event: ._receivedButtons(AddToBasketSuccessButtons(continueShopping: nil, showBasket: nil)), for: state)
    XCTAssertNil(nextEvent)
  }
}
