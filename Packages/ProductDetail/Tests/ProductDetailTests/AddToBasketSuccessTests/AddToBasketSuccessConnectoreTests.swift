import Combine
import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class AddToBasketSuccessConnectorTests: XCTestCase {
  private var sut: AddToBasketSuccessView.Connector!

  override func setUp() {
    super.setUp()
    let mockStore = AddToBasketSuccessStore(
      initialState: AddToBasketSuccessState(
        isAwaitingUpdate: false,
        components: [],
        componentConfigsJson: "",
        addToBasketSuccessButtons: AddToBasketSuccessButtons(
          continueShopping: .stub,
          showBasket: .stub
        )
      ),
      reducer: AddToBasketSuccessStoreReducerMock.reduce,
      middlewares: AddToBasketSuccessStoreMiddlewareMock()
    )
    sut = AddToBasketSuccessView.Connector(addToBasketSuccessStore: mockStore)
  }

  override func tearDown() {
    sut = nil
    super.tearDown()
  }

  func test_GIVEN_connector_WHEN_configureCalled_THEN_dispatchReceivedEvent() async {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected _received event to be dispatched")

      var dispatchedEvents: [AddToBasketSuccessView.Event] = []
      let dispatch: (AddToBasketSuccessView.Event) async -> Void = { event in
        dispatchedEvents.append(event)
        if case ._received = event {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertTrue(dispatchedEvents.contains(where: {
        if case ._received = $0 {
          return true
        }
        return false
      }))
    }
  }

  func test_GIVEN_connector_WHEN_configureCalled_THEN_dispatchReceivedButtonsEvent() async {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected _receivedButtons event to be dispatched")

      var dispatchedEvents: [AddToBasketSuccessView.Event] = []
      let dispatch: (AddToBasketSuccessView.Event) async -> Void = { event in
        dispatchedEvents.append(event)
        if case ._receivedButtons = event {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertTrue(dispatchedEvents.contains(where: {
        if case ._receivedButtons = $0 {
          return true
        }
        return false
      }))
    }
  }
}
