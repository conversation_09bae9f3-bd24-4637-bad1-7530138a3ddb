import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class AddToBasketSuccessStateReducerTests: XCTestCase {
  func test_GIVEN_initial_state_WHEN_actionIsSetAddToBasketSuccessScreen_THEN_stateShouldUpdate() {
    let components: [OGAddToBasketSuccessComponent] = .stub
    var state = AddToBasketSuccessState.initial
    AddToBasketSuccessState.Reducer.reduce(&state, with: ._setAddToBasketSuccessScreen(components))
    XCTAssertEqual(state.isAwaitingUpdate, false)
    XCTAssertEqual(state.components, components.componentsWithoutButtons)
    XCTAssertEqual(state.addToBasketSuccessButtons, AddToBasketSuccessButtons(
      continueShopping: components.continueShoppingButton,
      showBasket: components.showBasketButton
    ))
  }

  func test_GIVEN_initial_state_WHEN_actionIsSetComponentConfigsJson_THEN_componentConfigsJsonShouldUpdate() {
    var state = AddToBasketSuccessState.initial
    let componentConfigsJson = "{\"key\": \"value\"}"
    AddToBasketSuccessState.Reducer.reduce(&state, with: ._setComponentConfigsJson(componentConfigsJson))
    XCTAssertEqual(state.componentConfigsJson, componentConfigsJson)
  }

  func test_GIVEN_initial_state_WHEN_actionIsGetAddToBasketSuccessScreenWith_THEN_stateShouldRemainUnchanged() {
    var state = AddToBasketSuccessState.initial
    AddToBasketSuccessState.Reducer.reduce(&state, with: .getAddToBasketSuccessScreenWith(id: .stub))
    XCTAssertEqual(state, AddToBasketSuccessState.initial)
  }
}
