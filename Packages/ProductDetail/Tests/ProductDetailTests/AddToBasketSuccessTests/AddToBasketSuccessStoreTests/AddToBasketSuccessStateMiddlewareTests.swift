import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class AddToBasketSuccessStateMiddlewareTests: XCTestCase {
  private var sut: AddToBasketSuccessState.Middleware!
  private var serviceMock: AddToBasketSuccessServiceMock!

  override func setUp() {
    super.setUp()
    serviceMock = AddToBasketSuccessServiceMock()
    sut = AddToBasketSuccessState.Middleware(service: serviceMock, productID: .stub)
  }

  override func tearDown() {
    sut = nil
    serviceMock = nil
    super.tearDown()
  }

  func test_GIVEN_actionGetAddToBasketSuccessScreenWith_WHEN_called_THEN_serviceShouldBeInvoked() async throws {
    let state = AddToBasketSuccessState(componentConfigsJson: .stub)
    let action: AddToBasketSuccessAction = .getAddToBasketSuccessScreenWith(id: .stub)

    let nextAction = try await sut(action: action, for: state)

    XCTAssertEqual(serviceMock.mock.getAddToBasketSuccessCalls.callsCount, 1)
    XCTAssertEqual(serviceMock.mock.getAddToBasketSuccessCalls.latestCall?.0, .stub)
    XCTAssertEqual(serviceMock.mock.getAddToBasketSuccessCalls.latestCall?.1, .stub)
    XCTAssertNil(nextAction)
  }

  func test_GIVEN_actionSetComponentConfigsJson_WHEN_stateHasConfig_THEN_shouldReturnGetAddToBasketSuccessScreenWith() async throws {
    var state = AddToBasketSuccessState.initial
    state.update(componentConfigsJson: .stub, componentConfigsDidChange: true)
    let action: AddToBasketSuccessAction = ._setComponentConfigsJson(.stub)

    let nextAction = try await sut(action: action, for: state)

    XCTAssertEqual(nextAction, .getAddToBasketSuccessScreenWith(id: .stub))
  }

  func test_GIVEN_actionSetComponentConfigsJson_WHEN_stateHasNoConfig_THEN_shouldReturnNil() async throws {
    let state = AddToBasketSuccessState.initial
    let action: AddToBasketSuccessAction = ._setComponentConfigsJson("")

    let nextAction = try await sut(action: action, for: state)

    XCTAssertNil(nextAction)
  }

  func test_GIVEN_actionSetAddToBasketSuccessScreen_WHEN_called_THEN_shouldReturnNil() async throws {
    let state = AddToBasketSuccessState.initial
    let action: AddToBasketSuccessAction = ._setAddToBasketSuccessScreen(.stub)

    let nextAction = try await sut(action: action, for: state)

    XCTAssertNil(nextAction)
  }
}
