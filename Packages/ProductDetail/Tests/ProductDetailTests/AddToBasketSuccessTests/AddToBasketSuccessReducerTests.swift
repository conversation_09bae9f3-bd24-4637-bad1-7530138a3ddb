import OGAppKitSDK
import XCTest
@testable import ProductDetail

final class AddToBasketSuccessViewReducerTests: XCTestCase {
  func test_GIVEN_initial_state_WHEN_eventIsReceivedComponents_THEN_componentsShouldUpdate() {
    var state = AddToBasketSuccessView.ViewState.initial
    let components: [OGAddToBasketSuccessComponent] = []
    AddToBasketSuccessView.Reducer.reduce(&state, with: ._received(components))
    XCTAssertEqual(state.components, components)
  }

  func test_GIVEN_initial_state_WHEN_eventIsReceivedButtons_THEN_buttonsShouldUpdate() {
    var state = AddToBasketSuccessView.ViewState.initial
    let buttons = AddToBasketSuccessButtons(
      continueShopping: .stub,
      showBasket: .stub
    )

    AddToBasketSuccessView.Reducer.reduce(&state, with: ._receivedButtons(buttons))
    XCTAssertTrue(state.hasShowBasketButton)
    XCTAssertTrue(state.hasContinueShoppingButton)
  }

  func test_GIVEN_initial_state_WHEN_eventIsDismiss_THEN_stateShouldRemainUnchanged() {
    var state = AddToBasketSuccessView.ViewState.initial
    AddToBasketSuccessView.Reducer.reduce(&state, with: .dismiss)
    XCTAssertEqual(state, AddToBasketSuccessView.ViewState.initial)
  }

  func test_GIVEN_initial_state_WHEN_eventIsGoToBasket_THEN_stateShouldRemainUnchanged() {
    var state = AddToBasketSuccessView.ViewState.initial
    AddToBasketSuccessView.Reducer.reduce(&state, with: .goToBasket)
    XCTAssertEqual(state, AddToBasketSuccessView.ViewState.initial)
  }
}
