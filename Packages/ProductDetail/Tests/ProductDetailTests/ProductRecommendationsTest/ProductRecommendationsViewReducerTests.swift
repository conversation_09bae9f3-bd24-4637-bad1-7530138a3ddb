import OGAppKitSDK
import XCTest

@testable import ProductDetail

final class ProductRecommendationsViewReducerTests: XCTestCase {
  func test_WHEN_setRecommendations_THEN_stateUpdatesCorrectly() throws {
    // Given
    var initial = ProductRecommendationsView.ViewState.initial
    let mockProducts = [OGAppKitSDK.ProductRecommendationsRecommendedProduct.mockProduct]
    let mockState = OGAppKitSDK.LoadingComponentStateDone.mockState(products: mockProducts)
    let mockRecommendations = OGAppKitSDK.StaticProductRecommendations(
      state: mockState,
      config: OGAppKitSDK.StaticProductRecommendations.Config(
        productIds: ["prod_123"],
        titleL10n: nil,
        subtitleL10n: nil,
        maxEntries: 1
      )
    )

    // When
    ProductRecommendationsView.ViewState.Reducer.reduce(
      &initial,
      with: ._setRecommendations(with: mockRecommendations)
    )

    // Then
    XCTAssertEqual(initial.products.count, 1)
    XCTAssertEqual(initial.products.first?.productId, "prod_123")
  }

  func test_WHEN_setRecommendationsWithEmptyContent_THEN_stateHasNoProducts() throws {
    // Given
    var initial = ProductRecommendationsView.ViewState.initial
    let emptyState = OGAppKitSDK.LoadingComponentStateDone.mockState(products: [])
    let emptyRecommendations = OGAppKitSDK.StaticProductRecommendations(
      state: emptyState,
      config: OGAppKitSDK.StaticProductRecommendations
        .Config(
          productIds: [],
          titleL10n: nil,
          subtitleL10n: nil,
          maxEntries: 0
        )
    )

    // When
    ProductRecommendationsView.ViewState.Reducer.reduce(
      &initial,
      with: ._setRecommendations(with: emptyRecommendations)
    )

    // Then
    XCTAssertTrue(initial.products.isEmpty)
  }
}
