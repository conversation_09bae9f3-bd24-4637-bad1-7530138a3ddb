import Combine
import OGAppKitSDK
import OGNavigationTestsUtils
import OGRouter
import OGRouterTestsUtils
import XCTest

@testable import ProductDetail

final class ProductRecommendationsViewMiddlewareTests: XCTestCase {
  func test_WHEN_middlewareProcessesSetRecommendationsEvent_THEN_returnsNil() async {
    // Given
    let routerMock = OGRoutePublisherMock()
    let middleware = ProductRecommendationsView.ViewState.Middleware(router: routerMock, dismissRoute: nil)
    let mockRecommendations = StaticProductRecommendations.stub

    // When
    let result = await middleware(
      event: ._setRecommendations(with: mockRecommendations),
      for: .initial
    )

    // Then
    XCTAssertNil(result)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 0)
  }

  func test_WHEN_productTapped_THEN_routerSendsProductDetailRoute() async {
    // Given
    let routerMock = OGRoutePublisherMock()
    let middleware = ProductRecommendationsView.ViewState.Middleware(router: routerMock, dismissRoute: nil)
    let productId = "test_product_id"

    // When
    let result = await middleware(
      event: .productTapped(productId: productId),
      for: .init(trackingScenario: .mainContent, isLoading: false)
    )

    // Then
    XCTAssertEqual(result, ._trackSelection(productId: productId))
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
  }
}
