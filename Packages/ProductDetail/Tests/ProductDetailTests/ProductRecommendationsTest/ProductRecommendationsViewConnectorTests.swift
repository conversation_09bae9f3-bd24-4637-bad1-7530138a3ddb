import OGAppKitSDK
import XCTest

@testable import ProductDetail

final class ProductRecommendationsViewConnectorTests: XCTestCase {
  func test_WHEN_connectorConfigures_THEN_dispatchesSetRecommendationsEvent() async throws {
    // Given
    let mockRecommendations = StaticProductRecommendations.stub
    let connector = ProductRecommendationsView.ViewState.Connector(recommendations: mockRecommendations)
    var dispatchedEvent: ProductRecommendationsView.Event?

    // When
    await connector.configure { event in
      dispatchedEvent = event
    }

    // Then
    if case let ._setRecommendations(recommendations) = dispatchedEvent {
      XCTAssertTrue(recommendations is StaticProductRecommendations)
    } else {
      XCTFail("Expected _setRecommendations event")
    }
  }

  func test_WHEN_storeInitializedWithEmptyData_THEN_connectorConfiguresCorrectly() async throws {
    // Given
    let emptyRecommendations = StaticProductRecommendations.emptyStub

    // When
    let store = await ProductRecommendationsView.makeStore(recommendations: emptyRecommendations, trackingScenario: .mainContent, dismissRoute: nil)

    // Then
    let state = await store.state
    XCTAssertTrue(state.products.isEmpty)
  }
}
