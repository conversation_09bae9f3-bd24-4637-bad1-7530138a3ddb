import XCTest
@testable import ProductDetail

final class ShopUspsViewUspsItemTests: XCTestCase {
  
  /// Test: GIVEN UspsItem with all properties, WHEN created, THEN all properties are set correctly
  func test_GIVEN_uspsItemWithAllProperties_WHEN_created_THEN_allPropertiesAreSetCorrectly() {
    // GIVEN & WHEN
    let item = ShopUspsView.UspsItem(
      icon: "test-icon",
      color: "test-color",
      text: "test-text",
      accessibilityLabel: "test-accessibility"
    )
    
    // THEN
    XCTAssertEqual(item.icon, "test-icon")
    XCTAssertEqual(item.color, "test-color")
    XCTAssertEqual(item.text, "test-text")
    XCTAssertEqual(item.accessibilityLabel, "test-accessibility")
  }
  
  /// Test: GIVEN two identical UspsItems, WHEN compared for equality, THEN they are equal
  func test_GIVEN_twoIdenticalUspsItems_WHEN_comparedForEquality_THEN_theyAreEqual() {
    // GIVEN
    let item1 = ShopUspsView.UspsItem(
      icon: "icon",
      color: "color",
      text: "text",
      accessibilityLabel: "accessibility"
    )
    let item2 = ShopUspsView.UspsItem(
      icon: "icon",
      color: "color",
      text: "text",
      accessibilityLabel: "accessibility"
    )
    
    // WHEN & THEN
    XCTAssertEqual(item1, item2)
  }
  
  /// Test: GIVEN two different UspsItems, WHEN compared for equality, THEN they are not equal
  func test_GIVEN_twoDifferentUspsItems_WHEN_comparedForEquality_THEN_theyAreNotEqual() {
    // GIVEN
    let item1 = ShopUspsView.UspsItem(
      icon: "icon1",
      color: "color",
      text: "text",
      accessibilityLabel: "accessibility"
    )
    let item2 = ShopUspsView.UspsItem(
      icon: "icon2",
      color: "color",
      text: "text",
      accessibilityLabel: "accessibility"
    )
    
    // WHEN & THEN
    XCTAssertNotEqual(item1, item2)
  }
  
  /// Test: GIVEN UspsItem, WHEN used in Set, THEN hashable protocol works correctly
  func test_GIVEN_uspsItem_WHEN_usedInSet_THEN_hashableProtocolWorksCorrectly() {
    // GIVEN
    let item1 = ShopUspsView.UspsItem(
      icon: "icon",
      color: "color",
      text: "text",
      accessibilityLabel: "accessibility"
    )
    let item2 = ShopUspsView.UspsItem(
      icon: "icon",
      color: "color",
      text: "text",
      accessibilityLabel: "accessibility"
    )
    let item3 = ShopUspsView.UspsItem(
      icon: "different-icon",
      color: "color",
      text: "text",
      accessibilityLabel: "accessibility"
    )
    
    // WHEN
    let set: Set<ShopUspsView.UspsItem> = [item1, item2, item3]
    
    // THEN
    XCTAssertEqual(set.count, 2) // item1 and item2 are identical, so only 2 unique items
  }
  
  /// Test: GIVEN UspsItem with empty strings, WHEN created, THEN empty strings are preserved
  func test_GIVEN_uspsItemWithEmptyStrings_WHEN_created_THEN_emptyStringsArePreserved() {
    // GIVEN & WHEN
    let item = ShopUspsView.UspsItem(
      icon: "",
      color: "",
      text: "",
      accessibilityLabel: ""
    )
    
    // THEN
    XCTAssertEqual(item.icon, "")
    XCTAssertEqual(item.color, "")
    XCTAssertEqual(item.text, "")
    XCTAssertEqual(item.accessibilityLabel, "")
  }
  
  /// Test: GIVEN UspsItem with special characters, WHEN created, THEN special characters are preserved
  func test_GIVEN_uspsItemWithSpecialCharacters_WHEN_created_THEN_specialCharactersArePreserved() {
    // GIVEN & WHEN
    let item = ShopUspsView.UspsItem(
      icon: "icon-with-special-chars-123!@#",
      color: "color.with.dots",
      text: "Text with émojis 🎉 and special chars: äöü",
      accessibilityLabel: "Accessibility with numbers: 123 and symbols: &%$"
    )
    
    // THEN
    XCTAssertEqual(item.icon, "icon-with-special-chars-123!@#")
    XCTAssertEqual(item.color, "color.with.dots")
    XCTAssertEqual(item.text, "Text with émojis 🎉 and special chars: äöü")
    XCTAssertEqual(item.accessibilityLabel, "Accessibility with numbers: 123 and symbols: &%$")
  }
}
