import XCTest
@testable import ProductDetail

final class ShopUspsViewReducerTests: XCTestCase {
  
  /// Test: GIVEN initial state, WHEN _received event is dispatched, THEN state updates with shopUsps and isLoading
  func test_GIVEN_initialState_WHEN_receivedEvent_THEN_stateUpdatesWithShopUspsAndIsLoading() {
    // GIVEN
    var state = ShopUspsView.ViewState.initial
    let shopUsps = ShopUsps.stub
    
    // WHEN
    ShopUspsView.ViewState.Reducer.reduce(&state, with: ._received(shopUsps))
    
    // THEN
    XCTAssertFalse(state.isLoading)
    XCTAssertEqual(state.usps.count, 4) // 3 default items + 1 payback item
  }
  
  /// Test: GIVEN initial state, WHEN _received event with loading shopUsps is dispatched, THEN isLoading is true
  func test_GIVEN_initialState_WHEN_receivedEventWithLoadingShopUsps_THEN_isLoadingTrue() {
    // GIVEN
    var state = ShopUspsView.ViewState.initial
    let shopUsps = ShopUsps.stubLoading
    
    // WHEN
    ShopUspsView.ViewState.Reducer.reduce(&state, with: ._received(shopUsps))
    
    // THEN
    XCTAssertTrue(state.isLoading)
  }
  
  /// Test: GIVEN initial state, WHEN _received event with shopUsps without payback is dispatched, THEN only default items are returned
  func test_GIVEN_initialState_WHEN_receivedEventWithoutPayback_THEN_onlyDefaultItemsReturned() {
    // GIVEN
    var state = ShopUspsView.ViewState.initial
    let shopUsps = ShopUsps.stubWithoutPayback
    
    // WHEN
    ShopUspsView.ViewState.Reducer.reduce(&state, with: ._received(shopUsps))
    
    // THEN
    XCTAssertFalse(state.isLoading)
    XCTAssertEqual(state.usps.count, 3) // Only default items, no payback item
  }
  
  /// Test: GIVEN initial state, WHEN _received event with high payback index is dispatched, THEN payback item is appended at end
  func test_GIVEN_initialState_WHEN_receivedEventWithHighPaybackIndex_THEN_paybackItemAppendedAtEnd() {
    // GIVEN
    var state = ShopUspsView.ViewState.initial
    let shopUsps = ShopUsps.stubWithHighPaybackIndex
    
    // WHEN
    ShopUspsView.ViewState.Reducer.reduce(&state, with: ._received(shopUsps))
    
    // THEN
    XCTAssertFalse(state.isLoading)
    XCTAssertEqual(state.usps.count, 4) // 3 default items + 1 payback item at end
    // Verify payback item is at the end (index 3)
    XCTAssertTrue(state.usps.last?.text.contains("250") == true)
  }
  
  /// Test: GIVEN state with existing shopUsps, WHEN _received event with new shopUsps is dispatched, THEN state updates correctly
  func test_GIVEN_stateWithExistingShopUsps_WHEN_receivedEventWithNewShopUsps_THEN_stateUpdatesCorrectly() {
    // GIVEN
    var state = ShopUspsView.ViewState(isLoading: true, shopUsps: ShopUsps.stubLoading)
    let newShopUsps = ShopUsps.stub
    
    // WHEN
    ShopUspsView.ViewState.Reducer.reduce(&state, with: ._received(newShopUsps))
    
    // THEN
    XCTAssertFalse(state.isLoading)
    XCTAssertEqual(state.usps.count, 4) // Updated with new shopUsps
  }
  
  /// Test: GIVEN initial state, WHEN _received event with payback index 0 is dispatched, THEN payback item is inserted at beginning
  func test_GIVEN_initialState_WHEN_receivedEventWithPaybackIndexZero_THEN_paybackItemInsertedAtBeginning() {
    // GIVEN
    var state = ShopUspsView.ViewState.initial
    let shopUsps = ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 150),
      config: ShopUsps.Config(paybackPointsIndex: 0)
    )
    
    // WHEN
    ShopUspsView.ViewState.Reducer.reduce(&state, with: ._received(shopUsps))
    
    // THEN
    XCTAssertFalse(state.isLoading)
    XCTAssertEqual(state.usps.count, 4) // 3 default items + 1 payback item
    // Verify payback item is at the beginning (index 0)
    XCTAssertTrue(state.usps.first?.text.contains("150") == true)
  }
  
  /// Test: GIVEN initial state, WHEN _received event with payback index 1 is dispatched, THEN payback item is inserted at index 1
  func test_GIVEN_initialState_WHEN_receivedEventWithPaybackIndexOne_THEN_paybackItemInsertedAtIndexOne() {
    // GIVEN
    var state = ShopUspsView.ViewState.initial
    let shopUsps = ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 75),
      config: ShopUsps.Config(paybackPointsIndex: 1)
    )
    
    // WHEN
    ShopUspsView.ViewState.Reducer.reduce(&state, with: ._received(shopUsps))
    
    // THEN
    XCTAssertFalse(state.isLoading)
    XCTAssertEqual(state.usps.count, 4) // 3 default items + 1 payback item
    // Verify payback item is at index 1
    XCTAssertTrue(state.usps[1].text.contains("75") == true)
  }
}
