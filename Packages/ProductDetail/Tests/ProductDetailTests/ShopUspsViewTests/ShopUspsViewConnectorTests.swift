import XCTest
@testable import ProductDetail

final class ShopUspsViewConnectorTests: XCTestCase {
  
  /// Test: GIVEN connector with shopUsps, WHEN configure is called, THEN _received event is dispatched
  func test_GIVEN_connectorWithShopUsps_WHEN_configureIsCalled_THEN_receivedEventIsDispatched() async {
    // GIVEN
    let shopUsps = ShopUsps.stub
    let connector = ShopUspsView.ViewState.Connector(shopUsps: shopUsps)
    var dispatchedEvent: ShopUspsView.Event?
    
    let dispatch: (ShopUspsView.Event) async -> Void = { event in
      dispatchedEvent = event
    }
    
    // WHEN
    await connector.configure(dispatch: dispatch)
    
    // THEN
    guard case let ._received(receivedShopUsps) = dispatchedEvent else {
      XCTFail("Expected _received event to be dispatched")
      return
    }
    XCTAssertEqual(receivedShopUsps.isLoading, shopUsps.isLoading)
    XCTAssertEqual(receivedShopUsps.content.paybackPoints, shopUsps.content.paybackPoints)
    XCTAssertEqual(receivedShopUsps.config.paybackPointsIndex, shopUsps.config.paybackPointsIndex)
  }
  
  /// Test: GIVEN connector with loading shopUsps, WHEN configure is called, THEN _received event with loading shopUsps is dispatched
  func test_GIVEN_connectorWithLoadingShopUsps_WHEN_configureIsCalled_THEN_receivedEventWithLoadingShopUspsIsDispatched() async {
    // GIVEN
    let shopUsps = ShopUsps.stubLoading
    let connector = ShopUspsView.ViewState.Connector(shopUsps: shopUsps)
    var dispatchedEvent: ShopUspsView.Event?
    
    let dispatch: (ShopUspsView.Event) async -> Void = { event in
      dispatchedEvent = event
    }
    
    // WHEN
    await connector.configure(dispatch: dispatch)
    
    // THEN
    guard case let ._received(receivedShopUsps) = dispatchedEvent else {
      XCTFail("Expected _received event to be dispatched")
      return
    }
    XCTAssertTrue(receivedShopUsps.isLoading)
  }
  
  /// Test: GIVEN connector with shopUsps without payback, WHEN configure is called, THEN _received event with shopUsps without payback is dispatched
  func test_GIVEN_connectorWithShopUspsWithoutPayback_WHEN_configureIsCalled_THEN_receivedEventWithShopUspsWithoutPaybackIsDispatched() async {
    // GIVEN
    let shopUsps = ShopUsps.stubWithoutPayback
    let connector = ShopUspsView.ViewState.Connector(shopUsps: shopUsps)
    var dispatchedEvent: ShopUspsView.Event?
    
    let dispatch: (ShopUspsView.Event) async -> Void = { event in
      dispatchedEvent = event
    }
    
    // WHEN
    await connector.configure(dispatch: dispatch)
    
    // THEN
    guard case let ._received(receivedShopUsps) = dispatchedEvent else {
      XCTFail("Expected _received event to be dispatched")
      return
    }
    XCTAssertNil(receivedShopUsps.content.paybackPoints)
  }
  
  /// Test: GIVEN multiple calls to configure, WHEN configure is called multiple times, THEN _received event is dispatched each time
  func test_GIVEN_multipleCalls_WHEN_configureIsCalledMultipleTimes_THEN_receivedEventIsDispatchedEachTime() async {
    // GIVEN
    let shopUsps = ShopUsps.stub
    let connector = ShopUspsView.ViewState.Connector(shopUsps: shopUsps)
    var dispatchCallCount = 0
    
    let dispatch: (ShopUspsView.Event) async -> Void = { _ in
      dispatchCallCount += 1
    }
    
    // WHEN
    await connector.configure(dispatch: dispatch)
    await connector.configure(dispatch: dispatch)
    await connector.configure(dispatch: dispatch)
    
    // THEN
    XCTAssertEqual(dispatchCallCount, 3)
  }
}
