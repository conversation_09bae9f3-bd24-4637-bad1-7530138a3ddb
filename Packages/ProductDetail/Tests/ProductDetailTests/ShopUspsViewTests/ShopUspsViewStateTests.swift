import XCTest
@testable import ProductDetail

final class ShopUspsViewStateTests: XCTestCase {
  
  /// Test: GIVEN initial state without shopUsps, WHEN usps is accessed, THEN only default items are returned
  func test_GIVEN_initialStateWithoutShopUsps_WHEN_uspsIsAccessed_THEN_onlyDefaultItemsReturned() {
    // GIVEN
    let state = ShopUspsView.ViewState.initial
    
    // WHEN
    let usps = state.usps
    
    // THEN
    XCTAssertEqual(usps.count, 3) // Only default items
    XCTAssertTrue(usps.allSatisfy { $0.icon.hasPrefix("icon24x24PdpUsp") })
  }
  
  /// Test: GIVEN state with shopUsps without payback points, WHEN usps is accessed, THEN only default items are returned
  func test_GIVEN_stateWithShopUspsWithoutPaybackPoints_WHEN_uspsIsAccessed_THEN_onlyDefaultItemsReturned() {
    // GIVEN
    let state = ShopUspsView.ViewState(isLoading: false, shopUsps: ShopUsps.stubWithoutPayback)
    
    // WHEN
    let usps = state.usps
    
    // THEN
    XCTAssertEqual(usps.count, 3) // Only default items
    XCTAssertTrue(usps.allSatisfy { $0.icon.hasPrefix("icon24x24PdpUsp") })
  }
  
  /// Test: GIVEN state with shopUsps with payback points and valid index, WHEN usps is accessed, THEN payback item is inserted at correct index
  func test_GIVEN_stateWithShopUspsWithPaybackPointsAndValidIndex_WHEN_uspsIsAccessed_THEN_paybackItemInsertedAtCorrectIndex() {
    // GIVEN
    let shopUsps = ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 100),
      config: ShopUsps.Config(paybackPointsIndex: 1)
    )
    let state = ShopUspsView.ViewState(isLoading: false, shopUsps: shopUsps)
    
    // WHEN
    let usps = state.usps
    
    // THEN
    XCTAssertEqual(usps.count, 4) // 3 default + 1 payback
    XCTAssertEqual(usps[1].icon, "icon24x24Payback") // Payback item at index 1
    XCTAssertTrue(usps[1].text.contains("100"))
  }
  
  /// Test: GIVEN state with shopUsps with payback points and index 0, WHEN usps is accessed, THEN payback item is inserted at beginning
  func test_GIVEN_stateWithShopUspsWithPaybackPointsAndIndexZero_WHEN_uspsIsAccessed_THEN_paybackItemInsertedAtBeginning() {
    // GIVEN
    let shopUsps = ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 250),
      config: ShopUsps.Config(paybackPointsIndex: 0)
    )
    let state = ShopUspsView.ViewState(isLoading: false, shopUsps: shopUsps)
    
    // WHEN
    let usps = state.usps
    
    // THEN
    XCTAssertEqual(usps.count, 4) // 3 default + 1 payback
    XCTAssertEqual(usps[0].icon, "icon24x24Payback") // Payback item at index 0
    XCTAssertTrue(usps[0].text.contains("250"))
  }
  
  /// Test: GIVEN state with shopUsps with payback points and index beyond bounds, WHEN usps is accessed, THEN payback item is appended at end
  func test_GIVEN_stateWithShopUspsWithPaybackPointsAndIndexBeyondBounds_WHEN_uspsIsAccessed_THEN_paybackItemAppendedAtEnd() {
    // GIVEN
    let shopUsps = ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 75),
      config: ShopUsps.Config(paybackPointsIndex: 10)
    )
    let state = ShopUspsView.ViewState(isLoading: false, shopUsps: shopUsps)
    
    // WHEN
    let usps = state.usps
    
    // THEN
    XCTAssertEqual(usps.count, 4) // 3 default + 1 payback
    XCTAssertEqual(usps[3].icon, "icon24x24Payback") // Payback item at end
    XCTAssertTrue(usps[3].text.contains("75"))
  }
  
  /// Test: GIVEN state with shopUsps with payback points and negative index, WHEN usps is accessed, THEN payback item is appended at end
  func test_GIVEN_stateWithShopUspsWithPaybackPointsAndNegativeIndex_WHEN_uspsIsAccessed_THEN_paybackItemAppendedAtEnd() {
    // GIVEN
    let shopUsps = ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 50),
      config: ShopUsps.Config(paybackPointsIndex: -1)
    )
    let state = ShopUspsView.ViewState(isLoading: false, shopUsps: shopUsps)
    
    // WHEN
    let usps = state.usps
    
    // THEN
    XCTAssertEqual(usps.count, 4) // 3 default + 1 payback
    XCTAssertEqual(usps[3].icon, "icon24x24Payback") // Payback item at end
    XCTAssertTrue(usps[3].text.contains("50"))
  }
  
  /// Test: GIVEN state with shopUsps with zero payback points, WHEN usps is accessed, THEN payback item is included with zero points
  func test_GIVEN_stateWithShopUspsWithZeroPaybackPoints_WHEN_uspsIsAccessed_THEN_paybackItemIncludedWithZeroPoints() {
    // GIVEN
    let shopUsps = ShopUsps(
      isLoading: false,
      content: ShopUsps.Content(paybackPoints: 0),
      config: ShopUsps.Config(paybackPointsIndex: 2)
    )
    let state = ShopUspsView.ViewState(isLoading: false, shopUsps: shopUsps)
    
    // WHEN
    let usps = state.usps
    
    // THEN
    XCTAssertEqual(usps.count, 4) // 3 default + 1 payback
    XCTAssertEqual(usps[2].icon, "icon24x24Payback") // Payback item at index 2
    XCTAssertTrue(usps[2].text.contains("0"))
  }
  
  /// Test: GIVEN state with shopUsps, WHEN update is called with new shopUsps, THEN state is updated correctly
  func test_GIVEN_stateWithShopUsps_WHEN_updateIsCalledWithNewShopUsps_THEN_stateIsUpdatedCorrectly() {
    // GIVEN
    var state = ShopUspsView.ViewState(isLoading: true, shopUsps: ShopUsps.stubLoading)
    let newShopUsps = ShopUsps.stub
    
    // WHEN
    state.update(shopUsps: newShopUsps)
    
    // THEN
    XCTAssertFalse(state.isLoading)
    XCTAssertEqual(state.usps.count, 4) // Updated with new shopUsps
  }
  
  /// Test: GIVEN initial state, WHEN init with custom parameters, THEN state is initialized correctly
  func test_GIVEN_initialState_WHEN_initWithCustomParameters_THEN_stateIsInitializedCorrectly() {
    // GIVEN & WHEN
    let shopUsps = ShopUsps.stub
    let state = ShopUspsView.ViewState(isLoading: false, shopUsps: shopUsps)
    
    // THEN
    XCTAssertFalse(state.isLoading)
    XCTAssertEqual(state.usps.count, 4) // 3 default + 1 payback
  }
}
