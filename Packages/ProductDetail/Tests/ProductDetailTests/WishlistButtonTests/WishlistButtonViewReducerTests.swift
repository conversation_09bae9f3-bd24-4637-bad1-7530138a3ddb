import OGAppKitSDK
import XCTest
@testable import ProductDetail

final class WishlistButtonViewReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_eventReceived_THEN_noStateChange() throws {
    var state = WishlistButtonView.ViewState.initial
    WishlistButtonView.ViewState.Reducer.reduce(&state, with: ._addToWishlist(.stub))
    XCTAssertEqual(state, WishlistButtonView.ViewState.initial)
  }

  func test_GIVEN_state_WHEN_addToWishlist_THEN_stateUnchanged() throws {
    var state = WishlistButtonView.ViewState.initial
    WishlistButtonView.ViewState.Reducer.reduce(&state, with: ._addToWishlist(.stub))
    XCTAssertEqual(state, WishlistButtonView.ViewState.initial)
  }

  func test_GIVEN_state_WHEN_removeFromWishlist_THEN_stateUnchanged() throws {
    var state = WishlistButtonView.ViewState.initial
    WishlistButtonView.ViewState.Reducer.reduce(&state, with: ._removeFromWishlist(.stub))
    XCTAssertEqual(state, WishlistButtonView.ViewState.initial)
  }
}
