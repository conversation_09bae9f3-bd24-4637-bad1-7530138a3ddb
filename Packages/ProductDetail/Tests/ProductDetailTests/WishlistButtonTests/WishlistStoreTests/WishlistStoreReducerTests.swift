import XCTest
@testable import ProductDetail

final class WishlistStoreReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_addToWishlist_THEN_isAwaitingUpdateTrue() {
    // GIVEN
    var state = WishlistState()

    // WHEN
    WishlistState.Reducer.reduce(&state, with: .addToWishlist(.stub))

    // THEN
    XCTAssertTrue(state.isAwaitingUpdate)
  }

  func test_GIVEN_initialState_WHEN_removeFromWishlist_THEN_isAwaitingUpdateFalse() {
    // GIVEN
    var state = WishlistState()

    // WHEN
    WishlistState.Reducer.reduce(&state, with: .removeFromWishlist(.stub))

    // THEN
    XCTAssertTrue(state.isAwaitingUpdate)
  }
}
