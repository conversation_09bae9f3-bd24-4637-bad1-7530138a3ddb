import Combine
import OGCoreTestsUtils
import OGRouter
import XCTest
@testable import ProductDetail

final class WishlistStoreMiddlewareTests: XCTestCase {
  func test_GIVEN_validResponse_WHEN_addToWishlist_THEN_addProductToWishlistCalls() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = WishlistState.Middleware(service: mockService)
    let state = WishlistState()
    mockService.mock.addProductToWishlistCalls.mockCall { _ in
      [.stub, .stub]
    }
    // WHEN
    let action = try? await sut.callAsFunction(action: .addToWishlist(.stub), for: state)

    // THEN
    XCTAssertEqual(mockService.mock.addProductToWishlistCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.addProductToWishlistCalls.latestCall, .stub)
  }

  func test_GIVEN_validResponse_WHEN_removeFromWishlist_THEN_removeProductFromWishlistCalls() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = WishlistState.Middleware(service: mockService)
    let state = WishlistState()
    mockService.mock.removeProductFromWishlistCalls.mockCall { _ in
      [.stub, .stub]
    }
    // WHEN
    let action = try? await sut.callAsFunction(action: .removeFromWishlist(.stub), for: state)

    // THEN
    XCTAssertEqual(mockService.mock.removeProductFromWishlistCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.removeProductFromWishlistCalls.latestCall, .stub)
  }
}
