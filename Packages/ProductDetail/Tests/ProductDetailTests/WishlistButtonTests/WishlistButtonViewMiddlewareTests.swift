import Combine
import O<PERSON>pp<PERSON><PERSON>SDK
import OGRouter
import OGRouterTestsUtils
import XCTest
@testable import ProductDetail

final class WishlistButtonViewMiddlewareTests: XCTestCase {
  func test_GIVEN_toggleWishlist_WHEN_idIsPresent_THEN_dispatchCorrectEvent() async {
    let middlewareMock = WishlistStoreMiddlewareMock()
    let wishlistStore = WishlistStore(
      reducer: WishlistStoreReducerMock.reduce,
      middlewares: middlewareMock
    )
    let routerMock = OGRoutePublisherMock()
    let sut = WishlistButtonView.ViewState.Middleware(wishlistStore: wishlistStore, router: routerMock)
    var state = WishlistButtonView.ViewState()
    state.update(productID: .stub)

    let addEvent = await sut.callAsFunction(event: .toggleWishlist(false), for: state)
    let removeEvent = await sut.callAsFunction(event: .toggleWishlist(true), for: state)

    XCTAssertEqual(addEvent, ._addToWishlist(.stub))
    XCTAssertEqual(removeEvent, ._removeFromWishlist(.stub))
  }

  func test_GIVEN_toggleWishlist_WHEN_idIsMissing_THEN_dispatchNoEvent() async {
    let wishlistStore = WishlistStore(
      reducer: WishlistStoreReducerMock.reduce,
      middlewares: WishlistStoreMiddlewareMock()
    )
    let routerMock = OGRoutePublisherMock()
    let sut = WishlistButtonView.ViewState.Middleware(wishlistStore: wishlistStore, router: routerMock)

    let event = await sut.callAsFunction(event: .toggleWishlist(false), for: .initial)

    XCTAssertNil(event)
  }
}
