import Combine
import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class WishlistButtonViewConnectorTests: XCTestCase {
  func test_GIVEN_connector_WHEN_configureCalled_THEN_dispatchFailedEvent() async {
    await withMainSerialExecutor {
      // Arrange
      let serviceMock = ProductDetailServiceMock()
      let middlewareMock = WishlistState.Middleware(service: serviceMock)
      let wishlistStore = WishlistStore(
        reducer: WishlistStoreReducerMock.reduce,
        middlewares: middlewareMock
      )
      serviceMock.mock.addProductToWishlistCalls.mockThrowCall(WishlistError.addToWishlistFailed(.stub))

      let sut = WishlistButtonView.ViewState.Connector(wishlistStore: wishlistStore)
      let expectation = expectation(description: "Expected _failed event to be dispatched")

      var dispatchedEvents: [WishlistButtonView.Event] = []
      let dispatch: (WishlistButtonView.Event) async -> Void = { event in
        dispatchedEvents.append(event)
        if case ._failed = event {
          expectation.fulfill()
        }
      }

      // Act
      await sut.configure(dispatch: dispatch)
      await wishlistStore.dispatch(.addToWishlist(.stub))
      // Assert
      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertTrue(dispatchedEvents.contains { if case ._failed = $0 { return true } else { return false } })
    }
  }
}
