import OGAppKitSDK
import XCTest

@testable import ProductDetail

final class ProductReviewsViewReducerTests: XCTestCase {
  func test_WHEN_setReviews_THEN_stateUpdatesCorrectly() throws {
    // Given
    var initial = ProductReviewsView.ViewState.initial
    let mockReviews = ProductReviews.stub

    // When
    ProductReviewsView.ViewState.Reducer.reduce(&initial, with: ._setReviews(with: mockReviews))

    // Then
    let expected = ProductReviewsView.ViewState(
      averageRating: 4.5,
      totalReviews: 10,
      reviews: (mockReviews.content as? ProductReviewsContentReviews)?.reviews ?? [],
      allReviewsUrl: "https://example.com/reviews/123"
    )

    XCTAssertEqual(initial.averageRating, expected.averageRating)
    XCTAssertEqual(initial.totalReviews, expected.totalReviews)
    XCTAssertEqual(initial.reviews.count, expected.reviews.count)
    XCTAssertEqual(initial.allReviewsUrl, expected.allReviewsUrl)
  }

  func test_WHEN_setReviewsWithEmptyContent_THEN_stateRemainsUnchanged() throws {
    // Given
    var initial = ProductReviewsView.ViewState.initial
    let mockReviews = ProductReviews(
      state: LoadingComponentStateDone(content: ProductReviews_ContentEmpty.stub),
      config: .stub
    )

    // When
    ProductReviewsView.ViewState.Reducer.reduce(&initial, with: ._setReviews(with: mockReviews))

    // Then
    XCTAssertEqual(initial.reviews.isEmpty, ProductReviewsView.ViewState.initial.reviews.isEmpty)
  }
}
