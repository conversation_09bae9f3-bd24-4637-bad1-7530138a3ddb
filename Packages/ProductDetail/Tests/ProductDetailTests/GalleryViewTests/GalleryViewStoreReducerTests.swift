import OGAppKitSDK
import XCTest
@testable import ProductDetail

final class GalleryViewStoreReducerTests: XCTestCase {
  /// Test: Reducer updates ViewState correctly when new URLs are received
  func test_GIVEN_initialState_WHEN_urlsReceived_THEN_updateUrlsAndImageCount() throws {
    var state = GalleryView.ViewState.initial
    let urls = [
      URL(string: "https://example.com/image1.jpg")!,
      URL(string: "https://example.com/image2.jpg")!
    ]
    let gallery = ProductGallery(
      state: LoadingComponentStateDone(
        content: ProductGallery
          .Content(
            images: [
              OGAppKitSDK.Image(url: urls[0].absoluteString, thumbnailUrl: .stub),
              OGAppKitSDK.Image(url: urls[1].absoluteString, thumbnailUrl: .stub)
            ],
            flags: [],
            isWishlisted: false,
            productIdForWishlisting: .stub
          )
      )
    )
    GalleryView.ViewState.Reducer.reduce(&state, with: ._received(gallery))

    XCTAssertEqual(state.urls, urls)
    XCTAssertEqual(state.numberOfImages, urls.count)
    XCTAssertTrue(state.shouldShowImageCount)
  }

  /// Test: Reducer updates current image index correctly
  func test_GIVEN_initialState_WHEN_indexOfCurrentImageUpdated_THEN_updateImageIndex() throws {
    var state = GalleryView.ViewState.initial

    GalleryView.ViewState.Reducer.reduce(&state, with: .indexOfCurrentImage(1))

    XCTAssertEqual(state.indexOfCurrentImage, 1)
    XCTAssertEqual(state.numberOfCurrentImage, 2) // 0-based index
  }

  /// Test: Reducer updates scale and visibility of image count based on scale
  func test_GIVEN_currentScale_WHEN_scaleUpdated_THEN_updateScaleAndVisibility() throws {
    var state = GalleryView.ViewState.initial

    GalleryView.ViewState.Reducer.reduce(&state, with: .currentScale(0.8))

    XCTAssertEqual(state.currentScale, 0.8)
    XCTAssertTrue(state.shouldShowImageCount)

    GalleryView.ViewState.Reducer.reduce(&state, with: .currentScale(1.2))

    XCTAssertEqual(state.currentScale, 1.2)
    XCTAssertFalse(state.shouldShowImageCount)
  }

  /// Test: Reducer does nothing on certain events (e.g., dismiss)
  func test_GIVEN_initialState_WHEN_dismissEventTriggered_THEN_noStateChange() throws {
    var state = GalleryView.ViewState.initial

    // Pass dismiss event, expecting no change in state
    GalleryView.ViewState.Reducer.reduce(&state, with: .dismiss)

    XCTAssertEqual(state, GalleryView.ViewState.initial)
  }

  func test_GIVEN_initialState_WHEN_urlsAndFlagsReceived_THEN_updateUrlsAndFlags() throws {
    var state = GalleryView.ViewState.initial
    let urls = [
      URL(string: "https://example.com/image1.jpg")!,
      URL(string: "https://example.com/image2.jpg")!
    ]
    let flags = [
      Flag(
        type: .sale
      ),
      Flag(
        type: .theNew
      )
    ]
    let gallery = ProductGallery(
      state: LoadingComponentStateDone(
        content: ProductGallery
          .Content(
            images: [
              OGAppKitSDK.Image(url: urls[0].absoluteString, thumbnailUrl: .stub),
              OGAppKitSDK.Image(url: urls[1].absoluteString, thumbnailUrl: .stub)
            ],
            flags: flags,
            isWishlisted: false,
            productIdForWishlisting: .stub
          )
      )
    )
    GalleryView.ViewState.Reducer.reduce(&state, with: ._received(gallery))

    XCTAssertEqual(state.urls, urls)
    XCTAssertEqual(state.flags.count, flags.count)
    XCTAssertEqual(state.flags[0].type, flags[0].type)
    XCTAssertEqual(state.numberOfImages, urls.count)
    XCTAssertTrue(state.shouldShowImageCount)
  }
}
