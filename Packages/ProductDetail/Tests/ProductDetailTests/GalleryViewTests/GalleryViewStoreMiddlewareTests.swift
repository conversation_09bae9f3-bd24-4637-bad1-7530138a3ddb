import Combine
import <PERSON>GAppKitSDK
import OGRouter
import OGRouterTestsUtils
import XCTest
@testable import ProductDetail

final class GalleryViewStoreMiddlewareTests: XCTestCase {
  /// Test: Middleware does not handle receiving ProductGallery
  func test_GIVEN_initialState_WHEN_receivedProductGallery_THEN_noNextEvent() async {
    // Mocking a product gallery
    let productGallery = ProductGallery(
      state: LoadingComponentStateDone(
        content: ProductGallery
          .Content(
            images: [
              OGAppKitSDK.Image(url: .stub, thumbnailUrl: .stub),
              OGAppKitSDK.Image(url: .stub, thumbnailUrl: .stub)
            ],
            flags: [],
            isWishlisted: false,
            productIdForWishlisting: .stub
          )
      )
    )
    let routerMock = OGRoutePublisherMock()
    let sut = GalleryView.ViewState.Middleware(router: routerMock, indexOfCurrentImagePublisher: nil)

    let nextEvent = await sut.callAsFunction(
      event: ._received(productGallery),
      for: .initial
    )

    XCTAssertNil(nextEvent)
  }

  /// Test: Middleware sends dismiss event correctly
  func test_GIVEN_initialState_WHEN_dismissEventIsTriggered_THEN_routerSendDismissEvent() async {
    let routerMock = OGRoutePublisherMock()

    let sut = GalleryView.ViewState.Middleware(router: routerMock, indexOfCurrentImagePublisher: nil)

    let nextEvent = await sut.callAsFunction(event: .dismiss, for: .initial)

    // Expect the dismiss event to trigger routing
    XCTAssertNil(nextEvent)
    XCTAssertEqual(routerMock.mock.dismissRouteCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.dismissRouteCalls.latestCall?.id, OGRoute.galleryView.id)
  }

  /// Test: Middleware sends swipe dismiss event based on swipe distance
  func test_GIVEN_swipeExceedsDismissThreshold_WHEN_onSwipe_THEN_triggerDismiss() async {
    let routerMock = OGRoutePublisherMock()
    let sut = GalleryView.ViewState.Middleware(router: routerMock, indexOfCurrentImagePublisher: nil)

    // Simulate a swipe action
    let nextEvent = await sut.callAsFunction(
      event: .onSwipe(
        endLocation: CGPoint(x: 0, y: 200),
        startLocation: CGPoint(x: 0, y: 0)
      ),
      for: GalleryView.ViewState(
        currentScale: 1, swipeDistanceToDismiss: 150
      )
    )

    // Expect a dismiss event after swipe
    XCTAssertEqual(nextEvent, .dismiss)
  }

  /// Test: Middleware handles tap on image without triggering further actions
  func test_GIVEN_imageTapped_WHEN_tappedImage_THEN_routerSendGalleryView() async {
    let routerMock = OGRoutePublisherMock()

    let sut = GalleryView.ViewState.Middleware(router: routerMock, indexOfCurrentImagePublisher: nil)

    let nextEvent = await sut.callAsFunction(event: .tapedImage, for: .initial)

    XCTAssertEqual(nextEvent, ._trackInteraction)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall?.url.absoluteString, OGRoute.galleryView.url.absoluteString)
  }

  /// Test: Middleware sends current image index update to publisher
  func test_GIVEN_indexOfCurrentImageUpdated_WHEN_indexOfCurrentImageEvent_THEN_publisherSendsNewIndex() async {
    let indexPublisher = CurrentValueSubject<Decodable, Never>(0)
    let routerMock = OGRoutePublisherMock()
    let sut = GalleryView.ViewState.Middleware(router: routerMock, indexOfCurrentImagePublisher: indexPublisher)

    let nextEvent = await sut.callAsFunction(event: .indexOfCurrentImage(2), for: GalleryView.ViewState(isFullscreen: true))

    XCTAssertNil(nextEvent)
    XCTAssertEqual(indexPublisher.value as? Int, 2)
  }

  /// Test: Middleware handles currentScale event without sending an event
  func test_GIVEN_currentScale_WHEN_currentScaleEvent_THEN_noFurtherEvent() async {
    let routerMock = OGRoutePublisherMock()
    let sut = GalleryView.ViewState.Middleware(router: routerMock, indexOfCurrentImagePublisher: nil)

    let nextEvent = await sut.callAsFunction(event: .currentScale(1.5), for: .initial)

    XCTAssertNil(nextEvent)
  }

  /// Test: Middleware handles onSwipe event without triggering dismiss when scale != 1
  func test_GIVEN_swipeBelowDismissThreshold_WHEN_onSwipe_THEN_noDismissTriggered() async {
    let routerMock = OGRoutePublisherMock()
    let sut = GalleryView.ViewState.Middleware(router: routerMock, indexOfCurrentImagePublisher: nil)

    let nextEvent = await sut.callAsFunction(
      event: .onSwipe(
        endLocation: CGPoint(x: 0, y: 100),
        startLocation: CGPoint(x: 0, y: 0)
      ),
      for: GalleryView.ViewState(
        currentScale: 1, swipeDistanceToDismiss: 150
      )
    )

    XCTAssertNil(nextEvent)
  }
}
