import Combine
import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class GalleryViewStoreConnectorTests: XCTestCase {
  /// Test: When configure is called, should dispatch the _received event with the gallery
  func test_GIVEN_connector_WHEN_configureCalled_THEN_dispatchReceivedEvent() async {
    await withMainSerialExecutor {
      // Arrange
      let gallery = ProductGallery(
        state: LoadingComponentStateDone(
          content: ProductGallery
            .Content(
              images: [],
              flags: [],
              isWishlisted: false,
              productIdForWishlisting: .stub
            )
        )
      )
      let sut = GalleryView.ViewState.Connector(gallery: gallery)

      let expectation = expectation(description: "Expected _received event to be dispatched")

      // Act
      var dispatchedEvents: [GalleryView.Event] = []
      let dispatch: (GalleryView.Event) async -> Void = { event in
        dispatchedEvents.append(event)
        if case ._received = event {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      // Assert
      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertTrue(dispatchedEvents.contains(where: {
        if case let ._received(receivedGallery) = $0 {
          return receivedGallery === gallery
        }
        return false
      }))
    }
  }

  /// Test: When the index publisher updates, it dispatches the indexOfCurrentImage event
  func test_GIVEN_indexPublisher_WHEN_indexChanges_THEN_dispatchIndexOfCurrentImage() async {
    await withMainSerialExecutor {
      // Arrange
      let gallery = ProductGallery(
        state: LoadingComponentStateDone(
          content: ProductGallery
            .Content(
              images: [],
              flags: [],
              isWishlisted: false,
              productIdForWishlisting: .stub
            )
        )
      )
      let indexPublisher = CurrentValueSubject<Decodable, Never>(0)
      let sut = GalleryView.ViewState.Connector(gallery: gallery, indexOfCurrentImagePublisher: indexPublisher)

      let expectation = expectation(description: "Expected indexOfCurrentImage event to be dispatched")

      var dispatchedEvents: [GalleryView.Event] = []
      let dispatch: (GalleryView.Event) async -> Void = { event in

        if case .indexOfCurrentImage = event {
          dispatchedEvents.append(event)
          if dispatchedEvents.count == 2 {
            expectation.fulfill()
          }
        }
      }

      // Act: Call configure and simulate a publisher change
      await sut.configure(dispatch: dispatch)
      indexPublisher.send(2) // Simulate index change to 2

      // Assert: Verify the event is dispatched
      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertTrue(dispatchedEvents.contains(where: {
        if case let .indexOfCurrentImage(index) = $0 {
          return index == 2
        }
        return false
      }))
    }
  }

  /// Test: When index publisher is nil, no indexOfCurrentImage event should be dispatched
  func test_GIVEN_nilIndexPublisher_WHEN_configureCalled_THEN_noIndexOfCurrentImageEvent() async {
    await withMainSerialExecutor {
      // Arrange
      let gallery = ProductGallery(
        state: LoadingComponentStateDone(
          content: ProductGallery
            .Content(
              images: [],
              flags: [],
              isWishlisted: false,
              productIdForWishlisting: .stub
            )
        )
      )
      let sut = GalleryView.ViewState.Connector(gallery: gallery, indexOfCurrentImagePublisher: nil)

      let expectation = expectation(description: "Expected _received event to be dispatched, but no indexOfCurrentImage event")
      expectation.expectedFulfillmentCount = 1 // Only expect _received to be dispatched

      var dispatchedEvents: [GalleryView.Event] = []
      let dispatch: (GalleryView.Event) async -> Void = { event in
        dispatchedEvents.append(event)
        if case ._received = event {
          expectation.fulfill()
        }
      }

      // Act: Call configure
      await sut.configure(dispatch: dispatch)

      // Assert: Verify only the _received event is dispatched, no indexOfCurrentImage event
      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertFalse(dispatchedEvents.contains(where: {
        if case .indexOfCurrentImage = $0 {
          return true
        }
        return false
      }))
    }
  }
}
