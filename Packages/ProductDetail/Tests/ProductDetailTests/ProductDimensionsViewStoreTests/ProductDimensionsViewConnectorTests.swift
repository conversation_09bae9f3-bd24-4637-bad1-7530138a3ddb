import Combine
import OGAppKitSDK
import OGCoreTestsUtils
import XCTest

@testable import ProductDetail

final class ProductDimensionsViewConnectorTests: XCTestCase {
  func test_WHEN_configure_THEN_setDimensionsDispatched() async {
    await withMainSerialExecutor {
      // Arrange
      let productDimensions = ProductDimensions.stub()
      let sut = ProductDimensionsView.ViewState.Connector(
        productDimensions: productDimensions,
        selectProduct: CurrentValueSubject<Decodable, Never>("")
      )

      let expectation = XCTestExpectation(description: "setDimensions dispatched")

      // Act
      var dispatchedEvents: [ProductDimensionsView.Event] = []
      let dispatch: (ProductDimensionsView.Event) async -> Void = { event in
        dispatchedEvents.append(event)
        if case ._setDimensions = event {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      // Assert
      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertTrue(dispatchedEvents.contains(where: {
        if case ._setDimensions = $0 {
          return true
        }
        return false
      }))
    }
  }
}
