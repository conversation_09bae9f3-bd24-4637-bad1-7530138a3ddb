import OGAppKitSDK
import XCTest
@testable import ProductDetail

final class ProductDimensionsViewReducerTests: XCTestCase {
  func test_WHEN_setDimensions_THEN_stateUpdated() {
    // Arrange
    var state = ProductDimensionsView.ViewState.stub()
    let productDimensions = ProductDimensions.stub()

    // Act
    ProductDimensionsView.ViewState.Reducer.reduce(&state, with: ._setDimensions(with: productDimensions))

    // Assert
    XCTAssertEqual(state.dimensionTitleName, "Size:")
    XCTAssertEqual(state.variantName, "Small")
  }

  func test_WHEN_showVariantSelection_THEN_noStateChange() {
    // Arrange
    var state = ProductDimensionsView.ViewState.stub()
    let initialState = state

    // Act
    ProductDimensionsView.ViewState.Reducer.reduce(&state, with: .showVariantSelection)

    // Assert
    XCTAssertEqual(state, initialState)
  }
}
