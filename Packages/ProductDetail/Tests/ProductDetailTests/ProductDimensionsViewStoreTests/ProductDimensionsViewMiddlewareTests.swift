import Combine
import OGAppKitSDK
import OGRouterTestsUtils
import XCTest

@testable import ProductDetail

final class ProductDimensionsViewMiddlewareTests: XCTestCase {
  func test_WHEN_showVariantSelection_THEN_trackInteraction() async {
    // Arrange
    let expectation = XCTestExpectation(description: "showVariantSelection called")
    let showVariantSelection: () -> Void = {
      expectation.fulfill()
    }
    let sut = ProductDimensionsView.ViewState.Middleware(didSelectSize: nil, showVariantSelection: showVariantSelection, selectProduct: CurrentValueSubject<Decodable, Never>(""))
    let state = ProductDimensionsView.ViewState.stub()

    // Act
    let nextEvent = await sut.callAsFunction(event: .showVariantSelection, for: state)

    // Assert
    await fulfillment(of: [expectation], timeout: 0.1)
    XCTAssertEqual(nextEvent, ._trackInteraction)
  }

  func test_WHEN_setDimensions_THEN_noActionTaken() async {
    // Arrange
    let sut = ProductDimensionsView.ViewState.Middleware(didSelectSize: nil, showVariantSelection: nil, selectProduct: CurrentValueSubject<Decodable, Never>(""))
    let dimensions = ProductDimensions.stub()

    // Act
    let nextEvent = await sut.callAsFunction(event: ._setDimensions(with: dimensions), for: .stub())

    // Assert
    XCTAssertNil(nextEvent)
  }
}
