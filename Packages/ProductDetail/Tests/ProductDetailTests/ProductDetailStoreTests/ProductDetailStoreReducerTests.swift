import XCTest
@testable import ProductDetail

final class ProductDetailStoreReducerTests: XCTestCase {
  /// Test: GIVEN initial state, WHEN getDetailScreen is dispatched, THEN the state updates with isAwaitingUpdate = true
  func test_GIVEN_initialState_WHEN_getDetailScreen_THEN_isAwaitingUpdateTrue() {
    // GIVEN
    var state = ProductDetailState()

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: .getDetailScreen)

    // THEN
    XCTAssertTrue(state.isAwaitingUpdate)
  }

  /// Test: GIVEN initial state, WHEN _setDetailScreen is dispatched, THEN the state updates components and isAwaitingUpdate = false
  func test_GIVEN_initialState_WHEN_setDetailScreen_THEN_componentsUpdate_and_isAwaitingUpdateFalse() {
    // GIVEN
    var state = ProductDetailState(isAwaitingUpdate: true)
    let components: [OGProductDetailComponent] = []

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._setDetailScreen(components))

    // THEN
    XCTAssertEqual(state.components, components)
    XCTAssertFalse(state.isAwaitingUpdate)
  }

  /// Test: GIVEN initial state, WHEN _setDetailScreen with stub components is dispatched, THEN header and basket are extracted
  func test_GIVEN_initialState_WHEN_setDetailScreenWithStubComponents_THEN_headerAndBasketExtracted() {
    // GIVEN
    var state = ProductDetailState(isAwaitingUpdate: true)
    let components: [OGProductDetailComponent] = .stub

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._setDetailScreen(components))

    // THEN
    XCTAssertFalse(state.isAwaitingUpdate)
    XCTAssertNotNil(state.header)
    XCTAssertNotNil(state.basket)
    XCTAssertEqual(state.components, components.componentsWithoutHeader)
  }

  /// Test: GIVEN initial state, WHEN _setComponentConfigsJson is dispatched, THEN componentConfigsJson is updated
  func test_GIVEN_initialState_WHEN_setComponentConfigsJson_THEN_componentConfigsJsonUpdates() {
    // GIVEN
    var state = ProductDetailState(componentConfigsJson: "")
    let newConfigJson = "{\"key\":\"value\"}"

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._setComponentConfigsJson(newConfigJson))

    // THEN
    XCTAssertEqual(state.componentConfigsJson, newConfigJson)
  }

  /// Test: GIVEN initial state, WHEN _throwError is dispatched, THEN isAwaitingUpdate is set to false
  func test_GIVEN_initialState_WHEN_throwError_THEN_isAwaitingUpdateFalse() {
    // GIVEN
    var state = ProductDetailState(isAwaitingUpdate: true)

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._throwError(.notFound))

    // THEN
    XCTAssertFalse(state.isAwaitingUpdate)
  }

  /// Test: GIVEN initial state, WHEN _setScreenId is dispatched, THEN screenId is updated
  func test_GIVEN_initialState_WHEN_setScreenId_THEN_screenIdUpdates() {
    // GIVEN
    var state = ProductDetailState(screenId: nil)
    let newScreenId = "test-screen-id"

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._setScreenId(newScreenId))

    // THEN
    XCTAssertEqual(state.screenId, newScreenId)
  }

  /// Test: GIVEN state with different componentConfigsJson, WHEN _setComponentConfigsJson is dispatched, THEN componentConfigsDidChange is true
  func test_GIVEN_differentComponentConfigsJson_WHEN_setComponentConfigsJson_THEN_componentConfigsDidChangeTrue() {
    // GIVEN
    var state = ProductDetailState(componentConfigsJson: "{\"old\":\"value\"}")
    let newConfigJson = "{\"new\":\"value\"}"

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._setComponentConfigsJson(newConfigJson))

    // THEN
    XCTAssertEqual(state.componentConfigsJson, newConfigJson)
    XCTAssertTrue(state.componentConfigsDidChange)
  }

  /// Test: GIVEN state with same componentConfigsJson, WHEN _setComponentConfigsJson is dispatched, THEN componentConfigsDidChange is false
  func test_GIVEN_sameComponentConfigsJson_WHEN_setComponentConfigsJson_THEN_componentConfigsDidChangeFalse() {
    // GIVEN
    let configJson = "{\"key\":\"value\"}"
    var state = ProductDetailState(componentConfigsJson: configJson)

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._setComponentConfigsJson(configJson))

    // THEN
    XCTAssertEqual(state.componentConfigsJson, configJson)
    XCTAssertFalse(state.componentConfigsDidChange)
  }

  /// Test: GIVEN initial state, WHEN getDetailScreenWith is dispatched, THEN state remains unchanged
  func test_GIVEN_initialState_WHEN_getDetailScreenWith_THEN_stateRemainsUnchanged() {
    // GIVEN
    var state = ProductDetailState()
    let initialState = state

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: .getDetailScreenWith(id: "test-id", secondaryId: nil))

    // THEN
    XCTAssertEqual(state, initialState)
  }

  /// Test: GIVEN initial state, WHEN updateBadgeCount is dispatched, THEN state remains unchanged
  func test_GIVEN_initialState_WHEN_updateBadgeCount_THEN_stateRemainsUnchanged() {
    // GIVEN
    var state = ProductDetailState()
    let initialState = state

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: .updateBadgeCount)

    // THEN
    XCTAssertEqual(state, initialState)
  }

  /// Test: GIVEN initial state, WHEN updateProductColorSelection is dispatched, THEN state remains unchanged
  func test_GIVEN_initialState_WHEN_updateProductColorSelection_THEN_stateRemainsUnchanged() {
    // GIVEN
    var state = ProductDetailState()
    let initialState = state

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: .updateProductColorSelection(id: "test-id"))

    // THEN
    XCTAssertEqual(state, initialState)
  }

  /// Test: GIVEN initial state, WHEN updateProductVariantSelection is dispatched, THEN state remains unchanged
  func test_GIVEN_initialState_WHEN_updateProductVariantSelection_THEN_stateRemainsUnchanged() {
    // GIVEN
    var state = ProductDetailState()
    let initialState = state

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: .updateProductVariantSelection(id: "test-id"))

    // THEN
    XCTAssertEqual(state, initialState)
  }
}
