import XCTest
@testable import ProductDetail

final class ProductDetailStoreReducerTests: XCTestCase {
  /// Test: GIVEN initial state, WHEN getDetailScreen is dispatched, THEN the state updates with isAwaitingUpdate = true
  func test_GIVEN_initialState_WHEN_getDetailScreen_THEN_isAwaitingUpdateTrue() {
    // GIVEN
    var state = ProductDetailState()

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: .getDetailScreen)

    // THEN
    XCTAssertTrue(state.isAwaitingUpdate)
  }

  /// Test: GIVEN initial state, WHEN _setDetailScreen is dispatched, THEN the state updates components and isAwaitingUpdate = false
  func test_GIVEN_initialState_WHEN_setDetailScreen_THEN_componentsUpdate_and_isAwaitingUpdateFalse() {
    // GIVEN
    var state = ProductDetailState(isAwaitingUpdate: true)
    let components: [OGProductDetailComponent] = []

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._setDetailScreen(components))

    // THEN
    XCTAssertEqual(state.components, components)
    XCTAssertFalse(state.isAwaitingUpdate)
  }

  /// Test: GIVEN initial state, WHEN _setComponentConfigsJson is dispatched, THEN componentConfigsJson is updated
  func test_GIVEN_initialState_WHEN_setComponentConfigsJson_THEN_componentConfigsJsonUpdates() {
    // GIVEN
    var state = ProductDetailState(componentConfigsJson: "")
    let newConfigJson = "{\"key\":\"value\"}"

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._setComponentConfigsJson(newConfigJson))

    // THEN
    XCTAssertEqual(state.componentConfigsJson, newConfigJson)
  }

  /// Test: GIVEN initial state, WHEN _throwError is dispatched, THEN the state remains unchanged
  func test_GIVEN_initialState_WHEN_throwError_THEN_stateRemainsUnchanged() {
    // GIVEN
    var state = ProductDetailState()
    let initialState = state

    // WHEN
    ProductDetailState.Reducer.reduce(&state, with: ._throwError(.notFound))

    // THEN
    XCTAssertEqual(state, initialState)
  }
}
