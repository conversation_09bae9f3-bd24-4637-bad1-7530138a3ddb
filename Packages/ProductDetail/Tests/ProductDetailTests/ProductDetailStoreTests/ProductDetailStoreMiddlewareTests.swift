import Combine
import OGCoreTestsUtils
import OGRouter
import XCTest
@testable import ProductDetail

final class ProductDetailStoreMiddlewareTests: XCTestCase {
  /// Test: GIVEN valid service response, WHEN getDetailScreen is dispatched, THEN it triggers no further action
  func test_GIVEN_validResponse_WHEN_getDetailScreen_THEN_noFurtherAction() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState()

    // WHEN
    let action = try? await sut.callAsFunction(action: .getDetailScreen, for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.fetchProductDetailScreenCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.fetchProductDetailScreenCalls.latestCall?.0, .stub)
    XCTAssertEqual(mockService.mock.fetchProductDetailScreenCalls.latestCall?.1, "")
  }

  /// Test: GIVEN valid service response, WHEN getDetailScreenWith(id:) is dispatched, THEN it triggers no further action
  func test_GIVEN_validResponse_WHEN_getDetailScreenWithId_THEN_noFurtherAction() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState()

    // WHEN
    let action = try? await sut.callAsFunction(action: .getDetailScreenWith(id: .stub, secondaryId: nil), for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.latestCall?.0, .stub)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.latestCall?.1, nil)
  }

  /// Test: GIVEN valid response, WHEN _setComponentConfigsJson is dispatched, THEN it returns getDetailScreen action
  func test_GIVEN_setComponentConfigsJson_WHEN_middlewareCalled_THEN_returnsGetDetailScreen() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState(componentConfigsJson: "{\"key\":\"value\"}", componentConfigsDidChange: true)

    // WHEN
    let action = try? await sut.callAsFunction(action: ._setComponentConfigsJson("{\"key\":\"value\"}"), for: state)

    // THEN
    XCTAssertEqual(action, .getDetailScreen)
  }
}
