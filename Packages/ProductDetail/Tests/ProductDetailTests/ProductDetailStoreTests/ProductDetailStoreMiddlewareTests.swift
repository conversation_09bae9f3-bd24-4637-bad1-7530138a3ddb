import Combine
import OGCoreTestsUtils
import OGRouter
import XCTest
@testable import ProductDetail

final class ProductDetailStoreMiddlewareTests: XCTestCase {
  /// Test: GIVEN valid service response, WHEN getDetailScreen is dispatched, THEN it triggers no further action
  func test_GIVEN_validResponse_WHEN_getDetailScreen_THEN_noFurtherAction() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState()

    // WHEN
    let action = try? await sut.callAsFunction(action: .getDetailScreen, for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.fetchProductDetailScreenCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.fetchProductDetailScreenCalls.latestCall?.0, .stub)
    XCTAssertEqual(mockService.mock.fetchProductDetailScreenCalls.latestCall?.1, "")
  }

  /// Test: GIVEN valid service response, WHEN getDetailScreenWith(id:) is dispatched, THEN it triggers no further action
  func test_GIVEN_validResponse_WHEN_getDetailScreenWithId_THEN_noFurtherAction() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState()

    // WHEN
    let action = try? await sut.callAsFunction(action: .getDetailScreenWith(id: .stub, secondaryId: nil), for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.latestCall?.0, .stub)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.latestCall?.1, nil)
  }

  /// Test: GIVEN valid response, WHEN _setComponentConfigsJson is dispatched, THEN it returns getDetailScreen action
  func test_GIVEN_setComponentConfigsJson_WHEN_middlewareCalled_THEN_returnsGetDetailScreen() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState(componentConfigsJson: "{\"key\":\"value\"}", componentConfigsDidChange: true)

    // WHEN
    let action = try? await sut.callAsFunction(action: ._setComponentConfigsJson("{\"key\":\"value\"}"), for: state)

    // THEN
    XCTAssertEqual(action, .getDetailScreen)
  }

  /// Test: GIVEN state with screenId, WHEN updateProductColorSelection is dispatched, THEN it calls service and returns nil
  func test_GIVEN_stateWithScreenId_WHEN_updateProductColorSelection_THEN_callsServiceAndReturnsNil() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState(screenId: "test-screen-id")

    // WHEN
    let action = try? await sut.callAsFunction(action: .updateProductColorSelection(id: .stub), for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.updateProductColorSelectionCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.updateProductColorSelectionCalls.latestCall?.0, .stub)
    XCTAssertEqual(mockService.mock.updateProductColorSelectionCalls.latestCall?.1, "test-screen-id")
  }

  /// Test: GIVEN state without screenId, WHEN updateProductColorSelection is dispatched, THEN it returns getDetailScreenWith action
  func test_GIVEN_stateWithoutScreenId_WHEN_updateProductColorSelection_THEN_returnsGetDetailScreenWith() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState(screenId: nil)

    // WHEN
    let action = try? await sut.callAsFunction(action: .updateProductColorSelection(id: .stub), for: state)

    // THEN
    XCTAssertEqual(action, .getDetailScreenWith(id: .stub, secondaryId: nil))
    XCTAssertEqual(mockService.mock.updateProductColorSelectionCalls.callsCount, 0)
  }

  /// Test: GIVEN state with screenId, WHEN updateProductVariantSelection is dispatched, THEN it calls service and returns nil
  func test_GIVEN_stateWithScreenId_WHEN_updateProductVariantSelection_THEN_callsServiceAndReturnsNil() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState(screenId: "test-screen-id")

    // WHEN
    let action = try? await sut.callAsFunction(action: .updateProductVariantSelection(id: .stub), for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.updateProductColorSelectionCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.updateProductColorSelectionCalls.latestCall?.0, .stub)
    XCTAssertEqual(mockService.mock.updateProductColorSelectionCalls.latestCall?.1, "test-screen-id")
  }

  /// Test: GIVEN state without screenId, WHEN updateProductVariantSelection is dispatched, THEN it returns getDetailScreenWith action
  func test_GIVEN_stateWithoutScreenId_WHEN_updateProductVariantSelection_THEN_returnsGetDetailScreenWith() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState(screenId: nil)

    // WHEN
    let action = try? await sut.callAsFunction(action: .updateProductVariantSelection(id: .stub), for: state)

    // THEN
    XCTAssertEqual(action, .getDetailScreenWith(id: .stub, secondaryId: nil))
    XCTAssertEqual(mockService.mock.updateProductColorSelectionCalls.callsCount, 0)
  }

  /// Test: GIVEN valid service response, WHEN updateBadgeCount is dispatched, THEN it calls service methods and returns nil
  func test_GIVEN_validResponse_WHEN_updateBadgeCount_THEN_callsServiceMethodsAndReturnsNil() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let mockWishlistStore = WishlistStore(
      reducer: WishlistStoreReducerMock.reduce,
      middlewares: WishlistStoreMiddlewareMock()
    )
    let mockBadgeStore = BadgeStore(
      reducer: BadgeStoreState.Reducer.reduce,
      middlewares: BadgeStoreState.Middleware()
    )
    let sut = ProductDetailState.Middleware(
      service: mockService,
      url: .stub,
      wishlistStore: mockWishlistStore,
      badgeStore: mockBadgeStore
    )
    let state = ProductDetailState()

    // Mock service responses
    mockService.mock.refreshWishlistCountCalls.mockCall { [.stub, .stub] }
    mockService.mock.refreshBasketCountCalls.mockCall { 5 }

    // WHEN
    let action = try? await sut.callAsFunction(action: .updateBadgeCount, for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.refreshWishlistCountCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.refreshBasketCountCalls.callsCount, 1)
  }

  /// Test: GIVEN ProductDetailError, WHEN _throwError is dispatched, THEN it throws the error
  func test_GIVEN_productDetailError_WHEN_throwError_THEN_throwsError() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState()
    let expectedError = ProductDetailError.notFound

    // WHEN & THEN
    do {
      _ = try await sut.callAsFunction(action: ._throwError(expectedError), for: state)
      XCTFail("Expected error to be thrown")
    } catch let error as ProductDetailError {
      XCTAssertEqual(error, expectedError)
    } catch {
      XCTFail("Unexpected error type: \(error)")
    }
  }

  /// Test: GIVEN _setDetailScreen action, WHEN middleware is called, THEN it returns nil
  func test_GIVEN_setDetailScreen_WHEN_middlewareCalled_THEN_returnsNil() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState()
    let components: [OGProductDetailComponent] = .stub

    // WHEN
    let action = try? await sut.callAsFunction(action: ._setDetailScreen(components), for: state)

    // THEN
    XCTAssertNil(action)
  }

  /// Test: GIVEN _setScreenId action, WHEN middleware is called, THEN it returns nil
  func test_GIVEN_setScreenId_WHEN_middlewareCalled_THEN_returnsNil() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState()

    // WHEN
    let action = try? await sut.callAsFunction(action: ._setScreenId(.stub), for: state)

    // THEN
    XCTAssertNil(action)
  }

  /// Test: GIVEN empty componentConfigsJson, WHEN _setComponentConfigsJson is dispatched, THEN it returns nil
  func test_GIVEN_emptyComponentConfigsJson_WHEN_setComponentConfigsJson_THEN_returnsNil() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState(componentConfigsJson: "", componentConfigsDidChange: false)

    // WHEN
    let action = try? await sut.callAsFunction(action: ._setComponentConfigsJson(""), for: state)

    // THEN
    XCTAssertNil(action)
  }

  /// Test: GIVEN componentConfigsDidChange is false, WHEN _setComponentConfigsJson is dispatched, THEN it returns nil
  func test_GIVEN_componentConfigsDidNotChange_WHEN_setComponentConfigsJson_THEN_returnsNil() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub)
    let state = ProductDetailState(componentConfigsJson: "{\"key\":\"value\"}", componentConfigsDidChange: false)

    // WHEN
    let action = try? await sut.callAsFunction(action: ._setComponentConfigsJson("{\"key\":\"value\"}"), for: state)

    // THEN
    XCTAssertNil(action)
  }

  /// Test: GIVEN route data with productId, WHEN getDetailScreen is dispatched, THEN it calls fetchProductDetail
  func test_GIVEN_routeDataWithProductId_WHEN_getDetailScreen_THEN_callsFetchProductDetail() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let routeData = ProductDetailDestinationProvider.RouteData(productId: "test-product-id", secondaryId: "test-secondary-id")
    let sut = ProductDetailState.Middleware(service: mockService, url: .stub, routeData: routeData)
    let state = ProductDetailState()

    // WHEN
    let action = try? await sut.callAsFunction(action: .getDetailScreen, for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.callsCount, 1)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.latestCall?.0, "test-product-id")
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.latestCall?.1, "test-secondary-id")
    XCTAssertEqual(mockService.mock.fetchProductDetailScreenCalls.callsCount, 0)
  }

  /// Test: GIVEN empty URL and no route data, WHEN getDetailScreen is dispatched, THEN no service calls are made
  func test_GIVEN_emptyUrlAndNoRouteData_WHEN_getDetailScreen_THEN_noServiceCalls() async {
    // GIVEN
    let mockService = ProductDetailServiceMock()
    let sut = ProductDetailState.Middleware(service: mockService, url: .empty, routeData: nil)
    let state = ProductDetailState()

    // WHEN
    let action = try? await sut.callAsFunction(action: .getDetailScreen, for: state)

    // THEN
    XCTAssertNil(action)
    XCTAssertEqual(mockService.mock.fetchProductDetailCalls.callsCount, 0)
    XCTAssertEqual(mockService.mock.fetchProductDetailScreenCalls.callsCount, 0)
  }
}
