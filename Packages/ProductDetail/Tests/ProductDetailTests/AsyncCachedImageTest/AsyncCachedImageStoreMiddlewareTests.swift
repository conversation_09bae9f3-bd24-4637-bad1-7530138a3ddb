import Combine
import SwiftUI
import XCTest
@testable import ProductDetail

final class AsyncCachedImageStoreMiddlewareTests: XCTestCase {
  // MARK: - Middleware Tests

  /// Test: When loadImage event with valid URL is dispatched, middleware returns _startLoadImage event
  func test_GIVEN_loadImageEventWithValidURL_WHEN_middlewareCalled_THEN_returnsStartLoadImageEvent() async {
    // Arrange
    let mockDownloader = ImageDownloaderMock()
    let sut = AsyncCachedImage<EmptyView, EmptyView>.Middleware(imageDownloader: mockDownloader)
    let state = AsyncCachedImageViewState()

    // Act
    let event = await sut.callAsFunction(event: .loadImage(URL(string: .stub)), for: state)

    // Assert
    XCTAssertNotNil(event)
    if case let ._startLoadImage(url) = event {
      XCTAssertNotNil(url)
    } else {
      XCTFail("Expected _startLoadImage event")
    }
    XCTAssertEqual(mockDownloader.mock.downloadImageCalls.callsCount, 0)
  }

  /// Test: When loadImage event with valid URL is dispatched, middleware returns _startLoadImage event
  func test_GIVEN_StartLoadImageEventWithValidURL_WHEN_middlewareCalled_THEN_returnsReceivedImageEvent() async {
    // Arrange
    let mockDownloader = ImageDownloaderMock()
    let testImageData = UIImage(systemName: "circle")?.pngData()
    mockDownloader.mock.downloadImageCalls.mockCall { _ in
      testImageData!
    }

    let sut = AsyncCachedImage<EmptyView, EmptyView>.Middleware(imageDownloader: mockDownloader)
    let state = AsyncCachedImageViewState()

    // Act
    let event = await sut.callAsFunction(event: ._startLoadImage(URL(string: .stub)!), for: state)

    // Assert
    XCTAssertNotNil(event)
    if case let ._receivedImage(data) = event {
      XCTAssertNotNil(data)
    } else {
      XCTFail("Expected _startLoadImage event")
    }
    XCTAssertEqual(mockDownloader.mock.downloadImageCalls.callsCount, 1)
    XCTAssertEqual(mockDownloader.mock.downloadImageCalls.latestCall, URL(string: .stub))
  }

  /// Test: When loadImage event with invalid URL is dispatched, middleware returns loadingFailed
  func test_GIVEN_loadImageEventWithInvalidURL_WHEN_middlewareCalled_THEN_returnsLoadingFailedEvent() async {
    // Arrange
    let mockDownloader = ImageDownloaderMock()
    let sut = AsyncCachedImage<EmptyView, EmptyView>.Middleware(imageDownloader: mockDownloader)
    let state = AsyncCachedImageViewState()

    // Act
    let event = await sut.callAsFunction(event: .loadImage(nil), for: state)

    // Assert
    XCTAssertEqual(event, ._loadingFailed)
    XCTAssertEqual(mockDownloader.mock.downloadImageCalls.callsCount, 0)
    XCTAssertEqual(mockDownloader.mock.downloadImageCalls.latestCall, nil)
  }

  /// Test: When downloadImage returns nil, middleware returns loadingFailed
  func test_GIVEN_startLoadImageEvent_WHEN_downloadImageFails_THEN_middlewareReturnsLoadingFailedEvent() async {
    // Arrange
    let mockDownloader = ImageDownloaderMock()

    // Simulate failed download
    mockDownloader.mock.downloadImageCalls.mockThrowCall(AsyncCachedImageDownloadError.failed)

    let sut = AsyncCachedImage<EmptyView, EmptyView>.Middleware(imageDownloader: mockDownloader)
    let state = AsyncCachedImageViewState()

    // Act
    let event = await sut.callAsFunction(event: ._startLoadImage(URL(string: .stub)!), for: state)

    // Assert
    XCTAssertEqual(event, ._loadingFailed)
    XCTAssertEqual(mockDownloader.mock.downloadImageCalls.callsCount, 1)
    XCTAssertEqual(mockDownloader.mock.downloadImageCalls.latestCall, URL(string: .stub))
  }

  /// Test: When _receivedImage event is dispatched, middleware returns nil
  func test_GIVEN_receivedImageEvent_WHEN_middlewareCalled_THEN_returnsNil() async {
    // Arrange
    let mockDownloader = ImageDownloaderMock()
    let sut = AsyncCachedImage<EmptyView, EmptyView>.Middleware(imageDownloader: mockDownloader)
    let state = AsyncCachedImageViewState()

    // Act
    let event = await sut.callAsFunction(event: ._receivedImage(Data()), for: state)

    // Assert
    XCTAssertNil(event)
    XCTAssertEqual(mockDownloader.mock.downloadImageCalls.callsCount, 0)
  }
}
