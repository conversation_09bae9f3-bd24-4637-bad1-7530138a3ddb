import SwiftUI
import XCTest
@testable import ProductDetail

final class AsyncCachedImageStoreReducerTests: XCTestCase {
  // MARK: - Reducer Tests

  /// Test: When _receivedImage event is passed, the state should update with the image and success loading
  func test_GIVEN_receivedImageEvent_WHEN_reduced_THEN_stateUpdatesWithImage() {
    // Arrange
    var state = AsyncCachedImageViewState()
    let testUIImage = UIImage(systemName: "circle")!

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: ._receivedImage(testUIImage.pngData()!))

    // Assert
    XCTAssertEqual(state.loading, .success)
    XCTAssertNotNil(state.image)
  }

  /// Test: When _receivedImage event is passed with empty data, the state should update with failure
  func test_GIVEN_receivedImageEvent_WHEN_noData_THEN_stateUpdatesWithFailure() {
    // Arrange
    var state = AsyncCachedImageViewState()

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: ._receivedImage(Data()))

    // Assert
    XCTAssertEqual(state.loading, .failure(AsyncCachedImageDownloadError.failed))
    XCTAssertNil(state.image)
  }

  /// Test: When loadImage event is passed, the state should remain unchanged
  func test_GIVEN_loadImageEvent_WHEN_reduced_THEN_stateRemainsUnchanged() {
    // Arrange
    var state = AsyncCachedImageViewState()
    let initialState = state

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: .loadImage(nil))

    // Assert
    XCTAssertEqual(state, initialState)
  }

  /// Test: When _startLoad event is passed, the loading state should change to progress
  func test_GIVEN_startLoadImageEvent_WHEN_reduced_THEN_stateLoadingIsProgress() {
    // Arrange
    var state = AsyncCachedImageViewState()

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: ._startLoadImage(.stub))

    // Assert
    XCTAssertEqual(state.loading, .progress)
  }
}
