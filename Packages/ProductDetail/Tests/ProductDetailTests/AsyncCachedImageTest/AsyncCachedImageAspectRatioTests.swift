import SwiftUI
import XCTest
@testable import ProductDetail

final class AsyncCachedImageAspectRatioTests: XCTestCase {
  
  /// Test: GIVEN square image, WHEN scaledToFitWithAspectRatio with 5:7 ratio, THEN image is padded vertically with transparent background
  func test_GIVEN_squareImage_WHEN_scaledToFitWithAspectRatio5to7_THEN_imagePaddedVertically() {
    // GIVEN
    let originalSize = CGSize(width: 100, height: 100) // Square image
    let targetSize = CGSize(width: 50, height: 70) // 5:7 aspect ratio
    let targetAspectRatio = CGSize(width: 5, height: 7)
    let originalImage = createTestImage(size: originalSize)
    
    // WHEN
    let resultImage = originalImage.scaledToFitWithAspectRatio(
      targetSize: targetSize,
      targetAspectRatio: targetAspectRatio
    )
    
    // THEN
    XCTAssertNotNil(resultImage)
    // Result should have 5:7 aspect ratio
    let resultRatio = resultImage!.size.width / resultImage!.size.height
    let expectedRatio = targetAspectRatio.width / targetAspectRatio.height
    XCTAssertEqual(resultRatio, expectedRatio, accuracy: 0.01)
    
    // Should fit within target size
    XCTAssertLessThanOrEqual(resultImage!.size.width, targetSize.width)
    XCTAssertLessThanOrEqual(resultImage!.size.height, targetSize.height)
  }
  
  /// Test: GIVEN wide image, WHEN scaledToFitWithAspectRatio with 5:7 ratio, THEN image is padded horizontally with transparent background
  func test_GIVEN_wideImage_WHEN_scaledToFitWithAspectRatio5to7_THEN_imagePaddedHorizontally() {
    // GIVEN
    let originalSize = CGSize(width: 200, height: 100) // Wide image (2:1 ratio)
    let targetSize = CGSize(width: 50, height: 70) // 5:7 aspect ratio
    let targetAspectRatio = CGSize(width: 5, height: 7)
    let originalImage = createTestImage(size: originalSize)
    
    // WHEN
    let resultImage = originalImage.scaledToFitWithAspectRatio(
      targetSize: targetSize,
      targetAspectRatio: targetAspectRatio
    )
    
    // THEN
    XCTAssertNotNil(resultImage)
    // Result should have 5:7 aspect ratio
    let resultRatio = resultImage!.size.width / resultImage!.size.height
    let expectedRatio = targetAspectRatio.width / targetAspectRatio.height
    XCTAssertEqual(resultRatio, expectedRatio, accuracy: 0.01)
  }
  
  /// Test: GIVEN image with correct aspect ratio, WHEN scaledToFitWithAspectRatio, THEN image is scaled without padding
  func test_GIVEN_correctAspectRatioImage_WHEN_scaledToFitWithAspectRatio_THEN_imageScaledWithoutPadding() {
    // GIVEN
    let originalSize = CGSize(width: 500, height: 700) // Already 5:7 ratio
    let targetSize = CGSize(width: 50, height: 70) // Same 5:7 aspect ratio
    let targetAspectRatio = CGSize(width: 5, height: 7)
    let originalImage = createTestImage(size: originalSize)
    
    // WHEN
    let resultImage = originalImage.scaledToFitWithAspectRatio(
      targetSize: targetSize,
      targetAspectRatio: targetAspectRatio
    )
    
    // THEN
    XCTAssertNotNil(resultImage)
    // Result should match target size exactly (no padding needed)
    XCTAssertEqual(resultImage!.size.width, targetSize.width, accuracy: 1.0)
    XCTAssertEqual(resultImage!.size.height, targetSize.height, accuracy: 1.0)
  }
  
  /// Test: GIVEN very tall image, WHEN scaledToFitWithAspectRatio with 5:7 ratio, THEN image is constrained and padded correctly
  func test_GIVEN_tallImage_WHEN_scaledToFitWithAspectRatio5to7_THEN_imageConstrainedAndPadded() {
    // GIVEN
    let originalSize = CGSize(width: 100, height: 500) // Very tall image (1:5 ratio)
    let targetSize = CGSize(width: 50, height: 70) // 5:7 aspect ratio
    let targetAspectRatio = CGSize(width: 5, height: 7)
    let originalImage = createTestImage(size: originalSize)
    
    // WHEN
    let resultImage = originalImage.scaledToFitWithAspectRatio(
      targetSize: targetSize,
      targetAspectRatio: targetAspectRatio
    )
    
    // THEN
    XCTAssertNotNil(resultImage)
    // Result should have 5:7 aspect ratio
    let resultRatio = resultImage!.size.width / resultImage!.size.height
    let expectedRatio = targetAspectRatio.width / targetAspectRatio.height
    XCTAssertEqual(resultRatio, expectedRatio, accuracy: 0.01)
    
    // Should fit within target size
    XCTAssertLessThanOrEqual(resultImage!.size.width, targetSize.width)
    XCTAssertLessThanOrEqual(resultImage!.size.height, targetSize.height)
  }
  
  /// Test: GIVEN AsyncCachedImage with aspect ratio, WHEN middleware processes image, THEN correct aspect ratio scaling is applied
  func test_GIVEN_asyncCachedImageWithAspectRatio_WHEN_middlewareProcesses_THEN_correctAspectRatioScaling() async {
    // GIVEN
    let targetSize = CGSize(width: 40, height: 56) // ColorDimensionView size
    let aspectRatio = CGSize(width: 5, height: 7) // 5:7 aspect ratio
    let middleware = AsyncCachedImage<EmptyView, EmptyView>.Middleware(
      targetSize: targetSize,
      targetAspectRatio: aspectRatio
    )
    let state = AsyncCachedImageViewState()
    let testURL = URL(string: "https://example.com/test.jpg")!
    
    // Create a square test image that should be padded to 5:7 ratio
    let squareImage = createTestImage(size: CGSize(width: 200, height: 200))
    let mockDownloader = MockAsyncCachedImageDownloader()
    mockDownloader.mockImageData = squareImage.pngData()!
    
    let middlewareWithMockDownloader = AsyncCachedImage<EmptyView, EmptyView>.Middleware(
      targetSize: targetSize,
      targetAspectRatio: aspectRatio
    )
    
    // WHEN
    let result = await middlewareWithMockDownloader.callAsFunction(event: ._startLoadImage(testURL), for: state)
    
    // THEN
    if case let ._receivedImage(resultImage) = result {
      // Image should have the correct aspect ratio
      let resultRatio = resultImage.size.width / resultImage.size.height
      let expectedRatio = aspectRatio.width / aspectRatio.height
      XCTAssertEqual(resultRatio, expectedRatio, accuracy: 0.01)
      
      // Should fit within target size
      XCTAssertLessThanOrEqual(resultImage.size.width, targetSize.width)
      XCTAssertLessThanOrEqual(resultImage.size.height, targetSize.height)
    } else {
      XCTFail("Expected _receivedImage event")
    }
  }
  
  // MARK: - Helper Methods
  
  private func createTestImage(size: CGSize) -> UIImage {
    let format = UIGraphicsImageRendererFormat()
    format.scale = 1.0
    format.opaque = true
    
    let renderer = UIGraphicsImageRenderer(size: size, format: format)
    return renderer.image { context in
      UIColor.blue.setFill()
      context.fill(CGRect(origin: .zero, size: size))
    }
  }
}

// MARK: - Mock Downloader

class MockAsyncCachedImageDownloader: AsyncCachedImageDownloadable {
  var mockImageData: Data = Data()
  
  func downloadImage(url: URL) async throws -> Data {
    return mockImageData
  }
}
