import SwiftUI
import XCTest
@testable import ProductDetail

final class AsyncCachedImageScalingTests: XCTestCase {
  
  /// Test: GIVEN UIImage with large dimensions, WHEN scaledToFit is called with smaller target size, THEN image is scaled down while maintaining aspect ratio
  func test_GIVEN_largeImage_WHEN_scaledToFit_THEN_imageScaledDownMaintainingAspectRatio() {
    // GIVEN
    let originalSize = CGSize(width: 1000, height: 1400) // 5:7 aspect ratio
    let targetSize = CGSize(width: 100, height: 140) // Same aspect ratio, smaller
    let originalImage = createTestImage(size: originalSize)
    
    // WHEN
    let scaledImage = originalImage.scaledToFit(targetSize: targetSize)
    
    // THEN
    XCTAssertNotNil(scaledImage)
    XCTAssertEqual(scaledImage?.size.width, 100, accuracy: 1.0)
    XCTAssertEqual(scaledImage?.size.height, 140, accuracy: 1.0)
  }
  
  /// Test: GIVEN UIImage smaller than target size, WHEN scaledToFit is called, THEN original image is returned unchanged
  func test_GIVEN_smallImage_WHEN_scaledToFit_THEN_originalImageReturned() {
    // GIVEN
    let originalSize = CGSize(width: 50, height: 70)
    let targetSize = CGSize(width: 100, height: 140)
    let originalImage = createTestImage(size: originalSize)
    
    // WHEN
    let scaledImage = originalImage.scaledToFit(targetSize: targetSize)
    
    // THEN
    XCTAssertNotNil(scaledImage)
    XCTAssertEqual(scaledImage?.size.width, 50, accuracy: 1.0)
    XCTAssertEqual(scaledImage?.size.height, 70, accuracy: 1.0)
  }
  
  /// Test: GIVEN UIImage with different aspect ratio, WHEN scaledToFit is called, THEN image is scaled to fit within bounds maintaining aspect ratio
  func test_GIVEN_differentAspectRatio_WHEN_scaledToFit_THEN_imageScaledToFitWithinBounds() {
    // GIVEN
    let originalSize = CGSize(width: 1000, height: 500) // 2:1 aspect ratio (wide)
    let targetSize = CGSize(width: 100, height: 140) // 5:7 aspect ratio (tall)
    let originalImage = createTestImage(size: originalSize)
    
    // WHEN
    let scaledImage = originalImage.scaledToFit(targetSize: targetSize)
    
    // THEN
    XCTAssertNotNil(scaledImage)
    // Should be constrained by width (100), height should be 50 to maintain 2:1 ratio
    XCTAssertEqual(scaledImage?.size.width, 100, accuracy: 1.0)
    XCTAssertEqual(scaledImage?.size.height, 50, accuracy: 1.0)
  }
  
  /// Test: GIVEN tall image, WHEN scaledToFit is called with wide target, THEN image is constrained by height
  func test_GIVEN_tallImage_WHEN_scaledToFitWideTarget_THEN_imageConstrainedByHeight() {
    // GIVEN
    let originalSize = CGSize(width: 500, height: 1000) // 1:2 aspect ratio (tall)
    let targetSize = CGSize(width: 140, height: 100) // 7:5 aspect ratio (wide)
    let originalImage = createTestImage(size: originalSize)
    
    // WHEN
    let scaledImage = originalImage.scaledToFit(targetSize: targetSize)
    
    // THEN
    XCTAssertNotNil(scaledImage)
    // Should be constrained by height (100), width should be 50 to maintain 1:2 ratio
    XCTAssertEqual(scaledImage?.size.width, 50, accuracy: 1.0)
    XCTAssertEqual(scaledImage?.size.height, 100, accuracy: 1.0)
  }
  
  /// Test: GIVEN AsyncCachedImage with target size, WHEN middleware processes image, THEN correct scaling is applied
  func test_GIVEN_asyncCachedImageWithTargetSize_WHEN_middlewareProcesses_THEN_correctScalingApplied() async {
    // GIVEN
    let targetSize = CGSize(width: 40, height: 56) // ColorDimensionView size
    let mockDownloader = MockAsyncCachedImageDownloader()
    let middleware = AsyncCachedImage<EmptyView, EmptyView>.Middleware(
      targetSize: targetSize,
      imageDownloader: mockDownloader
    )
    let state = AsyncCachedImageViewState()
    let testURL = URL(string: "https://example.com/test.jpg")!
    
    // Create a large test image that should be scaled down
    let largeImage = createTestImage(size: CGSize(width: 400, height: 560))
    mockDownloader.mockImageData = largeImage.pngData()!
    
    // WHEN
    let result = await middleware.callAsFunction(event: ._startLoadImage(testURL), for: state)
    
    // THEN
    if case let ._receivedImage(scaledImage) = result {
      // Image should be scaled down to fit target size while maintaining aspect ratio
      XCTAssertEqual(scaledImage.size.width, 40, accuracy: 1.0)
      XCTAssertEqual(scaledImage.size.height, 56, accuracy: 1.0)
    } else {
      XCTFail("Expected _receivedImage event")
    }
  }
  
  // MARK: - Helper Methods
  
  private func createTestImage(size: CGSize) -> UIImage {
    let format = UIGraphicsImageRendererFormat()
    format.scale = 1.0
    format.opaque = true
    
    let renderer = UIGraphicsImageRenderer(size: size, format: format)
    return renderer.image { context in
      UIColor.blue.setFill()
      context.fill(CGRect(origin: .zero, size: size))
    }
  }
}

// MARK: - Mock Downloader

class MockAsyncCachedImageDownloader: AsyncCachedImageDownloadable {
  var mockImageData: Data = Data()
  
  func downloadImage(url: URL) async throws -> Data {
    return mockImageData
  }
}
