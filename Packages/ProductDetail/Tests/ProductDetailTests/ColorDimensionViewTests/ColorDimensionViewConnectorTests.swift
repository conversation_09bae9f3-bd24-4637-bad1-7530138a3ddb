import Combine
import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class ColorDimensionViewConnectorTests: XCTestCase {
  /// Test: GIVEN ColorDimensionViewConnector, WHEN configure is called, THEN it dispatches _setColor event with correct ProductColorDimension
  func test_GIVEN_connector_WHEN_configure_THEN_dispatchesSetColorEvent() async {
    await withMainSerialExecutor {
      // GIVEN
      let colorDimension = ProductColorDimension(
        state: LoadingComponentStateDone(
          content: ProductColorDimension.Content(
            colorName: .stub,
            colors: [
              .stub()
            ]
          )
        )
      )

      let sut = ColorDimensionView.ViewState.Connector(colorDimension: colorDimension)
      let expectation = XCTestExpectation(description: "Dispatches _setColor event")

      var receivedEvent: ColorDimensionView.Event?

      // WHEN
      await sut.configure { event in
        receivedEvent = event
        expectation.fulfill()
      }

      // THEN
      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertEqual(receivedEvent, ._setColor(with: colorDimension))
    }
  }
}
