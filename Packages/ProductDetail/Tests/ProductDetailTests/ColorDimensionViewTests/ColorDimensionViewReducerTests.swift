import OGAppKitSDK
import OGCoreTestsUtils
import XCTest

@testable import ProductDetail

final class ColorDimensionViewReducerTests: XCTestCase {
  /// Test: GIVEN an initial state, WHEN _setColor is dispatched with color dimension, THEN the state is updated with colors, colorName, and showsColorSelection
  func test_GIVEN_initialState_WHEN_setColor_THEN_stateUpdatesCorrectly() {
    // GIVEN
    var state = ColorDimensionView.ViewState.initial
    let colorDimension = ProductColorDimension(
      state: LoadingComponentStateDone(
        content:
        ProductColorDimension
          .Content(
            colorName: .stub,
            colors: [
              .stub(),
              .stub(
                isSelected: false
              )
            ]
          )
      )
    )

    // WHEN
    ColorDimensionView.ViewState.Reducer.reduce(&state, with: ._setColor(with: colorDimension))

    // THEN
    XCTAssertEqual(state.colors.count, 2)
    XCTAssertEqual(state.colorName, .stub)
  }

  /// Test: GIVEN colorName is provided, WHEN _setColor is dispatched, THEN colorName is set accordingly even if no selected color exists
  func test_GIVEN_colorNameProvided_WHEN_setColor_THEN_colorNameIsUsed() {
    // GIVEN
    var state = ColorDimensionView.ViewState.initial
    let colorDimension = ProductColorDimension(
      state: LoadingComponentStateDone(
        content:
        ProductColorDimension
          .Content(
            colorName: .stub,
            colors: [
              .stub()
            ]
          )
      )
    )

    // WHEN
    ColorDimensionView.ViewState.Reducer.reduce(&state, with: ._setColor(with: colorDimension))

    // THEN
    XCTAssertEqual(state.colorName, .stub)
  }

  /// Test: GIVEN color dimension with 'inStock' availability, WHEN _setColor is dispatched, THEN color is available
  func test_GIVEN_inStockAvailability_WHEN_setColor_THEN_colorIsAvailable() {
    // GIVEN
    var state = ColorDimensionView.ViewState.initial
    let colorDimension = ProductColorDimension(
      state: LoadingComponentStateDone(
        content:
        ProductColorDimension
          .Content(
            colorName: .stub,
            colors: [
              .stub()
            ]
          )
      )
    )

    // WHEN
    ColorDimensionView.ViewState.Reducer.reduce(&state, with: ._setColor(with: colorDimension))

    // THEN
    XCTAssertEqual(state.colors.first?.isAvailable, true)
  }

  /// Test: GIVEN color dimension with 'lowStock' availability, WHEN _setColor is dispatched, THEN color is available
  func test_GIVEN_lowStockAvailability_WHEN_setColor_THEN_colorIsAvailable() {
    // GIVEN
    var state = ColorDimensionView.ViewState.initial
    let colorDimension = ProductColorDimension(
      state: LoadingComponentStateDone(
        content:
        ProductColorDimension
          .Content(
            colorName: .stub,
            colors: [
              .stub(availabilityState: .lowStock)
            ]
          )
      )
    )

    // WHEN
    ColorDimensionView.ViewState.Reducer.reduce(&state, with: ._setColor(with: colorDimension))

    // THEN
    XCTAssertEqual(state.colors.first?.isAvailable, true)
  }

  /// Test: GIVEN color dimension with 'permanentlyOutOfStock' availability, WHEN _setColor is dispatched, THEN color is NOT available
  func test_GIVEN_permanentlyOutOfStockAvailability_WHEN_setColor_THEN_colorIsNotAvailable() {
    // GIVEN
    var state = ColorDimensionView.ViewState.initial
    let colorDimension = ProductColorDimension(
      state: LoadingComponentStateDone(
        content:
        ProductColorDimension
          .Content(
            colorName: .stub,
            colors: [
              .stub(availabilityState: .permanentlyOutOfStock)
            ]
          )
      )
    )

    // WHEN
    ColorDimensionView.ViewState.Reducer.reduce(&state, with: ._setColor(with: colorDimension))

    // THEN
    XCTAssertEqual(state.colors.first?.isAvailable, false)
  }

  /// Test: GIVEN color dimension with 'temporarilyOutOfStock' availability, WHEN _setColor is dispatched, THEN color is NOT available
  func test_GIVEN_temporarilyOutOfStockAvailability_WHEN_setColor_THEN_colorIsNotAvailable() {
    // GIVEN
    var state = ColorDimensionView.ViewState.initial
    let colorDimension = ProductColorDimension(
      state: LoadingComponentStateDone(
        content:
        ProductColorDimension
          .Content(
            colorName: .stub,
            colors: [
              .stub(availabilityState: .temporarilyOutOfStock)
            ]
          )
      )
    )

    // WHEN
    ColorDimensionView.ViewState.Reducer.reduce(&state, with: ._setColor(with: colorDimension))

    // THEN
    XCTAssertEqual(state.colors.first?.isAvailable, false)
  }

  /// Test: GIVEN color dimension with 'preOrderable' availability, WHEN _setColor is dispatched, THEN color is available
  func test_GIVEN_preOrderableAvailability_WHEN_setColor_THEN_colorIsAvailable() {
    // GIVEN
    var state = ColorDimensionView.ViewState.initial
    let colorDimension = ProductColorDimension(
      state: LoadingComponentStateDone(
        content:
        ProductColorDimension
          .Content(
            colorName: .stub,
            colors: [
              .stub(availabilityState: .preOrderable)
            ]
          )
      )
    )

    // WHEN
    ColorDimensionView.ViewState.Reducer.reduce(&state, with: ._setColor(with: colorDimension))

    // THEN
    XCTAssertEqual(state.colors.first?.isAvailable, true)
  }

  /// Test: GIVEN color dimension with 'unknown' availability, WHEN _setColor is dispatched, THEN color is NOT available
  func test_GIVEN_unknownAvailability_WHEN_setColor_THEN_colorIsNotAvailable() {
    // GIVEN
    var state = ColorDimensionView.ViewState.initial
    let colorDimension = ProductColorDimension(
      state: LoadingComponentStateDone(
        content:
        ProductColorDimension
          .Content(
            colorName: .stub,
            colors: [
              .stub(availabilityState: .unknown)
            ]
          )
      )
    )

    // WHEN
    ColorDimensionView.ViewState.Reducer.reduce(&state, with: ._setColor(with: colorDimension))

    // THEN
    XCTAssertEqual(state.colors.first?.isAvailable, false)
  }
}
