import Combine
import OGRouterTestsUtils
import XCTest

@testable import ProductDetail

final class VariantSelectionViewMiddlewareTests: XCTestCase {
  func test_WHEN_dismiss_THEN_routerSendDismiss() async {
    // Arrange
    let routerMock = OGRoutePublisherMock()
    let sut = VariantSelectionView.ViewState.Middleware(router: routerMock, selectedVariantPublisher: nil)
    let state = VariantSelectionView.ViewState.stub(
      variants: [.stub(isSelected: true)]
    )

    // Act
    let nextEvent = await sut.callAsFunction(event: .dismiss, for: state)

    // Assert
    XCTAssertNil(nextEvent)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall, .dismiss)
  }

  func test_WHEN_viewDisappeared_THEN_publisherUpdatedAndNoRouterCall() async {
    // Arrange
    let routerMock = OGRoutePublisherMock()
    let selectedProductIdPublisher = CurrentValueSubject<Decodable, Never>(SelectedVariant.empty)
    let sut = VariantSelectionView.ViewState.Middleware(router: routerMock, selectedVariantPublisher: selectedProductIdPublisher)
    let state = VariantSelectionView.ViewState.stub(
      variants: [.stub(isSelected: true)]
    )

    // Act
    let nextEvent = await sut.callAsFunction(event: .viewDisappeared, for: state)

    // Assert
    XCTAssertNil(nextEvent)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 0)
    XCTAssertEqual(selectedProductIdPublisher.value as? SelectedVariant, SelectedVariant(productId: "p1", userDidChangeVariant: false, isVoucher: false, customName: nil))
  }

  func test_WHEN_selectVariant_THEN_noActionTaken() async {
    // Arrange
    let routerMock = OGRoutePublisherMock()
    let selectedProductIdPublisher = CurrentValueSubject<Decodable, Never>(SelectedVariant.empty)
    let sut = VariantSelectionView.ViewState.Middleware(router: routerMock, selectedVariantPublisher: selectedProductIdPublisher)
    let state = VariantSelectionView.ViewState.stub()

    // Act
    let nextEvent = await sut.callAsFunction(event: .selectVariant("p2", true), for: state)

    // Assert
    XCTAssertNil(nextEvent)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 0)
    XCTAssertEqual(selectedProductIdPublisher.value as? SelectedVariant, SelectedVariant.empty)
  }

  func test_WHEN_selectFirstDimensionVariant_THEN_noActionTaken() async {
    // Arrange
    let routerMock = OGRoutePublisherMock()
    let selectedProductIdPublisher = CurrentValueSubject<Decodable, Never>(SelectedVariant.empty)
    let sut = VariantSelectionView.ViewState.Middleware(router: routerMock, selectedVariantPublisher: selectedProductIdPublisher)
    let state = VariantSelectionView.ViewState.stub(
      dimensionStyle: .nested,
      nestedDimensions: .stub()
    )

    // Act
    let nextEvent = await sut.callAsFunction(event: .selectFirstDimensionVariant("Blue"), for: state)

    // Assert
    XCTAssertNil(nextEvent)
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 0)
    XCTAssertEqual(selectedProductIdPublisher.value as? SelectedVariant, SelectedVariant.empty)
  }
}
