import XCTest

@testable import ProductDetail

final class VariantSelectionViewReducerTests: XCTestCase {
  func test_WHEN_selectVariant_withFlatDimension_THEN_stateUpdated() {
    // Arrange
    var state = VariantSelectionView.ViewState.stub(dimensionStyle: .flat)

    // Act
    VariantSelectionView.ViewState.Reducer.reduce(&state, with: .selectVariant("p2", true))

    // Assert
    XCTAssertEqual(state.selectedProductId, "p2")
    XCTAssertTrue(state.variants.first { $0.productId == "p2" }?.isSelected == true)
    XCTAssertFalse(state.variants.first { $0.productId == "p1" }?.isSelected == true)
  }

  func test_WHEN_selectVariant_withNestedDimensions_THEN_stateUpdated() {
    // Arrange
    var state = VariantSelectionView.ViewState.stub(
      dimensionStyle: .nested,
      nestedDimensions: .stub()
    )

    // Act
    VariantSelectionView.ViewState.Reducer.reduce(&state, with: .selectVariant("p2", true))

    // Assert
    XCTAssertEqual(state.selectedProductId, "p2")
    XCTAssertTrue(state.nestedDimensions?.entries.last?.productDimension.entries.first { $0.productId == "p2" }?.isSelected == true)
    XCTAssertFalse(state.nestedDimensions?.entries.first?.productDimension.entries.first { $0.productId == "p1" }?.isSelected == true)
    XCTAssertEqual(state.selectedNestedDimensionEntry?.name, "Blue")
  }

  func test_WHEN_selectFirstDimensionVariant_THEN_stateUpdated() {
    // Arrange
    var state = VariantSelectionView.ViewState.stub(
      dimensionStyle: .nested,
      nestedDimensions: .stub()
    )

    // Act
    VariantSelectionView.ViewState.Reducer.reduce(&state, with: .selectFirstDimensionVariant("Blue"))

    // Assert
    XCTAssertEqual(state.selectedNestedDimensionEntry?.name, "Blue")
  }

  func test_WHEN_selectVariant_withCategorizedDimension_THEN_userDidSelectVariant() {
    // Arrange
    var initialState = VariantSelectionView.ViewState.stub(dimensionStyle: .categorized)
    let state = VariantSelectionView.ViewState.stub(dimensionStyle: .categorized, userDidSelectVariant: true)

    // Act
    VariantSelectionView.ViewState.Reducer.reduce(&initialState, with: .selectVariant("p2", true))

    // Assert
    XCTAssertEqual(state, initialState)
  }

  func test_WHEN_dismiss_THEN_noStateChange() {
    // Arrange
    var state = VariantSelectionView.ViewState.stub()
    let initialState = state

    // Act
    VariantSelectionView.ViewState.Reducer.reduce(&state, with: .dismiss)

    // Assert
    XCTAssertEqual(state, initialState)
  }

  func test_WHEN_viewDisappeared_THEN_noStateChange() {
    // Arrange
    var state = VariantSelectionView.ViewState.stub()
    let initialState = state

    // Act
    VariantSelectionView.ViewState.Reducer.reduce(&state, with: .viewDisappeared)

    // Assert
    XCTAssertEqual(state, initialState)
  }
}
