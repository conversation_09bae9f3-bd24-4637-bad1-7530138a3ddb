import Combine
import OGAppKitSDK
import OGCoreTestsUtils
import OGRouter
import OGRouterTestsUtils
import XCTest
@testable import ProductDetail

final class ProductDetailViewMiddlewareTests: XCTestCase {
  private var sut: ProductDetailView.ViewState.Middleware!
  private var detailStoreMiddlewareMock: ProductDetailStoreMiddlewareMock!
  private var routerMock: OGRoutePublisherMock!
  private var selectedVariantPublisher: CurrentValueSubject<Decodable, Never>!
  override func setUp() {
    super.setUp()
    detailStoreMiddlewareMock = ProductDetailStoreMiddlewareMock()
    detailStoreMiddlewareMock.mock.callAsFunctionCalls.mockCall { action, _ in
      action
    }
    routerMock = OGRoutePublisherMock()
    selectedVariantPublisher = CurrentValueSubject<Decodable, Never>(SelectedVariant.empty)
    let mockStore = ProductDetailStore(
      reducer: ProductDetailStoreReducerMock.reduce,
      middlewares: detailStoreMiddlewareMock
    )

    sut = ProductDetailView.ViewState.Middleware(
      productDetailStore: mockStore,
      router: routerMock,
      selectedVariantPublisher: selectedVariantPublisher
    )
  }

  override func tearDown() {
    sut = nil
    detailStoreMiddlewareMock = nil
    routerMock = nil
    selectedVariantPublisher = nil
    super.tearDown()
  }

  func test_GIVEN_initialState_WHEN_eventIsReload_THEN_shouldDispatchGetDetailScreen() async {
    let expectation = expectation(description: "Expected getDetailScreen event to be dispatched")

    let nextEvent = await sut.callAsFunction(
      event: .reload,
      for: .initial
    )

    detailStoreMiddlewareMock.mock.callAsFunctionCalls.fulfilled = {
      expectation.fulfill()
    }

    await fulfillment(of: [expectation], timeout: 0.1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.callsCount, 1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.latestCall?.0, ProductDetailAction.getDetailScreen)
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventGetDetailScreenWith_THEN_shouldDispatchGetDetailScreen() async {
    let expectation = expectation(description: "Expected getDetailScreen event to be dispatched")

    let nextEvent = await sut.callAsFunction(
      event: .getDetailScreenWith(id: .stub, secondaryId: nil),
      for: .initial
    )

    detailStoreMiddlewareMock.mock.callAsFunctionCalls.fulfilled = {
      expectation.fulfill()
    }

    await fulfillment(of: [expectation], timeout: 0.1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.callsCount, 1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.latestCall?.0, ProductDetailAction.getDetailScreenWith(id: .stub, secondaryId: nil))
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventGetDetailScreenForColor_THEN_NoNextEvent() async {
    let expectation = expectation(description: "Expected updateProductColorSelection event to be dispatched")
    let nextEvent = await sut.callAsFunction(
      event: .getDetailScreenForColor(id: .stub),
      for: .initial
    )
    detailStoreMiddlewareMock.mock.callAsFunctionCalls.fulfilled = {
      expectation.fulfill()
    }
    await fulfillment(of: [expectation], timeout: 0.1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.callsCount, 1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.latestCall?.0, ProductDetailAction.updateProductColorSelection(id: .stub))
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventOpenVariantSelection_THEN_NoNextEvent() async {
    let nextEvent = await sut.callAsFunction(
      event: .openVariantSelection,
      for: .initial
    )
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall?.id, OGRoute.variantSelection.id)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall?.publisher?.value as? SelectedVariant, selectedVariantPublisher.value as? SelectedVariant)
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventReceivedSelectedVariant_THEN_NoNextEvent() async {
    let nextEvent = await sut.callAsFunction(
      event: ._receivedSelectedVariant(.stub()),
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventReceivedSelectedVariant_THEN_NextEventGetDetailScreenWith() async {
    let selectedVariant = SelectedVariant.stub(userDidChangeVariant: true)
    let nextEvent = await sut.callAsFunction(
      event: ._receivedSelectedVariant(selectedVariant),
      for: .initial
    )
    XCTAssertEqual(nextEvent, .getDetailScreenForSize(id: selectedVariant.productId))
  }

  func test_GIVEN_initialState_WHEN_eventGetDetailScreenForSize_THEN_shouldDispatchUpdateProductVariantSelection() async {
    let expectation = expectation(description: "Expected updateProductVariantSelection event to be dispatched")
    let nextEvent = await sut.callAsFunction(
      event: .getDetailScreenForSize(id: .stub),
      for: .initial
    )
    detailStoreMiddlewareMock.mock.callAsFunctionCalls.fulfilled = {
      expectation.fulfill()
    }
    await fulfillment(of: [expectation], timeout: 0.1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.callsCount, 1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.latestCall?.0, ProductDetailAction.updateProductVariantSelection(id: .stub))
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventUpdateBadgeCount_THEN_shouldDispatchUpdateBadgeCount() async {
    let expectation = expectation(description: "Expected updateBadgeCount event to be dispatched")
    let nextEvent = await sut.callAsFunction(
      event: .updateBadgeCount,
      for: .initial
    )
    detailStoreMiddlewareMock.mock.callAsFunctionCalls.fulfilled = {
      expectation.fulfill()
    }
    await fulfillment(of: [expectation], timeout: 0.1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.callsCount, 1)
    XCTAssertEqual(detailStoreMiddlewareMock.mock.callAsFunctionCalls.latestCall?.0, ProductDetailAction.updateBadgeCount)
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventSlowLoadingBannerTrue_THEN_shouldSendErrorBannerRoute() async {
    let nextEvent = await sut.callAsFunction(
      event: .slowLoadingBanner(true),
      for: .initial
    )
    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall?.id, OGRoute.errorBanner.id)
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventSlowLoadingBannerFalse_THEN_shouldDismissErrorBannerRoute() async {
    let nextEvent = await sut.callAsFunction(
      event: .slowLoadingBanner(false),
      for: .initial
    )
    XCTAssertEqual(routerMock.mock.dismissRouteCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.dismissRouteCalls.latestCall?.id, OGRoute.errorBanner.id)
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventTrackScreenView_THEN_noNextEvent() async {
    let nextEvent = await sut.callAsFunction(
      event: .trackScreenView,
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventError_THEN_returnsSlowLoadingBannerFalse() async {
    let nextEvent = await sut.callAsFunction(
      event: ._error(.notFound),
      for: .initial
    )
    XCTAssertEqual(nextEvent, .slowLoadingBanner(false))
  }

  func test_GIVEN_stateWithScreeDidChangeAndNotLoading_WHEN_eventReceived_THEN_returnsResetScreeDidChange() async {
    let state = ProductDetailView.ViewState(isLoading: false, screeDidChange: true)
    let nextEvent = await sut.callAsFunction(
      event: ._received(.stub, .stub, StickyBasket(price: .stub, basket: .stub)),
      for: state
    )
    XCTAssertEqual(nextEvent, ._resetScreeDidChange)
  }

  func test_GIVEN_stateWithoutScreeDidChange_WHEN_eventReceived_THEN_returnsNil() async {
    let state = ProductDetailView.ViewState(loadingState: .success, screeDidChange: false)
    let nextEvent = await sut.callAsFunction(
      event: ._received(.stub, .stub, StickyBasket(price: .stub, basket: .stub)),
      for: state
    )
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventResetScreeDidChange_THEN_returnsNil() async {
    let nextEvent = await sut.callAsFunction(
      event: ._resetScreeDidChange,
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventUpdating_THEN_returnsNil() async {
    let nextEvent = await sut.callAsFunction(
      event: ._updating,
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventIndexOfCurrentImage_THEN_returnsNil() async {
    let nextEvent = await sut.callAsFunction(
      event: .indexOfCurrentImage(1),
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_eventMonitoredView_THEN_returnsNil() async {
    let nextEvent = await sut.callAsFunction(
      event: .monitoredView(.productGallery, true),
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }
}
