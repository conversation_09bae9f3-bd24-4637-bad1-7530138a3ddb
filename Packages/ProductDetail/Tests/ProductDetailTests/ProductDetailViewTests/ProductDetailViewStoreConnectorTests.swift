import Combine
import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class ProductDetailViewStoreConnectorTests: XCTestCase {
  private var sut: ProductDetailView.ViewState.Connector!
  private var detailStoreMiddlewareMock: ProductDetailStoreMiddlewareMock!
  private var selectedVariantPublisher: CurrentValueSubject<Decodable, Never>!
  override func setUp() {
    super.setUp()
    detailStoreMiddlewareMock = ProductDetailStoreMiddlewareMock()
    selectedVariantPublisher = CurrentValueSubject<Decodable, Never>(SelectedVariant.empty)
    let mockStore = ProductDetailStore(
      initialState: ProductDetailStore.State(
        components: .stub,
        header: .stub,
        basket: StickyBasket(price: .stub, basket: .stub)
      ),
      reducer: ProductDetailStoreReducerMock.reduce,
      middlewares: detailStoreMiddlewareMock
    )

    sut = ProductDetailView.ViewState.Connector(
      productDetailStore: mockStore,
      selectedVariantPublisher: selectedVariantPublisher,
      trace: PerformanceTrackerMock()
    )
  }

  override func tearDown() {
    sut = nil
    detailStoreMiddlewareMock = nil
    selectedVariantPublisher = nil
    super.tearDown()
  }

  func test_GIVEN_connector_WHEN_configureCalled_THEN_dispatchReceivedEvent() async {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected _received event to be dispatched")

      var dispatchedEvents: [ProductDetailView.Event] = []
      let dispatch: (ProductDetailView.Event) async -> Void = { event in
        dispatchedEvents.append(event)
        if case ._received = event {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      await fulfillment(of: [expectation], timeout: 0.1)
      XCTAssertTrue(dispatchedEvents.contains(where: {
        if case ._received = $0 {
          return true
        }
        return false
      }))
    }
  }
}
