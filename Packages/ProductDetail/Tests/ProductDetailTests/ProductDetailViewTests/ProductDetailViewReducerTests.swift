import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class ProductDetailViewReducerTests: XCTestCase {
  func test_GIVEN_initial_state_WHEN_eventIsUpdating_THEN_loadingStateShouldBeProgress() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._updating)
    XCTAssertEqual(state.loadingState, .progress)
  }

  func test_GIVEN_initial_state_WHEN_eventIsReload_THEN_loadingStateShouldBeProgress() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .reload)
    XCTAssertEqual(state.loadingState, .progress)
  }

  func test_GIVEN_initial_state_WHEN_eventIsReceivedComponents_THEN_loadingStateShouldBeSuccess_and_componentsShouldUpdate() {
    var state = ProductDetailView.ViewState.initial
    let components: [OGProductDetailComponent] = .stub
    let header: ProductHeader = .stub
    let stickyBasket = StickyBasket(price: .stub, basket: .stub)
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._received(components, header, stickyBasket))
    XCTAssertEqual(state.loadingState, .success)
    XCTAssertEqual(state.components, components)
    XCTAssertEqual(state.header, header)
    XCTAssertEqual(state.stickyBasket, stickyBasket)
    XCTAssertEqual(state.isLoading, false)
  }

  func test_GIVEN_initial_state_WHEN_eventIsError_THEN_loadingStateShouldBeFailure() {
    var state = ProductDetailView.ViewState.initial
    let error = ProductDetailError.notFound
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._error(error))
    XCTAssertEqual(state.loadingState, .failure(error))
  }

  func test_GIVEN_initial_state_WHEN_eventIsMonitoredView_THEN_monitoredViewStateShouldUpdate() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .monitoredView(.productGallery, false))
    XCTAssertTrue(state.showsHeaderWishlistIcon)
  }

  func test_GIVEN_initial_state_WHEN_eventIsReceivedSelectedVariant_THEN_userDidChangeVariantStateShouldUpdate() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._receivedSelectedVariant(.stub(userDidChangeVariant: true)))
    XCTAssertTrue(state.selectedVariant.userDidChangeVariant)
  }

  func test_GIVEN_initial_state_WHEN_eventIsIndexOfCurrentImage_THEN_indexOfCurrentImageStateShouldUpdate() {
    var state = ProductDetailView.ViewState.initial
    let indexOfCurrentImage = 1
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .indexOfCurrentImage(indexOfCurrentImage))
    XCTAssertEqual(state.indexOfCurrentImage, indexOfCurrentImage)
  }

  func test_GIVEN_StateWithIndexOfCurrentImage_WHEN_eventIsGetDetailScreenForColor_THEN_indexOfCurrentImageStateShouldUpdat() {
    var state = ProductDetailView.ViewState(indexOfCurrentImage: 1)
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .getDetailScreenForColor(id: .stub))
    XCTAssertEqual(state.indexOfCurrentImage, 0)
  }

  func test_GIVEN_initial_state_WHEN_eventIsGetDetailScreenForColor_THEN_screeDidChangeShouldBeTrue() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .getDetailScreenForColor(id: .stub))
    XCTAssertTrue(state.screeDidChange)
  }

  func test_GIVEN_initial_state_WHEN_eventIsGetDetailScreenForSize_THEN_screeDidChangeShouldBeTrue() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .getDetailScreenForSize(id: .stub))
    XCTAssertTrue(state.screeDidChange)
  }

  func test_GIVEN_stateWithScreeDidChangeTrue_WHEN_eventIsResetScreeDidChange_THEN_screeDidChangeShouldBeFalse() {
    var state = ProductDetailView.ViewState(screeDidChange: true)
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._resetScreeDidChange)
    XCTAssertFalse(state.screeDidChange)
  }

  func test_GIVEN_initial_state_WHEN_eventIsGetDetailScreenWith_THEN_stateRemainsUnchanged() {
    var state = ProductDetailView.ViewState.initial
    let initialState = state
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .getDetailScreenWith(id: .stub, secondaryId: nil))
    XCTAssertEqual(state, initialState)
  }

  func test_GIVEN_initial_state_WHEN_eventIsOpenVariantSelection_THEN_stateRemainsUnchanged() {
    var state = ProductDetailView.ViewState.initial
    let initialState = state
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .openVariantSelection)
    XCTAssertEqual(state, initialState)
  }

  func test_GIVEN_initial_state_WHEN_eventIsSlowLoadingBanner_THEN_stateRemainsUnchanged() {
    var state = ProductDetailView.ViewState.initial
    let initialState = state
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .slowLoadingBanner(true))
    XCTAssertEqual(state, initialState)
  }

  func test_GIVEN_initial_state_WHEN_eventIsTrackScreenView_THEN_stateRemainsUnchanged() {
    var state = ProductDetailView.ViewState.initial
    let initialState = state
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .trackScreenView)
    XCTAssertEqual(state, initialState)
  }

  func test_GIVEN_initial_state_WHEN_eventIsUpdateBadgeCount_THEN_stateRemainsUnchanged() {
    var state = ProductDetailView.ViewState.initial
    let initialState = state
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .updateBadgeCount)
    XCTAssertEqual(state, initialState)
  }
}
