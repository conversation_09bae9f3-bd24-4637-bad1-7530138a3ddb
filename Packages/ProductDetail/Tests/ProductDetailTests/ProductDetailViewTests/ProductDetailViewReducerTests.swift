import OGAppKitSDK
import OGCoreTestsUtils
import XCTest
@testable import ProductDetail

final class ProductDetailViewReducerTests: XCTestCase {
  func test_GIVEN_initial_state_WHEN_eventIsUpdating_THEN_loadingStateShouldBeProgress() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._updating)
    XCTAssertEqual(state.loadingState, .progress)
  }

  func test_GIVEN_initial_state_WHEN_eventIsReload_THEN_loadingStateShouldBeProgress() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .reload)
    XCTAssertEqual(state.loadingState, .progress)
  }

  func test_GIVEN_initial_state_WHEN_eventIsReceivedComponents_THEN_loadingStateShouldBeSuccess_and_componentsShouldUpdate() {
    var state = ProductDetailView.ViewState.initial
    let components: [OGProductDetailComponent] = .stub
    let header: ProductHeader = .stub
    let stickyBasket = StickyBasket(price: .stub, basket: .stub)
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._received(components, header, stickyBasket))
    XCTAssertEqual(state.loadingState, .success)
    XCTAssertEqual(state.components, components)
    XCTAssertEqual(state.header, header)
    XCTAssertEqual(state.stickyBasket, stickyBasket)
    XCTAssertEqual(state.isLoading, false)
  }

  func test_GIVEN_initial_state_WHEN_eventIsError_THEN_loadingStateShouldBeFailure() {
    var state = ProductDetailView.ViewState.initial
    let error = ProductDetailError.notFound
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._error(error))
    XCTAssertEqual(state.loadingState, .failure(error))
  }

  func test_GIVEN_initial_state_WHEN_eventIsMonitoredView_THEN_monitoredViewStateShouldUpdate() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .monitoredView(.productGallery, false))
    XCTAssertTrue(state.showsHeaderWishlistIcon)
  }

  func test_GIVEN_initial_state_WHEN_eventIsReceivedSelectedVariant_THEN_userDidChangeVariantStateShouldUpdate() {
    var state = ProductDetailView.ViewState.initial
    ProductDetailView.ViewState.Reducer.reduce(&state, with: ._receivedSelectedVariant(.stub(userDidChangeVariant: true)))
    XCTAssertTrue(state.selectedVariant.userDidChangeVariant)
  }

  func test_GIVEN_initial_state_WHEN_eventIsIndexOfCurrentImage_THEN_indexOfCurrentImageStateShouldUpdate() {
    var state = ProductDetailView.ViewState.initial
    let indexOfCurrentImage = 1
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .indexOfCurrentImage(indexOfCurrentImage))
    XCTAssertEqual(state.indexOfCurrentImage, indexOfCurrentImage)
  }

  func test_GIVEN_StateWithIndexOfCurrentImage_WHEN_eventIsGetDetailScreenForColor_THEN_indexOfCurrentImageStateShouldUpdat() {
    var state = ProductDetailView.ViewState(indexOfCurrentImage: 1)
    ProductDetailView.ViewState.Reducer.reduce(&state, with: .getDetailScreenForColor(id: .stub))
    XCTAssertEqual(state.indexOfCurrentImage, 0)
  }
}
