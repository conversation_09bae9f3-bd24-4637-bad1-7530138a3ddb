import OGAppKitSDK

extension [OGProductDetailComponent] {
  var hasProductVariant: Bool {
    contains(where: {
      if case .productVariant = $0 {
        true
      } else {
        false
      }
    })
  }

  var productHeader: ProductHeader? {
    compactMap {
      if case let .productHeader(header) = $0 {
        return header
      } else {
        return nil
      }
    }.first
  }

  var productDimensions: ProductDimensions? {
    compactMap {
      if case let .productDimensions(productDimensions) = $0 {
        return productDimensions
      } else {
        return nil
      }
    }.first
  }

  var gallery: ProductGallery? {
    compactMap {
      if case let .productGallery(productGallery) = $0 {
        return productGallery
      } else {
        return nil
      }
    }.first
  }

  var productColor: ProductColor? {
    compactMap {
      if case let .productColor(productColor) = $0 {
        return productColor
      } else {
        return nil
      }
    }.first
  }

  var productColorDimension: ProductColorDimension? {
    compactMap {
      if case let .productColorDimension(productColorDimension) = $0 {
        return productColorDimension
      } else {
        return nil
      }
    }.first
  }

  var dynamicYieldRecommendations: [DynamicYieldRecommendations] {
    compactMap {
      if case let .dynamicYieldRecommendations(dynamicYieldRecommendations) = $0 {
        return dynamicYieldRecommendations
      } else {
        return nil
      }
    }
  }
  
  var gkAirRecommendations: [GkAirRecommendations] {
    compactMap {
      if case let .gkAirRecommendations(gkAirRecommendations) = $0 {
        return gkAirRecommendations
      } else {
        return nil
      }
    }
  }

  var productPrice: ProductPrice? {
    compactMap {
      if case let .productPrice(price) = $0 {
        return price
      } else {
        return nil
      }
    }.first
  }

  var productBasket: AddToBasketButton? {
    compactMap {
      if case let .addToBasketButton(basket) = $0 {
        return basket
      } else {
        return nil
      }
    }.first
  }

  var componentsWithoutHeader: [OGProductDetailComponent] {
    filter {
      if case .productHeader = $0 {
        false
      } else {
        true
      }
    }
  }

  var dynamicYieldBanner: DynamicYieldBanner? {
    compactMap {
      if case let .dynamicYieldBanner(banner) = $0 {
        return banner
      } else {
        return nil
      }
    }.first
  }
}

extension [OGAddToBasketSuccessComponent] {
  var componentsWithoutButtons: [OGAddToBasketSuccessComponent] {
    filter {
      if case .continueShoppingButton = $0 {
        false
      } else if case .showBasketButton = $0 {
        false
      } else {
        true
      }
    }
  }

  var continueShoppingButton: ContinueShoppingButton? {
    compactMap {
      if case let .continueShoppingButton(continueShoppingButton) = $0 {
        return continueShoppingButton
      } else {
        return nil
      }
    }.first
  }

  var showBasketButton: ShowBasketButton? {
    compactMap {
      if case let .showBasketButton(showBasketButton) = $0 {
        return showBasketButton
      } else {
        return nil
      }
    }.first
  }
}

extension [OGAddToBasketSuccessComponent] {
  var dynamicYieldRecommendations: [DynamicYieldRecommendations] {
    compactMap {
      if case let .dynamicYieldRecommendations(dynamicYieldRecommendations) = $0 {
        return dynamicYieldRecommendations
      } else {
        return nil
      }
    }
  }
}
