// Generated using OGFeatureAdapterMaker
import OGDIService

public final class ProductDetailFeatureAdapterContainer: OGDISharedContainer {

	public static var shared: ProductDetailFeatureAdapterContainer = .init()
  public var manager: OGDIContainerManager = .init()
	
  public var productDetail: OGDIService<ProductDetailFeatureAdaptable> {
		self {
      ProductDetailFeatureAdapter(configuration: nil)
		}.cached
  }
  
}
