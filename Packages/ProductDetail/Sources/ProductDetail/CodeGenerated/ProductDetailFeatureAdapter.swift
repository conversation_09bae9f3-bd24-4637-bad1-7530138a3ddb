// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros

public protocol ProductDetailFeatureConfigurable {
	var isEnabled: Bool { get set }
  var components: String{ get set }
  var basketSuccessComponents: String { get set }
}

struct ProductDetailComponents: Codable {
  public let components: [AnyJSONType]
}

struct Configuration: ProductDetailFeatureConfigurable {
  var isEnabled: Bool = false
  var components: String = ""
  var basketSuccessComponents: String = ""
}

public final class ProductDetailFeatureAdapter: OGFeatureAdapter, ProductDetailFeatureAdaptable {
	override public class var featureName: OGFeature.Name { OGIdentifier.productDetail.value }
	public let configuration: CurrentValueSubject<ProductDetailFeatureConfigurable, Never>
	private var subscriptions = Set<AnyCancellable>()
  
	public init(configuration: ProductDetailFeatureConfigurable?) {
		if let configuration = configuration  {
      self.configuration = CurrentValueSubject(configuration)
    } else {
      self.configuration = CurrentValueSubject(Configuration())
    }
		
    super.init()
    	
    receiveUpdates()
  }

	private func receiveUpdates() {
		$feature.sink { [weak self] feature in
			guard let self = self else { return }
			var updatedConfiguration = self.configuration.value
			guard let feature = feature else {
				updatedConfiguration.isEnabled = false
				self.configuration.send(updatedConfiguration)
				return
			}
			updatedConfiguration.isEnabled = feature.isEnabled
      do {
        let componentModel = try feature.customValue([AnyJSONType].self, for: OGFeatureKey.CustomValues.ProductDetail.components) ?? []
        let productDetailComponents = ProductDetailComponents(components: componentModel)
        let jsonData = try JSONEncoder().encode(productDetailComponents)
        updatedConfiguration.components = String(data: jsonData, encoding: .utf8) ?? updatedConfiguration.components
      } catch {
        logger.log(.critical, domain: .service, message: error.localizedDescription)
      }
      do {
        let componentModel = try feature.customValue([AnyJSONType].self, for: OGFeatureKey.CustomValues.ProductDetail.basketSuccessComponents) ?? []
        let basketSuccessComponents = ProductDetailComponents(components: componentModel)
        let jsonData = try JSONEncoder().encode(basketSuccessComponents)
        updatedConfiguration.basketSuccessComponents = String(data: jsonData, encoding: .utf8) ?? updatedConfiguration.basketSuccessComponents
      } catch {
        logger.log(.critical, domain: .service, message: error.localizedDescription)
      }
			self.configuration.send(updatedConfiguration)
			}.store(in: &subscriptions)
	}
}

public protocol ProductDetailFeatureAdaptable: OGFeatureAdaptable {
	var configuration: CurrentValueSubject<ProductDetailFeatureConfigurable, Never> { get }
}


extension OGFeatureKey.CustomValues {
	public enum ProductDetail: String, OGKeyReceivable {
			public var value: String  {
				rawValue
			}
			case components = "components"
      case basketSuccessComponents = "basketSuccessComponents"
		}
}


extension OGIdentifier {
	public static let productDetail = #identifier("productDetail")
}
