import Foundation
import OGAppKitSDK
import OGFeatureAdapter
import OGL10n
import OGRouter
import OGViewStore
import UICatalog
import UIKit

extension PriceView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore(
    price: ProductPrice
  )
    -> Store {
    var state = PriceView.ViewState.initial
    state.update(productPrice: price)
    return Store(
      initialState: state,
      reducer: PriceView.ViewState.Reducer.reduce,
      middleware: PriceView.ViewState.Middleware(),
      connector: PriceView.ViewState.Connector(price: price)
    )
  }
}

// MARK: - PriceView.ViewState

extension PriceView {
  struct ViewState: OGViewState {
    private(set) var price: String = ""
    private(set) var hasDiscount: Bool = false
    private(set) var oldPrice: String = ""
    private(set) var shippingCostUrl: String?
    private(set) var isLoading: Bool = true
    init(
      price: String = "",
      hasDiscount: Bool = false,
      oldPrice: String = "",
      shippingCostUrl: String? = nil,
      isLoading: Bool = true
    ) {
      self.price = price
      self.hasDiscount = hasDiscount
      self.oldPrice = oldPrice
      self.shippingCostUrl = shippingCostUrl
      self.isLoading = isLoading
    }

    var vatTextAttributed: AttributedString {
      if shippingCostUrl != nil {
        let vatText = vatTextL10n.replacingOccurrences(
          of: "{\(vatTextLinkL10n)}",
          with: vatTextLinkL10n
        )

        let attributedString = NSMutableAttributedString(string: vatText)

        if let range = vatText.range(of: vatTextLinkL10n) {
          let nsRange = NSRange(range, in: vatText)
          attributedString.addAttribute(.link, value: "http://", range: nsRange)
          attributedString.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: nsRange)
          attributedString.addAttribute(.underlineColor, value: OGColors.textPrimary.color, range: nsRange)
          attributedString.addAttribute(.foregroundColor, value: OGColors.textPrimary.color, range: nsRange)
        }

        return AttributedString(attributedString)
      } else {
        let vatText = vatTextL10n.replacingOccurrences(
          of: "{\(vatTextLinkL10n)}",
          with: vatTextLinkL10n
        )
        return AttributedString(vatText)
      }
    }

    var accessibilityLabel: String {
      if hasDiscount {
        "\(ogL10n.ProductDetail.Price.Old.Accessibility(oldPrice: oldPrice)) \(ogL10n.ProductDetail.Price.New.Accessibility(price: "\(price) \(vatText)"))"
      } else {
        "\(ogL10n.ProductDetail.Price.Accessibility(price: price)) \(vatText)"
      }
    }

    private var vatText: String {
      NSAttributedString(vatTextAttributed).string
    }

    private var vatTextL10n: String {
      ogL10n.ProductDetail.Price.Vat.Title
    }

    private var vatTextLinkL10n: String {
      ogL10n.ProductDetail.Price.Vat.Link
    }

    mutating func update(productPrice: ProductPrice) {
      isLoading = productPrice.isLoading
      let priceFormatter = ProductDetailContainer.shared.priceFormatter()
      let price = priceFormatter.format(Int(productPrice.content.price.value), currencyCode: productPrice.content.price.currency)
      var oldPrice: String? {
        if let oldValue = productPrice.content.price.oldValue?.intValue {
          return priceFormatter.format(oldValue, currencyCode: productPrice.content.price.currency)
        } else {
          return nil
        }
      }

      hasDiscount = oldPrice != nil
      self.price = price
      self.oldPrice = oldPrice ?? ""
      shippingCostUrl = productPrice.config.conditionsUrl
    }

    public static var initial = ViewState()
  }
}

// MARK: - PriceView.Event

extension PriceView {
  enum Event: OGViewEvent {
    case openShippingCosts
    /// private
    case _received(ProductPrice)
  }
}

extension PriceView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout PriceView.ViewState,
      with event: PriceView.Event
    ) {
      switch event {
      case let ._received(productPrice):
        state.update(productPrice: productPrice)
      case .openShippingCosts:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    let router: OGRoutePublishing
    let baseUrl: OGBaseUrlFeatureAdaptable
    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      baseUrl: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl()
    ) {
      self.router = router
      self.baseUrl = baseUrl
    }

    func callAsFunction(
      event: PriceView.Event,
      for state: PriceView.ViewState
    ) async
      -> PriceView.Event? {
      switch event {
      case .openShippingCosts:
        if let shippingCostUrl = state.shippingCostUrl {
          let url = baseUrl.urlRelativeToWebUrl(forUrlPath: shippingCostUrl)
          router.send(OGRoute(url: url))
        }
        return nil
      case ._received:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    let price: ProductPrice
    init(price: ProductPrice) {
      self.price = price
    }

    func configure(
      dispatch: @escaping (PriceView.Event) async -> Void
    ) async {
      await dispatch(._received(price))
    }
  }
}
