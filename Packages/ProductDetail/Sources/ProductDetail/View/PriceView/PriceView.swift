import OGAppKitSDK
import SwiftUI
import UICatalog

// MARK: - PriceView

struct PriceView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  @State private var hStacked: Bool = true
  init(price: ProductPrice, hStacked: Bool = true) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(price: price))
    _hStacked = State(wrappedValue: hStacked)
  }

  var body: some SwiftUI.View {
    content
  }

  @ViewBuilder var content: some SwiftUI.View {
    if hStacked {
      VStack(spacing: UILayoutConstants.PriceView.discountSpacing) {
        discount
        AdaptiveHStack(alignment: .lastTextBaseline) {
          price
          Spacer()
        }
      }
      .accessibilityElement(children: .ignore)
      .accessibilityLabel(viewStore.accessibilityLabel)
      .accessibilityAddTraits(.isButton)
    } else {
      VStack(alignment: .leading, spacing: .zero) {
        price
      }
      .accessibilityElement(children: .ignore)
      .accessibilityLabel(viewStore.accessibilityLabel)
      .accessibilityAddTraits(.isLink)
    }
  }

  @ViewBuilder var discount: some SwiftUI.View {
    if viewStore.hasDiscount {
      HStack {
        Text(viewStore.oldPrice)
          .foregroundColor(OGColors.textOnLight.color)
          .font(for: .priceMRegular)
          .strikethroughModifier()
          .shimmering(active: viewStore.isLoading)

        Spacer()
      }
    }
  }

  @ViewBuilder var price: some SwiftUI.View {
    Text(viewStore.price)
      .font(for: .priceL)
      .foregroundColor(viewStore.hasDiscount ? OGColors.textSale.color : OGColors.textOnLight.color)
      .multilineTextAlignment(.leading)
      .shimmering(active: viewStore.isLoading)
    vatText
  }

  @ViewBuilder var vatText: some SwiftUI.View {
    Text(viewStore.vatTextAttributed)
      .font(for: .copyS)
      .multilineTextAlignment(.leading)
      .foregroundColor(OGColors.textBlack60.color)
      .environment(\.openURL, OpenURLAction { _ in
        Task {
          await viewStore.dispatch(.openShippingCosts)
        }
        return .discarded
      })
      .shimmering(active: viewStore.isLoading)
  }
}

// MARK: - UILayoutConstants.PriceView

extension UILayoutConstants {
  enum PriceView {
    static let discountSpacing: CGFloat = 4
  }
}
