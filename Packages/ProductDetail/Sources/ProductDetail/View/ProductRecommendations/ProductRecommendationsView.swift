import Combine
import OGAppKitSDK
import OGDIService
import <PERSON>GL10n
import <PERSON><PERSON>outer
import SwiftUI
import UICatalog

// MARK: - ProductRecommendationsView

struct ProductRecommendationsView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  init(
    recommendations: any ProductRecommendations,
    trackingScenario: Interaction.ProductDetailViewRecommendationScenario = .mainContent,
    dismissRoute: OGRoute? = nil
  ) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(recommendations: recommendations, trackingScenario: trackingScenario, dismissRoute: dismissRoute))
  }

  private func visibilityChanged(_ id: Int, isVisible: ItemVisibility) {
    Task {
      await viewStore.dispatch(.index(id, visible: isVisible == .fully))
    }
  }

  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ProductRecommendationsView.verticalSpacing) {
      header
      scrollview
    }
    .padding(.bottom, UILayoutConstants.Default.padding2x)
  }

  private var scrollview: some SwiftUI.View {
    ScrollViewReader { reader in
      ScrollView(.horizontal, showsIndicators: false) {
        scrollviewContent
          .onChange(of: viewStore.productIndex) { newValue in
            withAnimation {
              reader.scrollTo(newValue, anchor: .center)
            }
          }
      }
      .simultaneousGesture(DragGesture().onEnded { _ in
        Task {
          await viewStore.dispatch(.trackInteraction)
        }
      })
    }
  }

  private var scrollviewContent: some SwiftUI.View {
    HStack(spacing: UILayoutConstants.ProductRecommendationsView.horizontalSpacing) {
      teaserImage
      ForEach(Array(viewStore.products.enumerated()), id: \.offset) { index, product in
        var accessibilityLabelCount: String {
          let numberOfCurrentProduct = "\(index + 1)"
          let numberOfProducts = String(viewStore.products.count)
          if let title = viewStore.title {
            return ogL10n.ProductDetail.Recommendation.Product.Title.Count.Accessibility(title: title, numberOfCurrentProduct: numberOfCurrentProduct, numberOfProducts: numberOfProducts)
          } else {
            return ogL10n.ProductDetail.Recommendation.Product.Count.Accessibility(numberOfCurrentProduct: numberOfCurrentProduct, numberOfProducts: numberOfProducts)
          }
        }
        let index = viewStore.imageURL == nil ? index : index + 1
        ProductRecommendationCardView(
          product: product,
          isLoading: viewStore.isLoading,
          accessibilityLabelCount: accessibilityLabelCount
        )
        .id(index)
        .onTapGesture {
          Task {
            await viewStore.dispatch(.productTapped(productId: product.productId, secondaryId: product.secondaryId))
          }
        }
      }
    }
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
  }

  @ViewBuilder private var nextButton: some SwiftUI.View {
    Button(action: {
      Task {
        await viewStore.dispatch(.moveToNext)
      }
    }, label: {
      OGImages.icon24x24ChevronRightPrimary.image
        .circleBackground()
    })
    .padding(UILayoutConstants.ProductRecommendationsView.floatingButtonPadding)
    .accessibilityHidden(true)
  }

  @ViewBuilder private var previousButton: some SwiftUI.View {
    Button(action: {
      Task {
        await viewStore.dispatch(.moveToPrevious)
      }
    }, label: {
      OGImages.icon24x24ChevronLeftPrimary.image
        .circleBackground()
    })
    .padding(UILayoutConstants.ProductRecommendationsView.floatingButtonPadding)
    .accessibilityHidden(true)
  }

  private var header: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ProductRecommendationsView.titleSpacing) {
      if let title = viewStore.title {
        Text(title)
          .componentHeadline
          .shimmering(active: viewStore.isLoading)
          .padding(.horizontal, UILayoutConstants.Default.padding2x)
      }
      if let subTitle = viewStore.subTitle {
        Text(subTitle)
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .shimmering(active: viewStore.isLoading)
          .padding(.horizontal, UILayoutConstants.Default.padding2x)
      }
    }
    .accessibilityAddTraits(.isHeader)
    .accessibilityElement(children: .combine)
  }

  @ViewBuilder private var teaserImage: some SwiftUI.View {
    if let imageURL = viewStore.imageURL {
      AsyncCachedImage(url: imageURL, content: { image in
        image
          .resizable()
          .aspectRatio(UILayoutConstants.ProductRecommendationsView.aspectRatio, contentMode: .fill)
      }, placeholder: { loadingState in
        if case .failure = loadingState {
          OGImages.icon24x24PlaceholderImg.image
        } else {
          Rectangle()
            .fill(.clear)
            .shimmering()
        }
      })
      .frame(height: UILayoutConstants.ProductRecommendationsView.teaserImageHeight)
      .cornerRadius(cornerRadius.extraSmall)
      .overlay(
        RoundedRectangle(cornerRadius: cornerRadius.extraSmall)
          .inset(by: cornerRadius.extraSmall / 4)
          .stroke(OGColors.backgroundBackground10.color, lineWidth: 1.0)
      )
      .accessibilityAddTraits(.isImage)
    }
  }
}

// MARK: - UILayoutConstants.ProductRecommendationsView

extension UILayoutConstants {
  enum ProductRecommendationsView {
    static let verticalSpacing: CGFloat = 16
    static let titleSpacing: CGFloat = 8
    static let horizontalSpacing: CGFloat = 12
    static let cardSpacing: CGFloat = 4
    static let wordSpacing: CGFloat = 4
    static let imageWidth: CGFloat = 156
    static let imageHeight: CGFloat = (imageWidth * aspectRatio.height) / aspectRatio.width
    static let teaserImageHeight: CGFloat = 318
    static let aspectRatio: CGSize = .init(width: 5, height: 7)
    static let iconPadding: CGFloat = 8
    static let imageOpacity: CGFloat = 0.2
    static let titleLineLimit: Int = 1
    static let brandPaddingTop: CGFloat = 2
    static let innerHorizontalPadding: CGFloat = 8
    static let floatingButtonPadding: CGFloat = 19
  }
}
