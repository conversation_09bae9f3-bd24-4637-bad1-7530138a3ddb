import OGAppKitSDK
import OGDIService
import OGL10n
import Swift<PERSON>
import UICatalog

// MARK: - ProductRecommendationCardView

struct ProductRecommendationCardView: SwiftUI.View {
  let product: RecommendedProduct
  let isLoading: Bool
  let accessibilityLabelCount: String
  @Environment(\.dynamicTypeSize) var dynamicTypeSize
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius

  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ProductRecommendationsView.cardSpacing) {
      if isLoading {
        loadingView
      } else {
        imageView
      }
      if let brandName = product.brandName {
        Text(brandName)
          .font(for: .caption)
          .foregroundColor(OGColors.textOnLight.color)
          .padding(.top, UILayoutConstants.ProductRecommendationsView.brandPaddingTop)
          .shimmering(active: isLoading)
          .padding(.horizontal, UILayoutConstants.ProductRecommendationsView.innerHorizontalPadding)
      }

      Text(product.title)
        .font(for: .titleS)
        .foregroundColor(OGColors.textOnLight.color)
        .lineLimit(dynamicTypeSize.isAccessibilitySize ? nil : UILayoutConstants.ProductRecommendationsView.titleLineLimit)
        .shimmering(active: isLoading)
        .padding(.horizontal, UILayoutConstants.ProductRecommendationsView.innerHorizontalPadding)
      if let formattedOldPrice = product.formattedOriginalPrice {
        Text(formattedOldPrice)
          .strikethrough()
          .font(for: .priceMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .shimmering(active: isLoading)
          .padding(.horizontal, UILayoutConstants.ProductRecommendationsView.innerHorizontalPadding)
          .accessibilityLabel(ogL10n.ProductDetail.Price.Old.Accessibility(oldPrice: formattedOldPrice))
      }
      if let formattedPrice = product.formattedPrice {
        Text(product.isStartPrice ? "\(ogL10n.ProductDetail.Recommendation.PriceDiscountTitle) \(formattedPrice)": formattedPrice)
          .font(for: .priceMEmphasized)
          .foregroundColor(product.hasDiscount ? OGColors.textSale.color : OGColors.textOnLight.color)
          .shimmering(active: isLoading)
          .padding(.horizontal, UILayoutConstants.ProductRecommendationsView.innerHorizontalPadding)
          .accessibilityLabel("\(product.hasDiscount ? ogL10n.ProductDetail.Price.New.Accessibility(price: formattedPrice) : ogL10n.ProductDetail.Price.Accessibility(price: formattedPrice))")
      }
      Spacer(minLength: 0)
    }
    .accessibilitySortPriority(1)
    .accessibilityElement(children: .combine)
    .accessibilityAddTraits(.isButton)

    .background(OGColors.backgroundBackground0.color)
    .frame(width: UILayoutConstants.ProductRecommendationsView.imageWidth)
    .overlay(alignment: .topTrailing) {
      if !isLoading {
        WishlistButtonView(
          isWishlisted: product.isWishlisted,
          productId: product.productIdForWishlisting,
          hasCircleBackground: true,
          circleBackgroundSize: UILayoutConstants.ProductRecommendationCardView.wishlistButtonSize
        )
        .padding(UILayoutConstants.ProductRecommendationCardView.wishlistButtonPadding)
      }
    }
    .accessibilityElement(children: .contain)
    .cornerRadius(cornerRadius.extraSmall)
    .overlay(
      RoundedRectangle(cornerRadius: cornerRadius.extraSmall)
        .inset(by: cornerRadius.extraSmall / 4)
        .stroke(OGColors.backgroundBackground10.color, lineWidth: UILayoutConstants.ProductRecommendationCardView.borderLineWidth)
    )
  }

  private var loadingView: some SwiftUI.View {
    Rectangle()
      .foregroundColor(.clear)
      .frame(
        width: UILayoutConstants.ProductRecommendationsView.imageWidth,
        height: UILayoutConstants.ProductRecommendationsView.imageHeight
      )
      .shimmering(active: isLoading, cornerRadius: .zero)
  }

  @ViewBuilder private var imageView: some SwiftUI.View {
    VStack(spacing: .zero) {
      AsyncCachedImage(
        url: product.imageURL,
        targetSize: CGSize(
          width: UILayoutConstants.ProductRecommendationsView.imageWidth,
          height: UILayoutConstants.ProductRecommendationsView.imageHeight
        ),
        content: { image in
          image
            .resizable()
            .aspectRatio(UILayoutConstants.ProductRecommendationsView.aspectRatio, contentMode: .fill)
        }, placeholder: { loadingState in
          if case .failure = loadingState {
            OGImages.icon24x24PlaceholderImg.image
          } else {
            Rectangle()
              .fill(.clear)
              .shimmering()
          }
        })
        .frame(
          width: UILayoutConstants.ProductRecommendationsView.imageWidth,
          height: UILayoutConstants.ProductRecommendationsView.imageHeight
        )
        .accessibilityAddTraits(.isImage)
        .accessibilityLabel(accessibilityLabelCount)
      OGColors.backgroundBackground10.color
        .frame(height: UILayoutConstants.ProductRecommendationCardView.borderLineWidth)
        .frame(width: UILayoutConstants.ProductRecommendationsView.imageWidth)
    }
  }
}

// MARK: - UILayoutConstants.ProductRecommendationCardView

extension UILayoutConstants {
  enum ProductRecommendationCardView {
    static let wishlistButtonPadding: CGFloat = 6
    static let wishlistButtonSize: CGSize = .init(width: 32, height: 32)
    static let borderLineWidth: CGFloat = 1
  }
}
