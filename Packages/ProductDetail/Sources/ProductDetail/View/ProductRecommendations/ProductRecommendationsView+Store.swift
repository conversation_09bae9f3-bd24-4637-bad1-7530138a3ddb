import Combine
import Foundation
import OGAppKitSDK
import OGCore
import OGL10n
import <PERSON><PERSON>outer
import OGTracker
import OGViewStore

extension ProductRecommendationsView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(
    recommendations: any ProductRecommendations,
    trackingScenario: Interaction.ProductDetailViewRecommendationScenario,
    dismissRoute: OGRoute?
  ) -> Store {
    Store(
      initialState: ViewState(trackingScenario: trackingScenario),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(router: OGRoutingContainer.shared.routePublisher(), dismissRoute: dismissRoute),
      connector: ViewState.Connector(recommendations: recommendations)
    )
  }

  struct ViewState: OGViewState {
    private(set) var products: [RecommendedProduct]
    private(set) var productIndex: Int
    private(set) var nextButtonIsVisible: Bool
    private(set) var previousButtonIsVisible: Bool
    private(set) var visibleProducts: Set<Int> = Set()
    private(set) var isLoading: Bool
    private(set) var title: String?
    private(set) var subTitle: String?
    private(set) var imageURL: URL?
    private(set) var didTrackInteraction: Bool
    private(set) var trackingScenario: Interaction.ProductDetailViewRecommendationScenario
    private(set) var trackingId: String?
    private var itemCount: Int {
      imageURL == nil ? products.count - 1 : products.count
    }

    init(
      trackingScenario: Interaction.ProductDetailViewRecommendationScenario,
      products: [RecommendedProduct] = [],
      productIndex: Int = 0,
      nextButtonIsVisible: Bool = false,
      previousButtonIsVisible: Bool = false,
      visibleProducts: Set<Int> = Set(),
      isLoading: Bool = true,
      title: String? = nil,
      subTitle: String? = nil,
      imageURL: URL? = nil,
      didTrackInteraction: Bool = false,
      trackingId: String? = nil
    ) {
      self.products = products
      self.productIndex = productIndex
      self.nextButtonIsVisible = nextButtonIsVisible
      self.previousButtonIsVisible = previousButtonIsVisible
      self.visibleProducts = visibleProducts
      self.isLoading = isLoading
      self.title = title
      self.subTitle = subTitle
      self.imageURL = imageURL
      self.didTrackInteraction = didTrackInteraction
      self.trackingScenario = trackingScenario
      self.trackingId = trackingId
    }

    mutating func moveToNext() {
      let newIndex = (visibleProducts.max() ?? 0) + 1
      let productIndex = productIndex == newIndex ? productIndex + 1 : newIndex
      if productIndex < itemCount {
        self.productIndex = productIndex
      } else {
        self.productIndex = itemCount
        nextButtonIsVisible = false
      }
    }

    mutating func moveToPrevious() {
      let newIndex = (visibleProducts.min() ?? 0) - 1
      let productIndex = productIndex == newIndex ? productIndex - 1 : newIndex
      if productIndex > 0 {
        self.productIndex = productIndex
      } else {
        self.productIndex = 0
        previousButtonIsVisible = false
      }
    }

    mutating func update(didTrackInteraction: Bool) {
      self.didTrackInteraction = didTrackInteraction
    }

    mutating func update(index: Int, visible: Bool) {
      if visible {
        visibleProducts.insert(index)
      } else {
        visibleProducts.remove(index)
      }
      if visible {
        productIndex = -1
      }
      previousButtonIsVisible = ((visibleProducts.min() ?? 0) > 0) && !isLoading
      nextButtonIsVisible = ((visibleProducts.max() ?? 0) < itemCount) && !isLoading
    }

    mutating func update(with recommendations: any ProductRecommendations) {
      switch onEnum(of: recommendations) {
      case let .dynamicYieldRecommendations(recommendations):
        if let titleL10n = recommendations.config.titleL10n {
          title = ogL10n.resolve(key: titleL10n)
        }
        if let subtitleL10n = recommendations.config.subtitleL10n {
          subTitle = ogL10n.resolve(key: subtitleL10n)
        }
      case let .recentlyViewedRecommendations(recommendations):
        if let titleL10n = recommendations.config.titleL10n {
          title = ogL10n.resolve(key: titleL10n)
        }
        if let subtitleL10n = recommendations.config.subtitleL10n {
          subTitle = ogL10n.resolve(key: subtitleL10n)
        }
      case let .staticProductRecommendations(recommendations):
        if let titleL10n = recommendations.config.titleL10n {
          title = ogL10n.resolve(key: titleL10n)
        }
        if let subtitleL10n = recommendations.config.subtitleL10n {
          subTitle = ogL10n.resolve(key: subtitleL10n)
        }
      case let .shopTheLookRecommendations(recommendations):
        if let titleL10n = recommendations.config.titleL10n {
          title = ogL10n.resolve(key: titleL10n)
        }
        if let subtitleL10n = recommendations.config.subtitleL10n {
          subTitle = ogL10n.resolve(key: subtitleL10n)
        }
      case let .moreFromTheSeriesRecommendations(recommendations):
        if let titleL10n = recommendations.config.titleL10n {
          title = ogL10n.resolve(key: titleL10n)
        }
        if let subtitleL10n = recommendations.config.subtitleL10n {
          subTitle = ogL10n.resolve(key: subtitleL10n)
        }
      }
      switch onEnum(of: recommendations.state) {
      case let .done(state):
        products = state.content.products.map(\.toRecommendedProduct)
        if let url = (state.content as? ProductRecommendationsContent)?.image?.url {
          imageURL = URL(string: url)
        }
        trackingId = state.content.trackingId

        isLoading = false
      case let .loading(state):
        products = state.placeholderContent.products.map(\.toRecommendedProduct)
        isLoading = true
      }
    }

    static var initial = ViewState(trackingScenario: .mainContent)
  }

  enum Event: OGViewEvent, Equatable {
    case moveToNext
    case moveToPrevious
    case index(Int, visible: Bool)
    case productTapped(productId: String, secondaryId: String? = nil)
    case trackInteraction
    /// private
    case _setRecommendations(with: any ProductRecommendations)
    case _didTrackInteraction
    case _trackSelection(productId: String)

    static func == (lhs: Event, rhs: Event) -> Bool {
      switch (lhs, rhs) {
      case let (.productTapped(lhsProductId, lhsSecondaryId), .productTapped(rhsProductId, rhsSecondaryId)):
        return lhsProductId == rhsProductId && lhsSecondaryId == rhsSecondaryId
      case let (._setRecommendations(lhsRec), ._setRecommendations(rhsRec)):
        return lhsRec.state as? ProductRecommendationsContent == rhsRec.state as? ProductRecommendationsContent
      case (.moveToNext, .moveToNext):
        return true
      case let (.index(lhsId, lhsVisible), .index(rhsId, rhsVisible)):
        return lhsId == rhsId && lhsVisible == rhsVisible
      case (.moveToPrevious, .moveToPrevious):
        return true
      case (.trackInteraction, .trackInteraction):
        return true
      case (._didTrackInteraction, ._didTrackInteraction):
        return true
      case let (._trackSelection(lhsProductId), ._trackSelection(rhsProductId)):
        return lhsProductId == rhsProductId
      default:
        return false
      }
    }
  }
}

extension ProductRecommendationsView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductRecommendationsView.ViewState,
      with event: ProductRecommendationsView.Event
    ) {
      switch event {
      case let ._setRecommendations(recommendations):
        state.update(with: recommendations)
      case .productTapped:
        break
      case .moveToNext:
        state.moveToNext()
      case .moveToPrevious:
        state.moveToPrevious()
      case let .index(index, visible: visible):
        state.update(index: index, visible: visible)
      case ._didTrackInteraction:
        state.update(didTrackInteraction: true)
      case ._trackSelection, .trackInteraction:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    let router: OGRoutePublishing
    let dismissRoute: OGRoute?
    let tracker: OGTrackerProtocol
    init(
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker(),
      router: OGRoutePublishing,
      dismissRoute: OGRoute?
    ) {
      self.router = router
      self.dismissRoute = dismissRoute
      self.tracker = tracker
    }

    func callAsFunction(
      event: ProductRecommendationsView.Event,
      for state: ProductRecommendationsView.ViewState
    ) async -> ProductRecommendationsView.Event? {
      switch event {
      case let .productTapped(productId, secondaryId):
        guard !state.isLoading else { return nil }
        if let dismissRoute {
          router.dismiss(route: dismissRoute)
        }
        let route = OGRoute.productDetail(data: ProductDetailDestinationProvider.RouteData(productId: productId, secondaryId: secondaryId))
        router.send(route)
        return ._trackSelection(productId: productId)
      case let ._trackSelection(productId):
        if let trackingId = state.trackingId, let product = state.products.first(where: { $0.productId == productId }) {
          tracker.multiplatformTrack(event: Interaction.ProductDetailSelectVariantByRecommendations(
            recommendationsId: trackingId,
            itemId: product.productId,
            itemName: product.title
          ))
        }
        return nil
      case .trackInteraction:
        guard !state.didTrackInteraction, let trackingId = state.trackingId else { return nil }
        tracker.multiplatformTrack(event: Interaction.ProductDetailViewRecommendation(
          eventScenario: state.trackingScenario,
          recommendationsId: trackingId
        ))
        return ._didTrackInteraction
      case .moveToNext, .moveToPrevious:
        return .trackInteraction
      case ._didTrackInteraction, ._setRecommendations, .index: return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    let recommendations: any ProductRecommendations

    init(recommendations: any ProductRecommendations) {
      self.recommendations = recommendations
    }

    func configure(
      dispatch: @escaping (ProductRecommendationsView.Event) async -> Void
    ) async {
      await dispatch(._setRecommendations(with: recommendations))
    }
  }
}

// MARK: - RecommendedProduct

struct RecommendedProduct: Equatable {
  let productId: String
  let secondaryId: String?
  let productIdForWishlisting: String
  let imageURL: URL?
  let brandName: String?
  let title: String
  let hasDiscount: Bool
  let formattedPrice: String?
  let formattedOriginalPrice: String?
  let isWishlisted: Bool
}

extension ProductRecommendationsRecommendedProduct {
  var toRecommendedProduct: RecommendedProduct {
    RecommendedProduct(
      productId: productId,
      secondaryId: secondaryId,
      productIdForWishlisting: productIdForWishlisting,
      imageURL: URL(string: image.url),
      brandName: brandName,
      title: title,
      hasDiscount: price?.oldValue != nil,
      formattedPrice: formattedPrice,
      formattedOriginalPrice: formattedOriginalPrice,
      isWishlisted: isWishlisted
    )
  }
}

extension ProductRecommendationsRecommendedProduct {
  var formattedPrice: String? {
    guard let price else { return nil }
    let priceFormatter = ProductDetailContainer.shared.priceFormatter()
    return priceFormatter.format(
      Int(price.value),
      currencyCode: price.currency
    )
  }

  var formattedOriginalPrice: String? {
    guard let price else { return nil }
    guard let oldValue = price.oldValue else { return nil }
    let priceFormatter = ProductDetailContainer.shared.priceFormatter()
    return priceFormatter.format(
      Int(truncating: oldValue),
      currencyCode: price.currency
    )
  }
}
