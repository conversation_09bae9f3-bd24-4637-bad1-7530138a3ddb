import OGAppKitSDK
import OGDIService
import SwiftUI
import UICatalog

// MARK: - PaymentCalculatorView

struct PaymentCalculatorView: SwiftUI.View {
  /// let paymentCalculator: ProductPaymentCalculator
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  @StateObject private var viewStore: Store
  init() {
    _viewStore = StateObject(wrappedValue: Self.makeStore())
  }

  var body: some SwiftUI.View {
    HStack(alignment: .center, spacing: .zero) {
      OGImages.icon24x24InstalmentCalculator.image
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .padding(.vertical, UILayoutConstants.PaymentCalculatorView.imageVerticalPadding)
        .accessibility(hidden: true)
      VStack(alignment: .leading) {
        Text(viewStore.financeTitle)
          .font(for: OGFonts.titleS)
          .foregroundColor(OGColors.textOnLight.color)
        Text(viewStore.buttonTitle)
          .underline()
          .font(for: OGFonts.copyMRegular)
          .foregroundColor(OGColors.textPrimary.color)
          .onTapGesture {
            Task {
              await viewStore.dispatch(.showPaymentCalculator)
            }
          }
          .accessibilityAddTraits(.isButton)
      }
      .accessibilityElement(children: .combine)
      .padding(.vertical, UILayoutConstants.PaymentCalculatorView.textBoxVerticalPadding)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
      Spacer()
    }
    .frame(minHeight: UILayoutConstants.PaymentCalculatorView.minHeight)
    .frame(maxWidth: .infinity)
    .background(OGColors.backgroundBackground0.color)
    .cornerRadius(cornerRadius.medium)
    .overlay(
      RoundedRectangle(cornerRadius: cornerRadius.medium)
        .inset(by: UILayoutConstants.PaymentCalculatorView.borderWidth / 2)
        .stroke(OGColors.backgroundBackground20.color, lineWidth: UILayoutConstants.PaymentCalculatorView.borderWidth)
    )
    .shimmering(active: viewStore.isLoading, cornerRadius: cornerRadius.medium)
  }
}

// MARK: - UILayoutConstants.PaymentCalculatorView

extension UILayoutConstants {
  enum PaymentCalculatorView {
    static let minHeight: CGFloat = 70
    static let spacing: CGFloat = 12
    static let imageVerticalPadding: CGFloat = 23
    static let textBoxVerticalPadding: CGFloat = 12
    static let borderWidth: CGFloat = 1.0
  }
}
