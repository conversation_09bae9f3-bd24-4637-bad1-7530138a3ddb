import Combine
import Foundation
import <PERSON>GAppKitSDK
import OGCore
import OGFeatureAdapter
import OGL10n
import <PERSON>GRouter
import OGTracker
import OGViewStore

extension PaymentCalculatorView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore() -> Store {
    Store(
      initialState: ViewState(),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(),
      connector: ViewState.Connector()
    )
  }

  struct ViewState: OGViewState {
    private(set) var isLoading: Bool = true
    var financeTitle: String {
      // ogL10n.ProductDetail.PaymentCalculatorView.finance(for: loan)
      "Finanziere ab 6,91 € im Monat"
    }

    var buttonTitle: String {
      // "ogL10n.ProductDetail.PaymentCalculatorView.Calculator.Button.Title"
      "Rechner"
    }

    private var paymentCalculatorUrl: String?
    private var loan: String = ""

    mutating func update() {
      isLoading = false
    }

    static var initial = ViewState()
  }

  enum Event: OGViewEvent, Equatable {
    case showPaymentCalculator
    /// private
    case _setPaymentCalculator
    case _trackInteraction
  }
}

extension PaymentCalculatorView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout PaymentCalculatorView.ViewState,
      with event: PaymentCalculatorView.Event
    ) {
      switch event {
      case ._setPaymentCalculator:
        state.update()
      case ._trackInteraction, .showPaymentCalculator:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let tracker: OGTrackerProtocol
    private let router: OGRoutePublishing
    private let baseUrl: OGBaseUrlFeatureAdaptable
    init(
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker(),
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      baseUrl: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl()
    ) {
      self.tracker = tracker
      self.router = router
      self.baseUrl = baseUrl
    }

    func callAsFunction(
      event: PaymentCalculatorView.Event,
      for state: PaymentCalculatorView.ViewState
    ) async -> PaymentCalculatorView.Event? {
      switch event {
      case .showPaymentCalculator:
        guard let url = state.paymentCalculatorUrl else { return nil }
        let paymentCalculatorUrl = baseUrl.urlRelativeToWebUrl(forUrlPath: url)
        var route = OGRoute(.productDetailSubFlow, data: paymentCalculatorUrl)
        router.send(route)
        return ._trackInteraction
      case ._setPaymentCalculator:
        return nil
      case ._trackInteraction:
        // tracker.multiplatformTrack(event: Interaction)
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    init() {}

    func configure(
      dispatch: @escaping (PaymentCalculatorView.Event) async -> Void
    ) async {
      await dispatch(._setPaymentCalculator)
    }
  }
}
