import OGAppKitSDK
import OGDIService
import OGL10n
import Swift<PERSON>
import UICatalog

// MARK: - ColorDimensionView

struct ColorDimensionView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  init(colorDimension: ProductColorDimension, selectedProductId: @escaping (String) -> Void) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(colorDimension: colorDimension, selectedProductId: selectedProductId))
  }

  var body: some SwiftUI.View {
    VStack(spacing: UILayoutConstants.ColorDimensionView.spacing) {
      colorName
        .accessibilityHidden(true)
      colorSelection
    }
  }

  var colorName: some SwiftUI.View {
    ColorNameView(name: viewStore.colorName, isLoading: viewStore.isLoading)
  }

  var colorSelection: some SwiftUI.View {
    ScrollView(.horizontal, showsIndicators: false) {
      HStack(spacing: .zero) {
        ForEach(Array(viewStore.colors.enumerated()), id: \.offset) { index, color in
          if viewStore.isLoading {
            loadingView
          } else {
            imageView(color: color, number: index + 1)
          }
        }
      }
    }
    .padding(.horizontal, UILayoutConstants.ColorDimensionView.scrollViewPadding)
    .scrollIsDisabled(viewStore.isLoading)
  }

  var loadingView: some SwiftUI.View {
    Rectangle()
      .foregroundColor(.clear)
      .frame(
        width: UILayoutConstants.ColorDimensionView.imageWidth,
        height: UILayoutConstants.ColorDimensionView.imageHeight
      )
      .selectedBorder(false)
      .shimmering(active: viewStore.isLoading, cornerRadius: cornerRadius.small)
      .padding(.horizontal, UILayoutConstants.ColorDimensionView.imagePadding)
  }

  @ViewBuilder
  func imageView(color: ColorDimensionView.Color, number: Int) -> some SwiftUI.View {
    AsyncCachedImage(
      url: color.url,
      targetSize: CGSize(
        width: UILayoutConstants.ColorDimensionView.imageWidth,
        height: UILayoutConstants.ColorDimensionView.imageHeight
      ),
      aspectRatio: UILayoutConstants.ColorDimensionView.aspectRatio
    ) { image in
      colorImage(image: image, with: color)
        .onTapGesture {
          Task {
            await viewStore.dispatch(.selectColor(color))
          }
        }
        .accessibilityRemoveTraits(.isImage)
        .accessibilityAddTraits(color.isSelected ? .isSelected : .isButton)
        .accessibilityLabel(number == 1 ? "\(ogL10n.ProductDetail.Colors.Title.Accessibility). \(ogL10n.ProductDetail.Colors.Accessibility(name: color.name, numberOfCurrentColor: String(number), numberOfColors: String(viewStore.colors.count), availabilityInfo: color.availabilityInfo))" : (ogL10n.ProductDetail.Colors.Accessibility(name: color.name, numberOfCurrentColor: String(number), numberOfColors: String(viewStore.colors.count), availabilityInfo: color.availabilityInfo)))
    } placeholder: { loadingState in
      placeholder(loadingState: loadingState, color: color)
    }
    .frame(
      width: UILayoutConstants.ColorDimensionView.imageWidth,
      height: UILayoutConstants.ColorDimensionView.imageHeight
    )
    .selectedBorder(color.isSelected)
    .notAvailableOverlay(color.isAvailable)

    .cornerRadius(cornerRadius.small)
    .padding(.horizontal, UILayoutConstants.ColorDimensionView.imagePadding)
  }

  @ViewBuilder
  func placeholder(loadingState: Loadable, color: Color) -> some SwiftUI.View {
    if case .failure = loadingState {
      OGImages.icon24x24PlaceholderImg.image
        .onTapGesture {
          Task {
            await viewStore.dispatch(.selectColor(color))
          }
        }
        .accessibilityAddTraits(.isButton)
        .accessibilityLabel(color.name)
    } else {
      Rectangle()
        .fill(.clear)
        .shimmering()
    }
  }

  func colorImage(image: SwiftUI.Image, with color: ColorDimensionView.Color) -> some SwiftUI.View {
    image
      .resizable()
      .aspectRatio(UILayoutConstants.ColorDimensionView.aspectRatio, contentMode: .fit)
  }
}

// MARK: - UILayoutConstants.ColorDimensionView

extension UILayoutConstants {
  enum ColorDimensionView {
    static let imageWidth: CGFloat = 40
    static let imageHeight: CGFloat = (imageWidth * aspectRatio.height) / aspectRatio.width
    static let aspectRatio: CGSize = .init(width: 5, height: 7)
    static let imagePadding: CGFloat = 4
    static let borderWidth: CGFloat = 2
    static let diagonalLineWith: CGFloat = 2
    static let scrollViewPadding: CGFloat = -4
    static let spacing: CGFloat = 10
  }
}
