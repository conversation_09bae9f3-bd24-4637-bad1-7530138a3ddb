import Combine
import OGL10n
import OGRouter
import OGWebBridge
import OGWebView
import SwiftUI
import UICatalog

// MARK: - ProductDetailSubView

struct ProductDetailSubView: View {
  @StateObject private var webViewStore: OGWebViewStore
  @Environment(\.dismiss) private var dismiss

  init(route: OGRoute, publisher: CurrentValueSubject<Decodable, Never>?) {
    let webBridge = OGWebBridge()
    webBridge.addActionHandler(SelectProductActionHandler(selectProduct: publisher))
    webBridge.addActionHandler(ReminderSignupActionHandler())
    self._webViewStore = StateObject(wrappedValue: OGWebViewStore(route: route, webBridge: webBridge))
  }

  var body: some View {
    header
    content
  }

  @ViewBuilder private var content: some View {
    OGWebViewProgressView(estimatedProgress: $webViewStore.estimatedProgress)
    OGWebView(with: webViewStore.webView)
  }

  var header: some View {
    HStack {
      if webViewStore.webView.canGoBack {
        OGWebViewBackButton {
          webViewStore.webView.goBack()
        }
      }
      Spacer()
      ProductDetailCloseButton(accessibilityLabel: ogL10n.General.Close, dropShadow: false) {
        dismiss()
      }
    }
    .padding()
    .frame(maxWidth: .infinity)
    .frame(maxHeight: UILayoutConstants.ProductDetailSubView.maxHeight)
    .background(OGColors.backgroundBarNavigationBar.color)
  }
}

// MARK: - UILayoutConstants.ProductDetailSubView

extension UILayoutConstants {
  enum ProductDetailSubView {
    static let maxHeight: CGFloat = 56
  }
}
