import OGCore
import OGIdentifier
import OGRouter
import SwiftUI

// MARK: - ProductDetailSubFlowDestinationProvider

public struct ProductDetailSubFlowDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier = OGIdentifier.productDetailSubFlow

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    if let url: String = route.getData() {
      ProductDetailSubView(route: OGRoute(url: URL(string: url)), publisher: route.publisher)
    }
  }

  public func presentationType() -> OGPresentationType {
    .sheet([.large])
  }
}

extension OGIdentifier {
  public static let productDetailSubFlow = #identifier("productDetailSubFlow")
}

extension OGRoute {
  public static let productDetailSubFlow = OGRoute(OGIdentifier.productDetailSubFlow.value)
}
