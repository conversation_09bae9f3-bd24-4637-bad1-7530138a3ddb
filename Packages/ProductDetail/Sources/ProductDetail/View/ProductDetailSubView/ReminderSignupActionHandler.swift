import OGDIService
import <PERSON><PERSON>outer
import OGWebBridge

struct ReminderSignupActionHandler: OGWebBridgeActionHandlable {
  private let router: OGRoutePublishing
  init(router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher()) {
    self.router = router
  }

  var webBridgeNames: [String] { ["pdp.reminderSignup"] }

  func canHandleUpdateAction(_ name: String) -> Bool {
    webBridgeNames.contains(name)
  }

  func handleUpdateAction(_ action: String, data: [String: Any]) {
    router.dismiss(route: .productDetailSubFlow)
  }
}
