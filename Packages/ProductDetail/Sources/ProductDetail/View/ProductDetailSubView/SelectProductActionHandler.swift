import Combine
import OGWebBridge

struct SelectProductActionHandler: OGWebBridgeActionHandlable {
  let selectProduct: CurrentValueSubject<Decodable, Never>?
  init(selectProduct: CurrentValueSubject<Decodable, Never>?) {
    self.selectProduct = selectProduct
  }

  var webBridgeNames: [String] { ["pdp.selectProduct"] }

  func canHandleUpdateAction(_ name: String) -> Bool {
    webBridgeNames.contains(name)
  }

  func handleUpdateAction(_ action: String, data: [String: Any]) {
    if let productID = data["body"] as? String {
      selectProduct?.send(productID)
    }
  }
}
