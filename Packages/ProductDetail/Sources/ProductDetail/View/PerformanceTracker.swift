import FirebasePerformance

// MARK: - PerformanceTracking

protocol PerformanceTracking {
  func startTrace(name: String) async
  func stopTrace() async
}

// MARK: - PerformanceTracker

actor PerformanceTracker: PerformanceTracking {
  private var trace: Trace?

  func startTrace(name: String) async {
    trace = FirebasePerformance.Performance.startTrace(name: name)
  }

  func stopTrace() async {
    trace?.stop()
    trace = nil
  }
}
