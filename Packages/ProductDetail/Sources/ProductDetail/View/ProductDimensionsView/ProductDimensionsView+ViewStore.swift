import Combine
import Foundation
import OGAppKitSDK
import OGCore
import OGFeatureAdapter
import OGL10n
import OGRouter
import OGTracker
import OGViewStore

extension ProductDimensionsView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(productDimensions: ProductDimensions, showVariantSelection: @escaping () -> Void, didSelectSize: @escaping (String) -> Void) -> Store {
    let selectProduct: CurrentValueSubject<Decodable, Never> = .init("")
    var state = ViewState()
    state.update(with: productDimensions)
    return Store(
      initialState: state,
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(
        didSelectSize: didSelectSize,
        showVariantSelection: showVariantSelection,
        selectProduct: selectProduct
      ),
      connector: ViewState.Connector(productDimensions: productDimensions, selectProduct: selectProduct)
    )
  }

  struct ViewState: OGViewState {
    private(set) var dimensionTitleName: String = ""
    private(set) var variantName: String = ""
    private(set) var isLoading: Bool = true
    private(set) var hasSizeAdvisor: Bool = false
    private var sizeAdvisorUrl: String?

    init(
      dimensionTitleName: String = "",
      variantName: String = "",
      sizeAdvisorUrl: String? = nil,
      isLoading: Bool = true
    ) {
      self.dimensionTitleName = dimensionTitleName
      self.variantName = variantName
      self.sizeAdvisorUrl = sizeAdvisorUrl
      self.isLoading = isLoading
      self.hasSizeAdvisor = sizeAdvisorUrl != nil
    }

    mutating func update(with dimensions: ProductDimensions) {
      isLoading = dimensions.isLoading
      let priceFormatter = ProductDetailContainer.shared.priceFormatter()
      let variantSelectionModel = dimensions.content.toVariantSelectionModel(
        style: dimensions.config.style.toDimensionStyle,
        priceFormatter: priceFormatter
      )
      variantName = variantSelectionModel?.selectedVariantName ?? variantName
      if (dimensions.content as? ProductDimensions.VoucherDimension) != nil {
        dimensionTitleName = variantSelectionModel?.nestedDimensions?.name ?? ogL10n.ProductDetail.Size.Title
      } else {
        dimensionTitleName = ogL10n.ProductDetail.Size.Title
      }

      hasSizeAdvisor = dimensions.content.sizeAdvisorUrl != nil
      if let url = dimensions.content.sizeAdvisorUrl {
        sizeAdvisorUrl = url
      }
    }

    static var initial = ViewState()
  }

  enum Event: OGViewEvent, Equatable {
    case showVariantSelection
    case showSizeAdvisor
    /// private
    case _setDimensions(with: ProductDimensions)
    case _trackInteraction
    case _didSelectSize(String)
  }
}

extension ProductDimensionsView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductDimensionsView.ViewState,
      with event: ProductDimensionsView.Event
    ) {
      switch event {
      case let ._setDimensions(dimensions):
        state.update(with: dimensions)
      case ._didSelectSize, ._trackInteraction, .showSizeAdvisor, .showVariantSelection:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let showVariantSelection: (() -> Void)?
    private let didSelectSize: ((String) -> Void)?
    private let tracker: OGTrackerProtocol
    private let router: OGRoutePublishing
    private let baseUrl: OGBaseUrlFeatureAdaptable
    private let selectProduct: CurrentValueSubject<Decodable, Never>
    init(
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker(),
      didSelectSize: ((String) -> Void)?,
      showVariantSelection: (() -> Void)?,
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      baseUrl: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl(),
      selectProduct: CurrentValueSubject<Decodable, Never>
    ) {
      self.tracker = tracker
      self.showVariantSelection = showVariantSelection
      self.router = router
      self.baseUrl = baseUrl
      self.selectProduct = selectProduct
      self.didSelectSize = didSelectSize
    }

    func callAsFunction(
      event: ProductDimensionsView.Event,
      for state: ProductDimensionsView.ViewState
    ) async -> ProductDimensionsView.Event? {
      switch event {
      case let ._didSelectSize(productId):
        didSelectSize?(productId)
        router.dismiss(route: .productDetailSubFlow)
        return nil
      case .showVariantSelection:
        showVariantSelection?()
        return ._trackInteraction
      case .showSizeAdvisor:
        guard let url = state.sizeAdvisorUrl else { return nil }
        let sizeAdvisorUrl = baseUrl.urlRelativeToWebUrl(forUrlPath: url)
        var route = OGRoute(.productDetailSubFlow, data: sizeAdvisorUrl)
        route.set(publisher: selectProduct)
        router.send(route)
        return nil
      case ._setDimensions:
        return nil
      case ._trackInteraction:
        tracker.multiplatformTrack(event: Interaction.ProductDetailViewVariants(dimensionName: state.dimensionTitleName))
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    let productDimensions: ProductDimensions
    let selectProduct: CurrentValueSubject<Decodable, Never>
    private var cancellables = Set<AnyCancellable>()
    init(
      productDimensions: ProductDimensions,
      selectProduct: CurrentValueSubject<Decodable, Never>
    ) {
      self.productDimensions = productDimensions
      self.selectProduct = selectProduct
    }

    func configure(
      dispatch: @escaping (ProductDimensionsView.Event) async -> Void
    ) async {
      await dispatch(._setDimensions(with: productDimensions))
      selectProduct
        .compactMap { $0 as? String }
        .sink { productId in
          guard !productId.isEmpty else { return }
          Task {
            await dispatch(._didSelectSize(productId))
          }
        }.store(in: &cancellables)
    }
  }
}
