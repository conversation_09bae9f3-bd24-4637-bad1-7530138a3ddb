import OGAppKitSDK
import OGDIService
import OGL10n
import Swift<PERSON>
import UICatalog

// MARK: - ProductDimensionsView

struct ProductDimensionsView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  init(
    productDimensions: ProductDimensions,
    showVariantSelection: @escaping () -> Void,
    didSelectSize: @escaping (String) -> Void
  ) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(productDimensions: productDimensions, showVariantSelection: showVariantSelection, didSelectSize: didSelectSize))
  }

  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ProductDimensionsView.verticalSpacing) {
      HStack {
        dimensionTitleName
        Spacer()
        sizeAdvisor
      }
      button
    }
    .frame(maxWidth: .infinity, alignment: .leading)
  }

  private var button: some SwiftUI.View {
    Button {
      Task {
        await viewStore.dispatch(.showVariantSelection)
      }
    } label: {
      HStack(spacing: UILayoutConstants.Default.padding) {
        Text(viewStore.variantName)
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .lineLimit(1)
          .accessibilityLabel(ogL10n.ProductDetail.Size.Accessibility(variantName: viewStore.variantName))
        Spacer()
        OGImages.icon16x16ChevronDownPrimary.image
          .accessibilityHidden(true)
      }
      .padding(.horizontal, UILayoutConstants.ProductDimensionsView.innerButtonPaddingHorizontal)
      .frame(minHeight: UILayoutConstants.ProductDimensionsView.buttonHeight)
      .background(OGColors.backgroundBackground0.color)
      .cornerRadius(cornerRadius.full)
      .overlay(
        RoundedRectangle(cornerRadius: cornerRadius.full)
          .stroke(viewStore.isLoading ? .clear : OGColors.backgroundBackground100.color, lineWidth: UILayoutConstants.ProductDimensionsView.borderWidth)
      )
    }
    .frame(minHeight: UILayoutConstants.ProductDimensionsView.buttonHeight)
    .shimmering(active: viewStore.isLoading, cornerRadius: cornerRadius.full)
  }

  private var dimensionTitleName: some SwiftUI.View {
    Text(viewStore.dimensionTitleName)
      .font(for: .titleS)
      .foregroundColor(OGColors.textOnLight.color)
      .multilineTextAlignment(.leading)
      .shimmering(active: viewStore.isLoading)
      .accessibilityHidden(true)
  }

  @ViewBuilder private var sizeAdvisor: some SwiftUI.View {
    if viewStore.hasSizeAdvisor {
      Text(ogL10n.ProductDetail.Size.Advisor.Button.Title)
        .underline()
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textPrimary.color)
        .shimmering(active: viewStore.isLoading)
        .onTapGesture {
          Task {
            await viewStore.dispatch(.showSizeAdvisor)
          }
        }
        .accessibilityAddTraits(.isButton)
    }
  }
}

// MARK: - UILayoutConstants.ProductDimensionsView

extension UILayoutConstants {
  enum ProductDimensionsView {
    static let verticalSpacing: CGFloat = 8
    static let innerButtonPaddingHorizontal: CGFloat = 24
    static let borderWidth: CGFloat = 1
    static let verticalPadding: CGFloat = 16
    static let buttonHeight: CGFloat = 48.0
  }
}
