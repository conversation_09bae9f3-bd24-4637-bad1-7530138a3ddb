import SwiftUI

// MARK: - DeviceRotationViewModifier

struct DeviceRotationViewModifier: ViewModifier {
  let action: (UIInterfaceOrientation) -> Void

  func body(content: Content) -> some View {
    content
      .onAppear {
        detectInterfaceOrientation()
      }
      .onReceive(NotificationCenter.default.publisher(for: UIDevice.orientationDidChangeNotification)) { _ in
        detectInterfaceOrientation()
      }
  }

  private func detectInterfaceOrientation() {
    let scene = UIApplication.shared.connectedScenes
      .filter { $0.activationState == .foregroundActive }
      .map { $0 as? UIWindowScene }
      .compactMap { $0 }
      .first

    if let scene {
      action(scene.interfaceOrientation)
    } else {
      switch UIDevice.current.orientation {
      case .portrait, .portraitUpsideDown:
        action(UIInterfaceOrientation.portrait)
      case .landscapeLeft, .landscapeRight:
        action(UIInterfaceOrientation.landscapeLeft)
      case .faceDown, .faceUp, .unknown:
        break
      @unknown default:
        break
      }
    }
  }
}

extension View {
  func onRotate(perform action: @escaping (UIInterfaceOrientation) -> Void) -> some View {
    modifier(DeviceRotationViewModifier(action: action))
  }
}
