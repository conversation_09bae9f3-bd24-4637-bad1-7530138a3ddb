import OGL10n
import SwiftUI
import UICatalog

// MARK: - ShimmerEffect

public struct ShimmerEffect: ViewModifier {
  private let cornerRadius: CGFloat

  private var gradientColors = [
    OGColors.backgroundBackground20.color,
    OGColors.backgroundBackground0.color,
    OGColors.backgroundBackground20.color
  ]

  @State private var startPoint: UnitPoint = .init(x: -1.8, y: -1.2)
  @State private var endPoint: UnitPoint = .init(x: 0, y: 0.5)

  public init(cornerRadius: CGFloat) {
    self.cornerRadius = cornerRadius
  }

  public func body(content: Content) -> some View {
    content
      .opacity(.zero)
      .overlay(content: {
        RoundedRectangle(cornerRadius: cornerRadius)
          .fill(
            LinearGradient(colors: gradientColors, startPoint: startPoint, endPoint: endPoint)
          )
      })
      .onAppear {
        withAnimation(.easeInOut(duration: 1.5)
          .repeatForever(autoreverses: false)) {
            startPoint = .init(x: 1.5, y: 0.5)
            endPoint = .init(x: 2.5, y: 0.5)
          }
      }
      .accessibilityLabel(ogL10n.ProductDetail.Loading.Accessibility)
  }
}

extension View {
  @ViewBuilder
  public func shimmering(
    active: Bool = true,
    cornerRadius: CGFloat = 5
  ) -> some View {
    if active {
      modifier(ShimmerEffect(cornerRadius: cornerRadius))
    } else {
      self
    }
  }
}
