import SwiftUI

extension View {
  public var scrollSnapLayout: some View {
    modifier(ScrollSnapLayout())
  }

  public func snapBehavior(_ edges: Edge.Set = .horizontal, safeAreaPadding: CGFloat = 0) -> some View {
    modifier(SnapBehavior(edges: edges, safeAreaPadding: safeAreaPadding))
  }
}

// MARK: - ScrollSnapLayout

public struct ScrollSnapLayout: ViewModifier {
  public func body(content: Content) -> some View {
    if #available(iOS 17.0, *) {
      content
        .scrollTargetLayout()
    } else {
      content
    }
  }
}

// MARK: - SnapBehavior

public struct SnapBehavior: ViewModifier {
  let edges: Edge.Set
  let safeAreaPadding: CGFloat

  public func body(content: Content) -> some View {
    if #available(iOS 17.0, *) {
      content
        .scrollTargetBehavior(.viewAligned)
        .safeAreaPadding(edges, safeAreaPadding)
    } else {
      content
    }
  }
}
