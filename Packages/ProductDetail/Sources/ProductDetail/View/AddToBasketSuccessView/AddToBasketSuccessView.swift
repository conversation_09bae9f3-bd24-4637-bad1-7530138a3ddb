import OGDIService
import OGL10n
import OGRouter
import SwiftUI
import UICatalog

// MARK: - AddToBasketSuccessView

struct AddToBasketSuccessView: View {
  @StateObject private var viewStore: Self.Store
  @OGInjected(\ProductDetailContainer.buttonStyleResolver) var buttonStyleResolver
  @State private var orientation: UIInterfaceOrientation = .portrait

  public init(id: String) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(id: id))
  }

  var body: some View {
    Group {
      header
      ScrollView {
        LazyVStack(alignment: .leading, spacing: .zero) {
          ForEach(viewStore.components, id: \.self) { component in
            viewForComponent(component: component)
          }
        }
      }
      ComponentDivider()
      Spacer()
      buttons
        .padding(UILayoutConstants.Default.padding2x)
    }
    .onRotate { orientation in
      self.orientation = orientation
    }
    .onAppear {
      Task {
        await viewStore.dispatch(.trackScreenView)
      }
    }
  }

  @ViewBuilder
  private func viewForComponent(component: OGAddToBasketSuccessComponent) -> some View {
    switch component {
    case let .addedProduct(product):
      AddedProductView(product: product)
    case .continueShoppingButton:
      EmptyView()
    case .showBasketButton:
      EmptyView()
    case let .dynamicYieldRecommendations(recommendations):
      ProductRecommendationsView(
        recommendations: recommendations,
        trackingScenario: .confirmation,
        dismissRoute: OGRoute.addToBasketSuccess
      )
      .padding(.vertical, UILayoutConstants.Default.padding2x)
    case let .recentlyViewedRecommendations(recommendations):
      ProductRecommendationsView(
        recommendations: recommendations,
        trackingScenario: .confirmation,
        dismissRoute: OGRoute.addToBasketSuccess
      )
      .padding(.vertical, UILayoutConstants.Default.padding2x)
    case let .staticProductRecommendations(recommendations):
      ProductRecommendationsView(
        recommendations: recommendations,
        trackingScenario: .confirmation,
        dismissRoute: OGRoute.addToBasketSuccess
      )
      .padding(.vertical, UILayoutConstants.Default.padding2x)
    case let .shopTheLookRecommendations(recommendations):
      ProductRecommendationsView(
        recommendations: recommendations,
        trackingScenario: .confirmation,
        dismissRoute: OGRoute.addToBasketSuccess
      )
      .padding(.vertical, UILayoutConstants.Default.padding2x)
    case let .moreFromTheSeriesRecommendations(recommendations):
      ProductRecommendationsView(
        recommendations: recommendations,
        trackingScenario: .confirmation,
        dismissRoute: OGRoute.addToBasketSuccess
      )
      .padding(.vertical, UILayoutConstants.Default.padding2x)
    case let .gkAirRecommendations(recommendations):
      ProductRecommendationsView(
        recommendations: recommendations,
        trackingScenario: .confirmation,
        dismissRoute: OGRoute.addToBasketSuccess
      )
      .padding(.vertical, UILayoutConstants.Default.padding2x)
      
    }
  }

  @ViewBuilder var buttons: some View {
    if #available(iOS 16.0, *) {
      let layout: AnyLayout = orientation.isLandscape ? AnyLayout(HStackLayout()) : AnyLayout(VStackLayout())
      layout {
        continueShoppingButton
        gotToBasketButton
      }
    } else {
      VStack {
        continueShoppingButton
        gotToBasketButton
      }
    }
  }

  @ViewBuilder var continueShoppingButton: some View {
    if viewStore.hasShowBasketButton {
      C2AButton(
        title: ogL10n.ProductDetail.BasketSuccessView.ContinueShopping,
        accessibilityIdentifier: ogL10n.ProductDetail.BasketSuccessView.ContinueShopping
      ) {
        Task {
          await viewStore.dispatch(.dismiss)
        }
      }
      .register(buttonStyleResolver.secondaryButtonStyle)
    }
  }

  @ViewBuilder var gotToBasketButton: some View {
    if viewStore.hasContinueShoppingButton {
      C2AButton(
        title: ogL10n.ProductDetail.BasketSuccessView.GoToBasket,
        accessibilityIdentifier: ogL10n.ProductDetail.BasketSuccessView.GoToBasket
      ) {
        Task {
          await viewStore.dispatch(.goToBasket)
        }
      }
      .register(buttonStyleResolver.primaryButtonStyle)
    }
  }

  var header: some View {
    ZStack {
      HStack {
        Spacer()
        ProductDetailCloseButton(accessibilityLabel: ogL10n.General.Close, dropShadow: false) {
          Task {
            await viewStore.dispatch(.dismiss)
          }
        }
      }
      .padding()
      HStack {
        OGImages.icon24x24Success.image
          .accessibilityHidden(true)
        Text(ogL10n.ProductDetail.BasketSuccessView.Title)
          .font(for: .titleM)
          .foregroundColor(OGColors.accentSuccess.color)
          .accessibilityAddTraits(.isHeader)
          .dynamicTypeSize(...DynamicTypeSize.xLarge)
      }
      .padding()
    }
    .frame(maxWidth: .infinity, maxHeight: UILayoutConstants.AddToBasketSuccessView.maxHeight)
    .background(OGColors.backgroundBarNavigationBar.color)
  }
}

// MARK: - UILayoutConstants.AddToBasketSuccessView

extension UILayoutConstants {
  enum AddToBasketSuccessView {
    static let verticalEdgePadding: CGFloat = 14.0
    static let maxHeight: CGFloat = 56
    static let imageHeight: CGFloat = (imageWidth * aspectRatio.height) / aspectRatio.width
    static let imageWidth: CGFloat = 86
    static let aspectRatio: CGSize = .init(width: 5, height: 7)
  }
}
