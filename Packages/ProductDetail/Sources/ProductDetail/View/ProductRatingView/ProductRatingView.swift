import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog
struct ProductRatingView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  init(
    productRating: ProductRating
  ) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(productRating: productRating))
  }

  var body: some SwiftUI.View {
    RatingView(
      rating: .init(
        averageRating: viewStore.averageRating,
        numberOfRatings: viewStore.numberOfRatings
      ),
      size: .large,
      reviewsText: viewStore.numberOfRatings == 1 ? ogL10n.ProductDetail.Review.Singular.Title : ogL10n.ProductDetail.Review.Plural.Title
    ) {
      Task {
        await viewStore.dispatch(.onRatingTap)
      }
    }
    .shimmering(active: viewStore.isLoading)
  }
}
