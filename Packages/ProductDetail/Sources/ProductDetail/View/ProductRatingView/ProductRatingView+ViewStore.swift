import Combine
import Foundation
import OGAppKitSDK
import OGCore
import OGRouter
import OGViewStore

extension ProductRatingView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(
    productRating: ProductRating
  ) -> Store {
    Store(
      initialState: ViewState(
        averageRating: productRating.content.rating.averageRating,
        numberOfRatings: Int(productRating.content.rating.count),
        allReviewsUrl: productRating.content.allReviewsUrl,
        isLoading: productRating.isLoading
      ),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(),
      connector: ViewState.Connector(productRating: productRating)
    )
  }

  struct ViewState: OGViewState {
    private(set) var averageRating: Float = 0.0
    private(set) var numberOfRatings: Int = 0
    private(set) var allReviewsUrl: String = ""
    private(set) var isLoading: Bool = true

    init(
      averageRating: Float = 0.0,
      numberOfRatings: Int = 0,
      allReviewsUrl: String = "",
      isLoading: Bool = true
    ) {
      self.averageRating = averageRating
      self.numberOfRatings = numberOfRatings
      self.allReviewsUrl = allReviewsUrl
      self.isLoading = isLoading
    }

    mutating func update(productRating: ProductRating) {
      isLoading = productRating.isLoading
      numberOfRatings = Int(productRating.content.rating.count)
      averageRating = productRating.content.rating.averageRating
      allReviewsUrl = productRating.content.allReviewsUrl
    }

    static var initial = ViewState()
  }

  enum Event: OGViewEvent, Equatable {
    case onRatingTap
    /// Private
    case _setRating(ProductRating)
  }
}

extension ProductRatingView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductRatingView.ViewState,
      with event: ProductRatingView.Event
    ) {
      switch event {
      case let ._setRating(productRating):
        state.update(productRating: productRating)
      case .onRatingTap:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing

    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher()
    ) {
      self.router = router
    }

    func callAsFunction(
      event: ProductRatingView.Event,
      for state: ProductRatingView.ViewState
    ) async -> ProductRatingView.Event? {
      switch event {
      case .onRatingTap:
        router.send(OGRoute(state.allReviewsUrl))
        return nil
      case ._setRating:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    let productRating: ProductRating

    init(
      productRating: ProductRating
    ) {
      self.productRating = productRating
    }

    func configure(
      dispatch: @escaping (ProductRatingView.Event) async -> Void
    ) async {
      await dispatch(._setRating(productRating))
    }
  }
}
