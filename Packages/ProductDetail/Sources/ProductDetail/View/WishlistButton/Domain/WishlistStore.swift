import AppCore
import Combine
import Foundation
import OGAppKitSDK
import OGBadge
import OGCore
import OGDIService
import <PERSON>GDomainStore
import OGNavigationCore
import OGRouter

typealias WishlistStore = OGDomainStore<WishlistState, WishlistAction>

extension OGDomainStoreFactory {
  static func make() -> WishlistStore {
    WishlistStore(
      reducer: WishlistState.Reducer.reduce,
      middlewares: WishlistState.Middleware()
    )
  }
}

// MARK: - WishlistState

struct WishlistState: OGDomainState {
  private(set) var isAwaitingUpdate: Bool
  private(set) var wishlist: [String] = []
  init(isAwaitingUpdate: Bool = false) {
    self.isAwaitingUpdate = isAwaitingUpdate
  }

  static let initial: Self = .init()

  mutating func update(isAwaitingUpdate: Bool, wishlist: [String]? = nil) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.wishlist = wishlist ?? self.wishlist
  }
}

// MARK: - WishlistAction

enum WishlistAction: OGDomainAction {
  case toggleWishlist(String)
  case addToWishlist(String)
  case removeFromWishlist(String)

  /// private
  case _receivedWishlist([String])
}

extension WishlistState {
  enum Reducer {
    static func reduce(
      _ state: inout WishlistState,
      with action: WishlistAction
    ) {
      switch action {
      case let ._receivedWishlist(wishlist):
        state.update(isAwaitingUpdate: false, wishlist: wishlist)
      case .addToWishlist, .removeFromWishlist, .toggleWishlist:
        state.update(isAwaitingUpdate: true)
      }
    }
  }

  // MARK: - WishlistDomainMiddleware

  struct Middleware: OGDomainMiddleware {
    private let service: ProductDetailServing
    private let logger: any OGLoggingDistributable
    private let badgeStore: BadgeStore

    init(
      service: ProductDetailServing = ProductDetailContainer.shared.service(),
      logger: any OGLoggingDistributable = OGCoreContainer.shared.logger(),
      badgeStore: BadgeStore = OGDomainStoreFactory.make()
    ) {
      self.service = service
      self.logger = logger
      self.badgeStore = badgeStore
    }

    func callAsFunction(
      action: WishlistAction,
      for state: WishlistState
    ) async throws
      -> WishlistAction? {
      switch action {
      case let .toggleWishlist(id):
        if state.wishlist.contains(where: { $0 == id }) {
          return .removeFromWishlist(id)
        } else {
          return .addToWishlist(id)
        }
      case let .addToWishlist(id):
        let items = try await service.addProductToWishlist(id: id)
        badgeStore.dispatchDetached(BadgeStoreAction.setWishlistCount(items.count))
        return ._receivedWishlist(items)
      case let .removeFromWishlist(id):
        let items = try await service.removeProductFromWishlist(id: id)
        badgeStore.dispatchDetached(BadgeStoreAction.setWishlistCount(items.count))
        return ._receivedWishlist(items)
      case ._receivedWishlist:
        return nil
      }
    }
  }
}
