import Combine
import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

struct ReviewsScrollView: SwiftUI.View {
  let isLoading: Bool
  let showMoreAction: (Int) -> Void

  @StateObject private var viewStore: Self.Store
  @State private var scrollDirectionAction = PassthroughSubject<ScrollAction, Never>()
  @Environment(\.screenSize) private var screenSize
  init(reviews: [Review], isLoading: Bool, showMoreAction: @escaping (Int) -> Void) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(reviews: reviews))
    self.isLoading = isLoading
    self.showMoreAction = showMoreAction
  }

  @State private var reviewCardWidth: CGFloat = 0
  private func visibilityChanged(_ id: Int, isVisible: ItemVisibility) {
    Task {
      await viewStore.dispatch(.index(id, visible: isVisible))
    }
  }

  var body: some SwiftUI.View {
    scrollView
      .onRotate { orientation in
        if UIDevice.current.userInterfaceIdiom == .pad {
          reviewCardWidth = orientation.isLandscape ? UILayoutConstants.ProductDetailView.iPadLandscapeMaxWidth * UILayoutConstants.ProductReviewsView.reviewCardLandscapePercentage : min(screenSize.width, screenSize.height) * UILayoutConstants.ProductReviewsView.reviewCardIPadPortraitPercentage
        } else {
          reviewCardWidth = orientation.isLandscape ? max(screenSize.width, screenSize.height) * UILayoutConstants.ProductReviewsView.reviewCardLandscapePercentage : min(screenSize.width, screenSize.height) * UILayoutConstants.ProductReviewsView.reviewCardPortraitPercentage
        }
      }
  }

  var scrollView: some SwiftUI.View {
    ScrollViewReader { reader in
      ScrollView(.horizontal, showsIndicators: false) {
        scrollviewContent
          .onChange(of: viewStore.index) { newValue in
            withAnimation {
              reader.scrollTo(newValue, anchor: .center)
            }
          }
      }
      .simultaneousGesture(DragGesture().onEnded { _ in
        Task {
          await viewStore.dispatch(.trackInteraction)
        }
      })
      .scrollIsDisabled(viewStore.scrollIsDisabled)
    }
  }

  private var scrollviewContent: some SwiftUI.View {
    HStack(spacing: UILayoutConstants.ProductReviewsView.reviewSpacing) {
      ForEach(Array(viewStore.reviews.enumerated()), id: \.offset) { index, review in
        var accessibilityLabel: String {
          var rating = ogL10n.ProductDetail.Review.Rating.Accessibility(rating: String(review.rating), date: review.formattedDate)
          if let reviewerName = review.reviewerName {
            rating += " " + ogL10n.ProductDetail.Review.ReviewerName.Accessibility(reviewerName: reviewerName)
          }
          rating += " \(review.title ?? "") \(review.text)"
          return rating
        }
        ReviewCardView(review: review, isLoading: isLoading, showMoreAction: {
          showMoreAction(index)
        })
        .frame(width: reviewCardWidth)
        .id(index)
        .accessibilityElement(children: .ignore)

        .accessibilityLabel(accessibilityLabel)
      }
    }
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
  }

  @ViewBuilder private var nextButton: some SwiftUI.View {
    Button(action: {
      Task {
        await viewStore.dispatch(.moveToNext)
      }
    }, label: {
      OGImages.icon24x24ChevronRightPrimary.image
        .circleBackground()
    })
    .padding(UILayoutConstants.ProductReviewsView.floatingButtonPadding)
    .accessibilityHidden(true)
  }

  @ViewBuilder private var previousButton: some SwiftUI.View {
    Button(action: {
      Task {
        await viewStore.dispatch(.moveToPrevious)
      }
    }, label: {
      OGImages.icon24x24ChevronLeftPrimary.image
        .circleBackground()
    })
    .padding(UILayoutConstants.ProductReviewsView.floatingButtonPadding)
    .accessibilityHidden(true)
  }
}
