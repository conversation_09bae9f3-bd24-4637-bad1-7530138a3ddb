import Combine
import Foundation
import OGAppKitSDK
import OGCore
import OGFeatureAdapter
import OGRouter
import OGViewStore

public typealias ProductReviews = ProductReviews_
public typealias ProductReviewsContentReviews = ProductReviews_ContentReviews

extension ProductReviewsView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(
    productReviews: ProductReviews
  ) -> Store {
    Store(
      initialState: ViewState(),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(),
      connector: ViewState.Connector(productReviews: productReviews)
    )
  }

  struct ViewState: OGViewState {
    private(set) var averageRating: Float = 0.0
    private(set) var totalReviews: Int = 0
    private(set) var reviews: [Review] = []
    private(set) var allReviewsUrl: String = ""
    private(set) var isLoading: Bool = false
    private(set) var writeReviewUrl: String?

    init(
      averageRating: Float = 0.0,
      totalReviews: Int = 0,
      reviews: [Review] = [],
      allReviewsUrl: String = "",
      productID: String? = ""
    ) {
      self.averageRating = averageRating
      self.totalReviews = totalReviews
      self.reviews = reviews
      self.allReviewsUrl = allReviewsUrl
    }

    mutating func update(with reviews: ProductReviews) {
      isLoading = reviews.isLoading
      if let content = reviews.content as? ProductReviewsContentReviews {
        averageRating = content.rating.averageRating
        totalReviews = Int(content.totalCount)
        self.reviews = content.reviews
        allReviewsUrl = content.allReviewsUrl
        writeReviewUrl = nil
      } else if let content = reviews.content as? ProductReviews_ContentEmpty {
        writeReviewUrl = content.writeReviewUrl
      }
    }

    static var initial = ViewState()
  }

  enum Event: OGViewEvent, Equatable {
    case onRatingMoreTap(Int)
    case onRatingTap
    case onWriteReviewTap
    case _setReviews(with: ProductReviews_)
  }
}

extension ProductReviewsView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductReviewsView.ViewState,
      with event: ProductReviewsView.Event
    ) {
      switch event {
      case let ._setReviews(reviews):
        state.update(with: reviews)
      case .onRatingMoreTap, .onRatingTap, .onWriteReviewTap:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing
    private let baseUrl: OGBaseUrlFeatureAdaptable

    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      baseUrl: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl()
    ) {
      self.router = router
      self.baseUrl = baseUrl
    }

    func callAsFunction(
      event: ProductReviewsView.Event,
      for state: ProductReviewsView.ViewState
    ) async -> ProductReviewsView.Event? {
      switch event {
      case .onRatingTap:
        router.send(OGRoute(state.allReviewsUrl))
        return nil
      case let .onRatingMoreTap(index):
        router.send(OGRoute(state.allReviewsUrl + "\(index)"))
        return nil
      case .onWriteReviewTap:
        guard let writeReviewUrl = state.writeReviewUrl else { return nil }
        let url = baseUrl.urlRelativeToWebUrl(forUrlPath: writeReviewUrl)
        router.send(OGRoute(url: url))
        return nil
      case ._setReviews:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    let productReviews: ProductReviews

    init(
      productReviews: ProductReviews
    ) {
      self.productReviews = productReviews
    }

    func configure(
      dispatch: @escaping (ProductReviewsView.Event) async -> Void
    ) async {
      await dispatch(._setReviews(with: productReviews))
    }
  }
}

// MARK: - Review + Identifiable

extension Review {
  public var formattedDate: String {
    if let dateTime {
      let dateFormatter = DateFormatter()
      dateFormatter.dateStyle = .medium
      return dateFormatter.string(from: Date(timeIntervalSince1970: TimeInterval(dateTime.epochSeconds)))
    } else {
      return ""
    }
  }
}
