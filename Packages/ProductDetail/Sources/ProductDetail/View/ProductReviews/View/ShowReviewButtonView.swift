import OGDIService
import Swift<PERSON>
import UICatalog

// MARK: - ShowReviewButtonView

struct ShowReviewButtonView: View {
  let text: String
  let showReview: () -> Void
  @OGInjected(\ProductDetailContainer.buttonStyleResolver) private var buttonStyleResolver
  var body: some View {
    C2AButton(
      title: text,
      accessibilityIdentifier: text
    ) {
      showReview()
    }
    .register(buttonStyleResolver.secondaryButtonStyle)
  }
}
