import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ProductReviewsView

struct ProductReviewsView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  @State private var rotation: UIInterfaceOrientation = .portrait
  init(
    productReviews: ProductReviews
  ) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(productReviews: productReviews))
  }

  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ProductReviewsView.verticalSpacing) {
      if viewStore.reviews.isEmpty {
        emptyStateView
          .padding(.bottom, UILayoutConstants.Default.padding2x)
      } else {
        reviewContentView
          .padding(.bottom, UILayoutConstants.Default.padding2x)
      }
    }
  }

  private var emptyStateView: some SwiftUI.View {
    VStack(alignment: .leading, spacing: .zero) {
      VStack(alignment: .leading, spacing: .zero) {
        Text(ogL10n.ProductDetail.Review.Title)
          .componentHeadline
          .accessibilityAddTraits(.isHeader)
          .shimmering(active: viewStore.isLoading)
          .padding(.bottom, UILayoutConstants.ProductReviewsView.emptyVerticalSpacing)
        Text(ogL10n.ProductDetail.Review.EmptyTitle)
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .shimmering(active: viewStore.isLoading)
          .padding(.bottom, UILayoutConstants.ProductReviewsView.verticalSpacing)
      }
      .accessibilityElement(children: .combine)
      ShowReviewButtonView(text: ogL10n.ProductDetail.Review.NewReview.Button.Title) {
        Task {
          await viewStore.dispatch(.onWriteReviewTap)
        }
      }
      .shimmering(active: viewStore.isLoading)
    }
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
  }

  private var headerSectionView: some SwiftUI.View {
    HeaderSectionView(
      averageRating: viewStore.averageRating,
      totalReviews: viewStore.totalReviews,
      isLoading: viewStore.isLoading,
      onRatingTap: {
        Task {
          await viewStore.dispatch(.onRatingTap)
        }
      }
    )
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
  }

  private var showReviewButtonView: some SwiftUI.View {
    ShowReviewButtonView(
      text: viewStore.totalReviews == 1 ? ogL10n.ProductDetail.Review.Button.Singular.Title : ogL10n.ProductDetail.Review.Button.Plural.Title(count: "\(viewStore.totalReviews)")
    ) {
      Task {
        await viewStore.dispatch(.onRatingTap)
      }
    }
    .shimmering(active: viewStore.isLoading)
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
  }

  private var reviewContentView: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ProductReviewsView.verticalSpacing) {
      headerSectionView
      ReviewsScrollView(reviews: viewStore.reviews, isLoading: viewStore.isLoading) { index in
        Task {
          await viewStore.dispatch(.onRatingMoreTap(index))
        }
      }
      showReviewButtonView
    }
  }
}

// MARK: - UILayoutConstants.ProductReviewsView

extension UILayoutConstants {
  enum ProductReviewsView {
    static let verticalSpacing: CGFloat = 16
    static let emptyVerticalSpacing: CGFloat = 4
    static let ratingSpacing: CGFloat = 5
    static let reviewSpacing: CGFloat = 16
    static var reviewCardPortraitPercentage: CGFloat = 0.83
    static var reviewCardIPadPortraitPercentage: CGFloat = 0.37
    static var reviewCardLandscapePercentage: CGFloat = 0.37
    static let ratingIconSpacing: CGFloat = 4
    static let floatingButtonPadding: CGFloat = 19
  }
}
