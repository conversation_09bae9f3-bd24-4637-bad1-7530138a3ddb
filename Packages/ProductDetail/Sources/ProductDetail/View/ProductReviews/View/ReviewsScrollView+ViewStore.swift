import Combine
import Foundation
import <PERSON>GAppKitSDK
import OGCore
import O<PERSON>outer
import OGTracker
import OGViewStore

extension ReviewsScrollView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(reviews: [Review]) -> Store {
    Store(
      initialState: ViewState(),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(),
      connector: ViewState.Connector(reviews: reviews)
    )
  }

  struct ViewState: OGViewState {
    private(set) var reviews: [Review] = []
    private(set) var index: Int = 0
    private(set) var nextButtonIsVisible: Bool = false
    private(set) var previousButtonIsVisible: Bool = false
    private var visibleProducts: [Int: ItemVisibility] = [:]
    private(set) var didTrackInteraction: Bool = false

    private var itemCount: Int {
      reviews.count - 1
    }

    var scrollIsDisabled: Bool {
      reviews.count == 1
    }

    private var maxFully: Int? {
      visibleProducts.filter { $0.value == .fully }.keys.max()
    }

    private var minFully: Int? {
      visibleProducts.filter { $0.value == .fully }.keys.min()
    }

    private var maxPartially: Int? {
      visibleProducts.filter { $0.value == .partially }.keys.max()
    }

    private var minPartially: Int? {
      visibleProducts.filter { $0.value == .partially }.keys.min()
    }

    mutating func moveToNext() {
      var index: Int {
        if let maxFully {
          maxFully + 1
        } else {
          maxPartially ?? itemCount
        }
      }
      if index < itemCount {
        self.index = index
      } else {
        self.index = itemCount
        nextButtonIsVisible = false
      }
    }

    mutating func moveToPrevious() {
      var index: Int {
        if let minFully {
          minFully - 1
        } else {
          minPartially ?? 0
        }
      }

      if index > 0 {
        self.index = index
      } else {
        self.index = 0
        previousButtonIsVisible = false
      }
    }

    mutating func update(index: Int, visible: ItemVisibility) {
      visibleProducts[index] = visible
      if visible == .fully || visible == .partially {
        self.index = -1
      }

      if let minFully {
        previousButtonIsVisible = minFully != 0
      } else {
        previousButtonIsVisible = true
      }

      if let maxFully {
        nextButtonIsVisible = maxFully != itemCount
      } else {
        nextButtonIsVisible = true
      }
    }

    mutating func update(reviews: [Review]) {
      self.reviews = reviews
      visibleProducts = reviews.enumerated().reduce(into: [Int: ItemVisibility]()) {
        $0[$1.offset] = .not
      }
    }

    mutating func update(didTrackInteraction: Bool) {
      self.didTrackInteraction = didTrackInteraction
    }

    static var initial = ViewState()
  }

  enum Event: OGViewEvent, Equatable {
    case moveToNext
    case moveToPrevious
    case index(Int, visible: ItemVisibility)
    case trackInteraction

    /// private
    case _setReviews([Review])
    case _didTrackInteraction
  }
}

extension ReviewsScrollView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ReviewsScrollView.ViewState,
      with event: ReviewsScrollView.Event
    ) {
      switch event {
      case let ._setReviews(reviews):
        state.update(reviews: reviews)
      case .moveToNext:
        state.moveToNext()
      case .moveToPrevious:
        state.moveToPrevious()
      case let .index(index, visible: visible):
        state.update(index: index, visible: visible)
      case ._didTrackInteraction:
        state.update(didTrackInteraction: true)
      case .trackInteraction:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let tracker: OGTrackerProtocol
    init(tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()) {
      self.tracker = tracker
    }

    func callAsFunction(
      event: ReviewsScrollView.Event,
      for state: ReviewsScrollView.ViewState
    ) async -> ReviewsScrollView.Event? {
      switch event {
      case .trackInteraction:
        guard !state.didTrackInteraction else { return nil }
        tracker.multiplatformTrack(event: Interaction.ProductDetailViewReviews())
        return ._didTrackInteraction
      case .moveToNext, .moveToPrevious:
        return ._didTrackInteraction
      case ._didTrackInteraction, ._setReviews, .index:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    let reviews: [Review]

    init(reviews: [Review]) {
      self.reviews = reviews
    }

    func configure(
      dispatch: @escaping (ReviewsScrollView.Event) async -> Void
    ) async {
      await dispatch(._setReviews(reviews))
    }
  }
}
