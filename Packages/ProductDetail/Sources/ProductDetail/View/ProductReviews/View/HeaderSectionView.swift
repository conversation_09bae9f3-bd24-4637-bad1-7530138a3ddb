import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

struct HeaderSectionView: SwiftUI.View {
  let averageRating: Float
  let totalReviews: Int
  let isLoading: Bool
  let onRatingTap: () -> Void

  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ProductReviewsView.verticalSpacing) {
      Text(ogL10n.ProductDetail.Review.Title)
        .componentHeadline
        .shimmering(active: isLoading)
        .accessibilityAddTraits(.isHeader)

      AdaptiveHStack(alignment: .center, spacing: UILayoutConstants.ProductReviewsView.ratingSpacing) {
        Text(String(format: "%.1f", locale: Locale.current, averageRating))
          .font(for: .headlineXXL)
          .foregroundColor(OGColors.textBlack.color)
          .shimmering(active: isLoading)
          .accessibilityHidden(true)
        RatingView(
          rating: .init(
            averageRating: averageRating,
            numberOfRatings: totalReviews
          ),
          size: .large,
          reviewsText: totalReviews == 1 ? ogL10n.ProductDetail.Review.Singular.Title : ogL10n.ProductDetail.Review.Plural.Title
        ) {
          onRatingTap()
        }
        .shimmering(active: isLoading)
      }
    }
  }
}
