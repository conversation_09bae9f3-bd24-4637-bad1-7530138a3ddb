import OGAppKitSDK
import OGDIService
import OGL10n
import Swift<PERSON>
import UICatalog

// MARK: - ReviewCardView

struct ReviewCardView: SwiftUI.View {
  let review: Review
  let isLoading: Bool
  let showMoreAction: () -> Void
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius

  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ReviewCardView.cardSpacing) {
      ReviewHeaderView(review: review, isLoading: isLoading)

      if let title = review.title {
        Text(title)
          .font(for: .titleM)
          .foregroundColor(OGColors.textOnLight.color)
          .lineLimit(1)
          .shimmering(active: isLoading)
      }

      TruncableTextView(
        text: review.text,
        showMoreTitle: ogL10n.ProductDetail.Review.ShowMoreReview.Button.Title,
        trailingTitle: review.reviewerName,
        contentSpacing: UILayoutConstants.ReviewCardView.contentSpacing,
        maxLines: UILayoutConstants.ReviewCardView.maxLines,
        showMoreAction: showMoreAction
      )
      .frame(minHeight: UILayoutConstants.ReviewCardView.textContentHeight)
      .shimmering(active: isLoading)
    }
    .padding(UILayoutConstants.ReviewCardView.cardInnerPadding)
    .background(
      RoundedRectangle(cornerRadius: cornerRadius.medium)
        .fill(OGColors.backgroundBackground0.color)
    )
    .overlay(
      RoundedRectangle(cornerRadius: cornerRadius.medium)
        .stroke(OGColors.backgroundBackground20.color, lineWidth: UILayoutConstants.ReviewCardView.borderWidth)
    )
    .padding(UILayoutConstants.ReviewCardView.borderPadding)
    .frame(minHeight: UILayoutConstants.ReviewCardView.cardMinHeight)
    .onTapGesture {
      showMoreAction()
    }
  }
}

// MARK: - UILayoutConstants.ReviewCardView

extension UILayoutConstants {
  enum ReviewCardView {
    static let cardMinHeight: CGFloat = 190.0
    static let borderWidth: CGFloat = 1
    static let cardInnerPadding: CGFloat = 16
    static let textContentHeight: CGFloat = 100
    static let contentSpacing: CGFloat = 8
    static let maxLines: Int = 3
    static let cardSpacing: CGFloat = 8
    static let borderPadding: CGFloat = 2
  }
}
