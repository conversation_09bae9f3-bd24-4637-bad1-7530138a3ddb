import OGAppKitSDK
import SwiftUI
import UICatalog

struct ReviewHeaderView: SwiftUI.View {
  let review: Review
  let isLoading: Bool
  var body: some SwiftUI.View {
    HStack(alignment: .top) {
      RatingView(simpleRating: review.rating, size: .medium)
        .shimmering(active: isLoading)
      Spacer()
      HStack(spacing: 8) {
        Text(review.formattedDate)
          .font(for: .copyS)
          .foregroundColor(OGColors.textBlack60.color)
      }
      .shimmering(active: isLoading)
    }
  }
}
