import SwiftUI
// Layout Representation:
// | Subview 0
// | Subview 1
// +-------------------------------+
// | primaryIndex 2      |  Subview 3
// |                     |  Subview 4
// |                     |  Subview 5
// +-------------------------------+
// | Subview 6
// | Subview 7
// +-------------------------------+

@available(iOS 16.0, *)
struct CustomIndexFlowLayout: Layout {
  let primaryViewAspectRatio: CGSize
  let primaryViewWidthRatio: CGFloat
  let maxFlowedItems: Int
  let primaryViewIndex: Int
  let primaryBottomSpacing: CGFloat
  let respectPrimaryHeight: Bool

  init(
    primaryIndex: Int = 0,
    widthRatio: CGFloat = 0.5,
    aspectRatio: CGSize = CGSize(width: 5, height: 7),
    maxFlowedItems: Int = 5,
    primaryBottomSpacing: CGFloat = 16,
    respectPrimaryHeight: Bool = false
  ) {
    self.primaryViewAspectRatio = aspectRatio
    self.primaryViewWidthRatio = widthRatio
    self.maxFlowedItems = maxFlowedItems
    self.primaryViewIndex = primaryIndex
    self.primaryBottomSpacing = primaryBottomSpacing
    self.respectPrimaryHeight = respectPrimaryHeight
  }

  func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
    guard !subviews.isEmpty else { return .zero }
    let containerWidth = proposal.width ?? proposal.replacingUnspecifiedDimensions().width
    let primaryViewWidth = containerWidth * primaryViewWidthRatio
    let primaryViewHeight = (primaryViewWidth * primaryViewAspectRatio.height) / primaryViewAspectRatio.width
    let safeIndex = min(max(0, primaryViewIndex), subviews.count - 1)
    let beforePrimaryCount = safeIndex
    var totalHeight: CGFloat = 0

    // Calculate height of items before primary view
    for i in 0 ..< beforePrimaryCount {
      let size = subviews[i].sizeThatFits(.unspecified)
      totalHeight += size.height
    }

    // Add primary view height
    totalHeight += primaryViewHeight

    var yOffset: CGFloat = 0
    var flowedCount = 0
    var rightSideHeight: CGFloat = 0

    // Calculate flowed items and their total height
    for i in (safeIndex + 1) ..< subviews.count {
      if flowedCount >= maxFlowedItems { break }
      let subviewSize = subviews[i].sizeThatFits(.unspecified)

      let shouldFlow: Bool
      if respectPrimaryHeight {
        shouldFlow = yOffset + subviewSize.height <= primaryViewHeight
      } else {
        shouldFlow = true
      }

      if shouldFlow {
        yOffset += subviewSize.height
        rightSideHeight += subviewSize.height
        flowedCount += 1
      } else {
        break
      }
    }

    // If right side is taller than primary view, adjust total height
    if rightSideHeight > primaryViewHeight {
      totalHeight += (rightSideHeight - primaryViewHeight)
    }

    // Add remaining subviews height
    for i in (safeIndex + 1 + flowedCount) ..< subviews.count {
      let size = subviews[i].sizeThatFits(.unspecified)
      totalHeight += size.height
    }

    return CGSize(width: containerWidth, height: totalHeight)
  }

  func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
    guard !subviews.isEmpty else { return }
    let containerSize = bounds.size
    let primaryViewWidth = containerSize.width * primaryViewWidthRatio
    let primaryViewHeight = (primaryViewWidth * primaryViewAspectRatio.height) / primaryViewAspectRatio.width
    let primarySubviewSize = CGSize(width: primaryViewWidth, height: primaryViewHeight)
    let safeIndex = min(max(0, primaryViewIndex), subviews.count - 1)

    // Place subviews that come before the primary view
    var currentY = bounds.minY
    for i in 0 ..< safeIndex {
      let subview = subviews[i]
      let subviewSize = subview.sizeThatFits(proposal)
      subview.place(
        at: CGPoint(x: bounds.minX, y: currentY),
        anchor: .topLeading,
        proposal: proposal
      )
      currentY += subviewSize.height
    }

    // Place the primary view
    let primaryViewY = currentY
    subviews[safeIndex].place(
      at: CGPoint(x: bounds.minX, y: primaryViewY),
      anchor: .topLeading,
      proposal: ProposedViewSize(primarySubviewSize)
    )

    let xOffset = primaryViewWidth + bounds.minX
    var yOffset = primaryViewY
    var flowedCount = 0
    var nextIndex = safeIndex + 1
    var rightSideHeight: CGFloat = 0

    // Place items that flow to the right of the primary view
    for i in (safeIndex + 1) ..< subviews.count {
      if flowedCount >= maxFlowedItems { break }

      let subview = subviews[i]
      let subviewSize = subview.sizeThatFits(proposal)

      let shouldFlow: Bool
      if respectPrimaryHeight {
        shouldFlow = yOffset + subviewSize.height <= primaryViewY + primaryViewHeight
      } else {
        shouldFlow = true
      }

      if shouldFlow {
        subview.place(
          at: CGPoint(x: xOffset, y: yOffset),
          anchor: .topLeading,
          proposal: ProposedViewSize(CGSize(width: containerSize.width - primaryViewWidth, height: subviewSize.height))
        )
        yOffset += subviewSize.height
        rightSideHeight += subviewSize.height
        flowedCount += 1
        nextIndex = i + 1
      } else {
        break
      }
    }

    // Update currentY to point to the bottom of the taller section (primary view or right side)
    let primaryBottom = primaryViewY + primaryViewHeight
    let rightSideBottom = primaryViewY + rightSideHeight
    currentY = max(primaryBottom, rightSideBottom) + primaryBottomSpacing

    // Place remaining subviews below
    for i in nextIndex ..< subviews.count {
      let subview = subviews[i]
      let subviewSize = subview.sizeThatFits(proposal)
      subview.place(
        at: CGPoint(x: bounds.minX, y: currentY),
        anchor: .topLeading,
        proposal: proposal
      )
      currentY += subviewSize.height
    }
  }
}
