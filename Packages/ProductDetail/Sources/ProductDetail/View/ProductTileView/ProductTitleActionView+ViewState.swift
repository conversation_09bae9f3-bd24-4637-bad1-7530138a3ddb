import OGAppKitSDK
import OGTracker
import OGViewStore

extension ProductTitleActionView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore()
    -> Store {
    Store(
      initialState: .initial,
      reducer: ProductTitleActionView.ViewState.Reducer.reduce,
      middleware: ProductTitleActionView.ViewState.Middleware()
    )
  }

  public struct ViewState: OGViewState {
    public static var initial: Self {
      .init()
    }
  }

  enum Event: OGViewEvent {
    case track
  }
}

extension ProductTitleActionView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductTitleActionView.ViewState,
      with event: ProductTitleActionView.Event
    ) {}
  }

  struct Middleware: OGViewStoreMiddleware {
    let tracker: OGTrackerProtocol
    init(tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()) {
      self.tracker = tracker
    }

    func callAsFunction(
      event: ProductTitleActionView.Event,
      for state: ProductTitleActionView.ViewState
    ) async
      -> ProductTitleActionView.Event? {
      switch event {
      case .track:
        tracker.multiplatformTrack(event: Interaction.ShareProduct())
        return nil
      }
    }
  }
}
