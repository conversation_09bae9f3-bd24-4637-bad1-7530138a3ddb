import OGAppKitSDK
import SwiftUI
import UICatalog

struct ProductTitleView: SwiftUI.View {
  let header: ProductHeader
  var body: some SwiftUI.View {
    VStack {
      if let brandName = header.content.brandName {
        Text(brandName)
          .font(for: .copyS)
          .foregroundColor(OGColors.navigationBarElementTitle.color)
      }
      Text(header.content.title)
        .font(for: .titleM)
        .foregroundColor(OGColors.navigationBarElementTitle.color)
    }
    .dynamicTypeSize(...DynamicTypeSize.xLarge)
    .shimmering(active: header.isLoading)
    .accessibilityAddTraits(.isHeader)
    .accessibilityElement(children: .combine)
  }
}
