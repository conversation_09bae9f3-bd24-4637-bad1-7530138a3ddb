import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ProductTitleActionView

struct ProductTitleActionView: SwiftUI.View {
  let header: ProductHeader
  let showsWishlistIcon: Bool
  @StateObject private var viewStore: Self.Store = Self.makeStore()
  var body: some SwiftUI.View {
    HStack {
      if showsWishlistIcon {
        WishlistButtonView(isWishlisted: header.content.isWishlisted, productId: header.content.productIdForWishlisting)
          .padding(.trailing, UILayoutConstants.Default.padding)
          .padding(.top, 3)
      }
      if let sharingData = header.content.sharingData, #available(iOS 16.0, *) {
        ShareLink(item: sharingData.url) {
          OGImages.icon24x24SharePrimary.image
            .accessibilityLabel(ogL10n.ProductDetail.Share.Button.Accessibility)
        }
        .simultaneousGesture(TapGesture().onEnded {
          Task {
            await viewStore.dispatch(.track)
          }
        })
      }
    }.padding()
  }
}

// MARK: - UILayoutConstants.ProductTitleActionView

extension UILayoutConstants {
  enum ProductTitleActionView {
    static let wishlistButtonTopPadding: CGFloat = 3
  }
}
