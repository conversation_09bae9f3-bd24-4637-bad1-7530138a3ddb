import OGAppKitSDK
import OGDIService
import OGL10n
import <PERSON><PERSON>
import UICatalog

// MARK: - DynamicYieldBannerView

struct DynamicYieldBannerView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  @State private var orientation = UIInterfaceOrientation.unknown
  @State private var showingSheet: Bool = false
  @State private var didCopyCode: Bool = false
  @Namespace private var namespace
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  private var isLandscapeOrPad: Bool {
    orientation.isLandscape || UIDevice.current.userInterfaceIdiom == .pad
  }

  private var isPortraitAndNotPad: Bool {
    orientation.isPortrait && UIDevice.current.userInterfaceIdiom != .pad
  }

  init(dynamicYieldBanner: DynamicYieldBanner) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(dynamicYieldBanner: dynamicYieldBanner))
  }

  var body: some SwiftUI.View {
    VStack(alignment: .leading) {
      content
    }
    .padding(UILayoutConstants.DynamicYieldBannerView.padding)
    .background(
      Color(viewStore.didCopyCode ? OGColors.accentSuccess.name : OGColors.accentSale.name)
    )
    .onAppear {
      Task {
        await viewStore.dispatch(.trackView)
      }
    }
    .onRotate { orientation in
      self.orientation = orientation
    }
    .sheet(isPresented: $showingSheet) {
      sheet
        .presentationDetentToContentHeight
    }
    .accessibilitySortPriority(2)
  }

  @ViewBuilder private var content: some SwiftUI.View {
    HStack(alignment: .firstTextBaseline) {
      Text(viewStore.text)
        .font(for: .titleM)
        .foregroundColor(OGColors.textOnDark.color)
      if isLandscapeOrPad {
        codeView
          .matchedGeometryEffect(id: "codeView", in: namespace)
          .padding(.leading, UILayoutConstants.Default.padding)
      }
      Spacer()
      if viewStore.showsInfo {
        OGImages.icon24x24InfoOnDark.image
          .accessibilityLabel(ogL10n.ProductDetail.DynamicYieldBanner.Info.Button.Accessibility)
          .accessibilityAddTraits(.isButton)
          .onTapGesture {
            withAnimation {
              showingSheet.toggle()
            }
          }
      }
    }
    if isPortraitAndNotPad {
      codeView
        .matchedGeometryEffect(id: "codeView", in: namespace)
    }
  }

  private var codeView: some SwiftUI.View {
    AdaptiveHStack(alignment: .center, spacing: UILayoutConstants.DynamicYieldBannerView.codeSpacing) {
      if didCopyCode {
        didCopyCodeView
      } else {
        copyCodeView
      }
    }
    .animation(.easeInOut(duration: 0.5), value: didCopyCode)
    .accessibilityElement(children: .combine)
    .accessibilityAddTraits(.isButton)
    .padding(UILayoutConstants.DynamicYieldBannerView.codePadding)
    .cornerRadius(cornerRadius.medium)
    .overlay(
      RoundedRectangle(cornerRadius: cornerRadius.medium)
        .stroke(OGColors.backgroundBackground0.color, style: UILayoutConstants.DynamicYieldBannerView.strokeStyle)
    )
    .onTapGesture {
      Task {
        await viewStore.dispatch(.onCopyCode)
      }
    }
    .onChange(of: viewStore.didCopyCode) { isCodeCopied in
      didCopyCode = isCodeCopied
    }
  }

  @ViewBuilder private var copyCodeView: some SwiftUI.View {
    Text(ogL10n.General.Banner.Code.Copy)
      .font(for: .copyMRegular)
      .foregroundColor(OGColors.textOnDark.color)
      .multilineTextAlignment(.center)
    Text(viewStore.promoCode)
      .font(for: .titleS)
      .foregroundColor(OGColors.textOnDark.color)
      .multilineTextAlignment(.center)
    Image(OGImages.icon24x24Copy.name)
      .accessibilityHidden(true)
  }

  @ViewBuilder private var didCopyCodeView: some SwiftUI.View {
    Text(ogL10n.General.Banner.Code.DidCopy)
      .font(for: .copyMRegular)
      .foregroundColor(OGColors.textOnDark.color)
      .multilineTextAlignment(.center)
    Image(OGImages.icon24x24CheckmarkOnDark.name)
      .accessibilityHidden(true)
  }

  @ViewBuilder private var sheet: some SwiftUI.View {
    VStack(alignment: .leading) {
      HStack {
        Text(ogL10n.ProductDetail.DynamicYieldBanner.Sheet.Discount.Title)
          .font(for: OGFonts.titleM)
          .foregroundColor(OGColors.textOnLight.color)
          .accessibilityAddTraits(.isHeader)
          .multilineTextAlignment(.leading)
          .dynamicTypeSize(...DynamicTypeSize.xLarge)
        Spacer()
        ProductDetailCloseButton(accessibilityLabel: ogL10n.General.Close, dropShadow: false, action: {
          withAnimation {
            showingSheet = false
          }
        })
      }
      ScrollView {
        Text(viewStore.infoText)
          .font(for: OGFonts.copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .multilineTextAlignment(.leading)
      }
    }
    .padding()
  }
}

// MARK: - UILayoutConstants.DynamicYieldBannerView

extension UILayoutConstants {
  enum DynamicYieldBannerView {
    static let codeSpacing: CGFloat = 4
    static let codePadding: EdgeInsets = .init(top: 4.0, leading: 8.0, bottom: 4.0, trailing: 8.0)
    static let padding: CGFloat = 12
    static let strokeStyle: StrokeStyle = .init(lineWidth: 1.0, dash: [4.0, 4.0])
  }
}
