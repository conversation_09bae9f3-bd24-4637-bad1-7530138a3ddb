import OGAppKitSDK
import OGDIService
import OGL10n
import Swift<PERSON>
import UICatalog

// MARK: - ProductAvailabilityView

struct ProductAvailabilityView: SwiftUI.View {
  let availability: ProductAvailability
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  var body: some SwiftUI.View {
    HStack(alignment: .center, spacing: .zero) {
      Image(availability.content.availability.imageName)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .padding(.vertical, UILayoutConstants.ProductAvailabilityView.imageVerticalPadding)
        .accessibility(hidden: true)
      VStack(alignment: .leading) {
        Text(availability.content.availability.availabilityMessage)
          .font(for: OGFonts.titleS)
          .foregroundColor(OGColors.textOnLight.color)
        if availability.content.availability.state == .lowStock {
          Text(ogL10n.ProductDetail.Availability.Quantity(count: "\(Int(availability.content.availability.quantity))"))
            .font(for: OGFonts.copyMRegular)
            .multilineTextAlignment(.leading)
            .foregroundColor(Color(availability.content.availability.colorName))
        } else if !availability.content.availability.state.availabilityInfo.isEmpty {
          Text(availability.content.availability.state.availabilityInfo)
            .font(for: OGFonts.copyMRegular)
            .foregroundColor(Color(availability.content.availability.colorName))
        }
      }
      .accessibilityElement(children: .combine)
      .padding(.vertical, UILayoutConstants.ProductAvailabilityView.textBoxVerticalPadding)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
      Spacer()
    }
    .frame(minHeight: UILayoutConstants.ProductAvailabilityView.minHeight)
    .frame(maxWidth: .infinity)
    .background(OGColors.backgroundBackground0.color)
    .cornerRadius(cornerRadius.medium)
    .overlay(
      RoundedRectangle(cornerRadius: cornerRadius.medium)
        .inset(by: UILayoutConstants.ProductAvailabilityView.borderWidth / 2)
        .stroke(OGColors.backgroundBackground20.color, lineWidth: UILayoutConstants.ProductAvailabilityView.borderWidth)
    )
    .shimmering(active: availability.isLoading, cornerRadius: cornerRadius.medium)
  }
}

// MARK: - UILayoutConstants.ProductAvailabilityView

extension UILayoutConstants {
  enum ProductAvailabilityView {
    static let minHeight: CGFloat = 70
    static let spacing: CGFloat = 12
    static let imageVerticalPadding: CGFloat = 23
    static let textBoxVerticalPadding: CGFloat = 12
    static let borderWidth: CGFloat = 1.0
  }
}
