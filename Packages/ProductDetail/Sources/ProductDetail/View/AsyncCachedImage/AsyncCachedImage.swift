import SwiftUI
import UICatalog
import UIKit

struct AsyncCachedImage<ImageView: View, PlaceholderView: View>: View {
  private let url: URL?
  private let targetSize: CGSize?
  private let targetAspectRatio: CGSize?
  @Binding private var refresh: Bool
  @ViewBuilder var content: (Image) -> ImageView
  @ViewBuilder var placeholder: (Loadable) -> PlaceholderView
  @StateObject private var viewStore: Self.Store

  init(
    url: URL?,
    targetSize: CGSize? = nil,
    targetAspectRatio: CGSize? = nil,
    refresh: Binding<Bool>? = nil,
    @ViewBuilder content: @escaping (Image) -> ImageView,
    @ViewBuilder placeholder: @escaping (Loadable) -> PlaceholderView
  ) {
    self.url = url
    self.targetSize = targetSize
    self.targetAspectRatio = targetAspectRatio
    _refresh = refresh ?? .constant(false)
    self.content = content
    self.placeholder = placeholder
    _viewStore = StateObject(wrappedValue: Self.makeStore(targetSize: targetSize, targetAspectRatio: targetAspectRatio))
  }

  var body: some View {
    if let image = viewStore.image {
      content(image)
    } else {
      placeholder(viewStore.loading)
        .onAppear {
          Task {
            await viewStore.dispatch(.loadImage(url))
          }
        }
        .onChange(of: refresh) { newValue in
          guard newValue else { return }
          Task {
            await viewStore.dispatch(.loadImage(url))
          }
        }
    }
  }
}

// MARK: - Convenience Initializers

extension AsyncCachedImage {
  /// Convenience initializer that automatically calculates target size from frame dimensions
  init(
    url: URL?,
    maxWidth: CGFloat,
    maxHeight: CGFloat,
    targetAspectRatio: CGSize? = nil,
    refresh: Binding<Bool>? = nil,
    @ViewBuilder content: @escaping (Image) -> ImageView,
    @ViewBuilder placeholder: @escaping (Loadable) -> PlaceholderView
  ) {
    self.init(
      url: url,
      targetSize: CGSize(width: maxWidth, height: maxHeight),
      targetAspectRatio: targetAspectRatio,
      refresh: refresh,
      content: content,
      placeholder: placeholder
    )
  }

  /// Convenience initializer for square containers
  init(
    url: URL?,
    maxSize: CGFloat,
    targetAspectRatio: CGSize? = nil,
    refresh: Binding<Bool>? = nil,
    @ViewBuilder content: @escaping (Image) -> ImageView,
    @ViewBuilder placeholder: @escaping (Loadable) -> PlaceholderView
  ) {
    self.init(
      url: url,
      targetSize: CGSize(width: maxSize, height: maxSize),
      targetAspectRatio: targetAspectRatio,
      refresh: refresh,
      content: content,
      placeholder: placeholder
    )
  }

  /// Convenience initializer with aspect ratio matching (5:7 ratio commonly used in product images)
  init(
    url: URL?,
    targetSize: CGSize,
    aspectRatio: CGSize = CGSize(width: 5, height: 7),
    refresh: Binding<Bool>? = nil,
    @ViewBuilder content: @escaping (Image) -> ImageView,
    @ViewBuilder placeholder: @escaping (Loadable) -> PlaceholderView
  ) {
    self.init(
      url: url,
      targetSize: targetSize,
      targetAspectRatio: aspectRatio,
      refresh: refresh,
      content: content,
      placeholder: placeholder
    )
  }
}
