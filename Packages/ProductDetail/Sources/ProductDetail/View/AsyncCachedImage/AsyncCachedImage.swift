import SwiftUI
import UICatalog
import UIKit

struct AsyncCachedImage<ImageView: View, PlaceholderView: View>: View {
  private let url: URL?
  private let targetSize: CGSize?
  @Binding private var refresh: Bool
  @ViewBuilder var content: (Image) -> ImageView
  @ViewBuilder var placeholder: (Loadable) -> PlaceholderView
  @StateObject private var viewStore: Self.Store

  init(
    url: URL?,
    targetSize: CGSize? = nil,
    refresh: Binding<Bool>? = nil,
    @ViewBuilder content: @escaping (Image) -> ImageView,
    @ViewBuilder placeholder: @escaping (Loadable) -> PlaceholderView
  ) {
    self.url = url
    self.targetSize = targetSize
    _refresh = refresh ?? .constant(false)
    self.content = content
    self.placeholder = placeholder
    _viewStore = StateObject(wrappedValue: Self.makeStore(targetSize: targetSize))
  }

  var body: some View {
    if let image = viewStore.image {
      content(image)
    } else {
      placeholder(viewStore.loading)
        .onAppear {
          Task {
            await viewStore.dispatch(.loadImage(url))
          }
        }
        .onChange(of: refresh) { newValue in
          guard newValue else { return }
          Task {
            await viewStore.dispatch(.loadImage(url))
          }
        }
    }
  }
}
