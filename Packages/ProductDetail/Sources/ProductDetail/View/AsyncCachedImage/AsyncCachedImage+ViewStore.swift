import Combine
import Foundation
import <PERSON>GViewStore
import SwiftUI
import UICatalog
extension AsyncCachedImage {
  typealias Store = OGViewStore<AsyncCachedImageViewState, Event>
  @MainActor
  static func makeStore(targetSize: CGSize? = nil) -> Store {
    AsyncCachedImage.Store(
      reducer: AsyncCachedImage.Reducer.reduce,
      middleware: AsyncCachedImage.Middleware(targetSize: targetSize)
    )
  }
}

// MARK: - AsyncCachedImageViewState

public struct AsyncCachedImageViewState: OGViewState {
  private(set) var image: Image?
  private(set) var loading: Loadable = .initial
  public init(
    image: Image? = nil,
    loading: Loadable = .initial
  ) {
    self.image = image
    self.loading = loading
  }

  mutating func update(image: Image? = nil, loading: Loadable? = nil) {
    self.image = image ?? self.image
    self.loading = loading ?? self.loading
  }

  public static var initial = AsyncCachedImageViewState()
}

// MARK: - AsyncCachedImage.Event

extension AsyncCachedImage {
  enum Event: OGViewEvent {
    case loadImage(URL?)
    /// private
    case _startLoadImage(URL)
    case _receivedImage(UIImage)
    case _loadingFailed
  }
}

extension AsyncCachedImage {
  enum Reducer {
    static func reduce(
      _ state: inout AsyncCachedImageViewState,
      with event: AsyncCachedImage.Event
    ) {
      switch event {
      case .loadImage:
        break
      case ._startLoadImage:
        state.update(loading: .progress)
      case let ._receivedImage(image):
        state.update(image: Image(uiImage: image), loading: .success)
      case ._loadingFailed:
        state.update(loading: .failure(AsyncCachedImageDownloadError.failed))
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let targetSize: CGSize?
    init(
      targetSize: CGSize? = nil,
    ) {
      self.targetSize = targetSize
    }

    func callAsFunction(
      event: AsyncCachedImage.Event,
      for state: AsyncCachedImageViewState
    ) async
      -> AsyncCachedImage.Event? {
      switch event {
      case let .loadImage(url):
        guard let url else {
          return ._loadingFailed
        }
        return ._startLoadImage(url)
      case let ._startLoadImage(url):
        do {
          let imageDownloader: AsyncCachedImageDownloadable = AsyncCachedImageDownloader()
          let data = try await imageDownloader.downloadImage(url: url)
          guard !data.isEmpty, let originalImage = UIImage(data: data) else {
            return ._loadingFailed
          }

          if let targetSize = targetSize {
            return ._receivedImage(originalImage.scaledToFit(targetSize: targetSize) ?? originalImage)
          } else {
            return ._receivedImage(originalImage)
          }

        } catch {
          return ._loadingFailed
        }
      case ._loadingFailed, ._receivedImage:
        return nil
      }
    }
  }
}

extension UIImage {
    func scaledDown(by factor: CGFloat) -> UIImage? {
        let newSize = CGSize(width: size.width * factor, height: size.height * factor)
        let format = UIGraphicsImageRendererFormat()
        format.scale = self.scale
        format.opaque = true

        let renderer = UIGraphicsImageRenderer(size: newSize, format: format)
          return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }

    /// Scales the image to fit within the target size while maintaining aspect ratio
    /// This helps save memory by not loading images larger than needed
    func scaledToFit(targetSize: CGSize) -> UIImage? {
        let originalSize = self.size

        // If the image is already smaller than or equal to target size, return as-is
        if originalSize.width <= targetSize.width && originalSize.height <= targetSize.height {
            return self
        }

        // Calculate scale factor to fit within target size while maintaining aspect ratio
        let widthScale = targetSize.width / originalSize.width
        let heightScale = targetSize.height / originalSize.height
        let scaleFactor = min(widthScale, heightScale)

        let newSize = CGSize(
            width: originalSize.width * scaleFactor,
            height: originalSize.height * scaleFactor
        )

        let format = UIGraphicsImageRendererFormat()
        format.scale = UIScreen.mainScreen.scale
        format.opaque = true

        let renderer = UIGraphicsImageRenderer(size: newSize, format: format)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }
}
