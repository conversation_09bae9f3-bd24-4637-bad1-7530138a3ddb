import Combine
import Foundation
import <PERSON>GViewStore
import SwiftUI
import UICatalog
extension AsyncCachedImage {
  typealias Store = OGViewStore<AsyncCachedImageViewState, Event>
  @MainActor
  static func makeStore() -> Store {
    AsyncCachedImage.Store(
      reducer: AsyncCachedImage.Reducer.reduce,
      middleware: AsyncCachedImage.Middleware()
    )
  }
}

// MARK: - AsyncCachedImageViewState

public struct AsyncCachedImageViewState: OGViewState {
  private(set) var image: Image?
  private(set) var loading: Loadable = .initial
  public init(
    image: Image? = nil,
    loading: Loadable = .initial
  ) {
    self.image = image
    self.loading = loading
  }

  mutating func update(image: Image? = nil, loading: Loadable? = nil) {
    self.image = image ?? self.image
    self.loading = loading ?? self.loading
  }

  public static var initial = AsyncCachedImageViewState()
}

// MARK: - AsyncCachedImage.Event

extension AsyncCachedImage {
  enum Event: OGViewEvent {
    case loadImage(URL?)
    /// private
    case _startLoadImage(URL)
    case _receivedImage(UIImage)
    case _loadingFailed
  }
}

extension AsyncCachedImage {
  enum Reducer {
    static func reduce(
      _ state: inout AsyncCachedImageViewState,
      with event: AsyncCachedImage.Event
    ) {
      switch event {
      case .loadImage:
        break
      case ._startLoadImage:
        state.update(loading: .progress)
      case let ._receivedImage(image):
        state.update(image: Image(uiImage: image), loading: .success)
      case ._loadingFailed:
        state.update(loading: .failure(AsyncCachedImageDownloadError.failed))
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let imageDownloader: AsyncCachedImageDownloadable

    init(
      imageDownloader: AsyncCachedImageDownloadable = AsyncCachedImageDownloader()
    ) {
      self.imageDownloader = imageDownloader
    }

    func callAsFunction(
      event: AsyncCachedImage.Event,
      for state: AsyncCachedImageViewState
    ) async
      -> AsyncCachedImage.Event? {
      switch event {
      case let .loadImage(url):
        guard let url else {
          return ._loadingFailed
        }
        return ._startLoadImage(url)
      case let ._startLoadImage(url):
        do {
          let data = try await imageDownloader.downloadImage(url: url)
          guard !data.isEmpty, let image = UIImage(data: data)?.scaledDown(by: 0.5) else {
            return ._loadingFailed
          }
          return ._receivedImage(image)
        } catch {
          return ._loadingFailed
        }
      case ._loadingFailed, ._receivedImage:
        return nil
      }
    }
  }
}

extension UIImage {
    func scaledDown(by factor: CGFloat) -> UIImage? {
        let newSize = CGSize(width: size.width * factor, height: size.height * factor)
        let format = UIGraphicsImageRendererFormat()
        format.scale = self.scale
        format.opaque = true
  
        let renderer = UIGraphicsImageRenderer(size: newSize, format: format)
          return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }
}
