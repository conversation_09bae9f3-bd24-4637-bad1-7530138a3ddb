import Combine
import Foundation
import <PERSON>GViewStore
import SwiftUI
import UICatalog
extension AsyncCachedImage {
  typealias Store = OGViewStore<AsyncCachedImageViewState, Event>
  @MainActor
  static func makeStore(targetSize: CGSize? = nil, targetAspectRatio: CGSize? = nil) -> Store {
    AsyncCachedImage.Store(
      reducer: AsyncCachedImage.Reducer.reduce,
      middleware: AsyncCachedImage.Middleware(targetSize: targetSize, targetAspectRatio: targetAspectRatio)
    )
  }
}

// MARK: - AsyncCachedImageViewState

public struct AsyncCachedImageViewState: OGViewState {
  private(set) var image: Image?
  private(set) var loading: Loadable = .initial
  public init(
    image: Image? = nil,
    loading: Loadable = .initial
  ) {
    self.image = image
    self.loading = loading
  }

  mutating func update(image: Image? = nil, loading: Loadable? = nil) {
    self.image = image ?? self.image
    self.loading = loading ?? self.loading
  }

  public static var initial = AsyncCachedImageViewState()
}

// MARK: - AsyncCachedImage.Event

extension AsyncCachedImage {
  enum Event: OGViewEvent {
    case loadImage(URL?)
    /// private
    case _startLoadImage(URL)
    case _receivedImage(UIImage)
    case _loadingFailed
  }
}

extension AsyncCachedImage {
  enum Reducer {
    static func reduce(
      _ state: inout AsyncCachedImageViewState,
      with event: AsyncCachedImage.Event
    ) {
      switch event {
      case .loadImage:
        break
      case ._startLoadImage:
        state.update(loading: .progress)
      case let ._receivedImage(image):
        state.update(image: Image(uiImage: image), loading: .success)
      case ._loadingFailed:
        state.update(loading: .failure(AsyncCachedImageDownloadError.failed))
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let targetSize: CGSize?
    private let targetAspectRatio: CGSize?

    init(
      targetSize: CGSize? = nil,
      targetAspectRatio: CGSize? = nil
    ) {
      self.targetSize = targetSize
      self.targetAspectRatio = targetAspectRatio
    }

    func callAsFunction(
      event: AsyncCachedImage.Event,
      for state: AsyncCachedImageViewState
    ) async
      -> AsyncCachedImage.Event? {
      switch event {
      case let .loadImage(url):
        guard let url else {
          return ._loadingFailed
        }
        return ._startLoadImage(url)
      case let ._startLoadImage(url):
        do {
          let imageDownloader: AsyncCachedImageDownloadable = AsyncCachedImageDownloader()
          let data = try await imageDownloader.downloadImage(url: url)
          guard !data.isEmpty, let originalImage = UIImage(data: data) else {
            return ._loadingFailed
          }

          let scaledImage: UIImage
          if let targetSize = targetSize, let targetAspectRatio = targetAspectRatio {
            // Use aspect ratio scaling with transparent padding
            scaledImage = originalImage.scaledToFitWithAspectRatio(
              targetSize: targetSize,
              targetAspectRatio: targetAspectRatio
            ) ?? originalImage
          } else if let targetSize = targetSize {
            // Use regular scaling without aspect ratio enforcement
            scaledImage = originalImage.scaledToFit(targetSize: targetSize) ?? originalImage
          } else if originalImage.size.width > 1024 {
              scaledImage = originalImage.scaledDown(by: 0.5) ?? originalImage
          } else {
            scaledImage = originalImage
          }

          return ._receivedImage(scaledImage)

        } catch {
          return ._loadingFailed
        }
      case ._loadingFailed, ._receivedImage:
        return nil
      }
    }
  }
}

extension UIImage {
    func scaledDown(by factor: CGFloat) -> UIImage? {
        let newSize = CGSize(width: size.width * factor, height: size.height * factor)
        let format = UIGraphicsImageRendererFormat()
        format.scale = self.scale
        format.opaque = true

        let renderer = UIGraphicsImageRenderer(size: newSize, format: format)
          return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }

    /// Scales the image to fit within the target size while maintaining aspect ratio
    /// This helps save memory by not loading images larger than needed
    func scaledToFit(targetSize: CGSize) -> UIImage? {
        let originalSize = self.size

        // If the image is already smaller than or equal to target size, return as-is
        if originalSize.width <= targetSize.width && originalSize.height <= targetSize.height {
            return self
        }

        // Calculate scale factor to fit within target size while maintaining aspect ratio
        let widthScale = targetSize.width / originalSize.width
        let heightScale = targetSize.height / originalSize.height
        let scaleFactor = min(widthScale, heightScale)

        let newSize = CGSize(
            width: originalSize.width * scaleFactor,
            height: originalSize.height * scaleFactor
        )

        let format = UIGraphicsImageRendererFormat()
        format.scale = UIScreen.mainScreen.scale
        format.opaque = true

        let renderer = UIGraphicsImageRenderer(size: newSize, format: format)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }

    /// Scales and pads the image to exactly match the target aspect ratio with transparent background
    /// This ensures consistent aspect ratios across all images in the UI
    func scaledToFitWithAspectRatio(targetSize: CGSize, targetAspectRatio: CGSize) -> UIImage? {
        let originalSize = self.size
        let targetRatio = targetAspectRatio.width / targetAspectRatio.height

        // First, scale the image to fit within the target size while maintaining its aspect ratio
        let widthScale = targetSize.width / originalSize.width
        let heightScale = targetSize.height / originalSize.height
        let scaleFactor = min(widthScale, heightScale)

        let scaledSize = CGSize(
            width: originalSize.width * scaleFactor,
            height: originalSize.height * scaleFactor
        )

        // Calculate the final canvas size that matches the target aspect ratio
        let canvasSize: CGSize
        if abs(targetRatio - (scaledSize.width / scaledSize.height)) < 0.01 {
            // Image already has the correct aspect ratio
            canvasSize = scaledSize
        } else if targetRatio > (scaledSize.width / scaledSize.height) {
            // Target is wider, add horizontal padding
            canvasSize = CGSize(width: scaledSize.height * targetRatio, height: scaledSize.height)
        } else {
            // Target is taller, add vertical padding
            canvasSize = CGSize(width: scaledSize.width, height: scaledSize.width / targetRatio)
        }

        // Ensure canvas doesn't exceed target size
        let finalCanvasSize = CGSize(
            width: min(canvasSize.width, targetSize.width),
            height: min(canvasSize.height, targetSize.height)
        )

        // Create the final image with transparent background
        let format = UIGraphicsImageRendererFormat()
        format.scale = self.scale
        format.opaque = false // Allow transparency

        let renderer = UIGraphicsImageRenderer(size: finalCanvasSize, format: format)
        return renderer.image { context in
            // Fill with transparent background
            context.cgContext.clear(CGRect(origin: .zero, size: finalCanvasSize))

            // Calculate centered position for the scaled image
            let x = (finalCanvasSize.width - scaledSize.width) / 2
            let y = (finalCanvasSize.height - scaledSize.height) / 2

            // Draw the scaled image centered on the transparent canvas
            self.draw(in: CGRect(x: x, y: y, width: scaledSize.width, height: scaledSize.height))
        }
    }
}
