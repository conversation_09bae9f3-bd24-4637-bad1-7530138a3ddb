import OGAppKitSDK
import OGL10n
import OGViewStore
import UICatalog

extension ShopUspsView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore(shopUsps: ShopUsps)
    -> Store {
    Store(
      initialState: ViewState(isLoading: shopUsps.isLoading, shopUsps: shopUsps),
      reducer: ShopUspsView.ViewState.Reducer.reduce,
      connector: ShopUspsView.ViewState.Connector(shopUsps: shopUsps)
    )
  }

  enum Event: OGViewEvent {
    case _received(ShopUsps)
  }

  struct ViewState: OGViewState {
    private(set) var isLoading: Bool = true
    private var shopUsps: ShopUsps?

    init(isLoading: Bool = true, shopUsps: ShopUsps? = nil) {
      self.isLoading = isLoading
      self.shopUsps = shopUsps
    }

    var usps: [UspsItem] {
      guard let paybackPoints = shopUsps?.content.paybackPoints else {
        return defaultItems
      }
      var items = defaultItems
      let item = UspsItem(
        icon: OGImages.icon24x24Payback.name,
        color: OGColors.textBonusProgramm.name,
        text: ogL10n.ProductDetail.PayBack.Title(points: "\(paybackPoints)"),
        accessibilityLabel: ogL10n.ProductDetail.PayBack.Title.Accessibility(points: "\(paybackPoints)")
      )
      let index = Int(shopUsps?.config.paybackPointsIndex ?? Int32(items.count))
      if index >= 0, index < items.count {
        items.insert(item, at: index)
      } else {
        items.append(item)
      }
      return items
    }

    private var defaultItems: [UspsItem] {
      ogL10n.ProductDetail.ShopUsps.Usps.components(separatedBy: "\n").enumerated().map {
        UspsItem(
          icon: "icon24x24PdpUsp\($0.offset + 1)",
          color: OGColors.textOnLight.name,
          text: $0.element,
          accessibilityLabel: $0.element
        )
      }
    }

    mutating func update(shopUsps: ShopUsps) {
      self.shopUsps = shopUsps
      isLoading = shopUsps.isLoading
    }

    static var initial = Self()
  }

  struct UspsItem: Hashable {
    let icon: String
    let color: String
    let text: String
    let accessibilityLabel: String
  }
}

extension ShopUspsView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ShopUspsView.ViewState,
      with event: ShopUspsView.Event
    ) {
      switch event {
      case let ._received(shopUsps):
        state.update(shopUsps: shopUsps)
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let shopUsps: ShopUsps
    init(shopUsps: ShopUsps) {
      self.shopUsps = shopUsps
    }

    func configure(
      dispatch: @escaping (ShopUspsView.Event) async -> Void
    ) async {
      await dispatch(._received(shopUsps))
    }
  }
}
