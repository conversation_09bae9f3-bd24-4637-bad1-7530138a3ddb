import OGL10n
import SwiftUI
import UICatalog

struct ProductVariantView: View {
  let name: String
  let availability: AvailabilityState
  let isLoading: Bool

  var body: some View {
    HStack {
      AdaptiveHStack(spacing: UILayoutConstants.Default.padding) {
        Text(ogL10n.ProductDetail.Size.Title)
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
        Text(name)
          .font(for: .titleS)
          .foregroundColor(OGColors.textOnLight.color)
          .padding(.trailing, UILayoutConstants.Default.padding)
      }
      .shimmering(active: isLoading)
      Spacer()
    }
    .accessibilityElement(children: .combine)
  }
}
