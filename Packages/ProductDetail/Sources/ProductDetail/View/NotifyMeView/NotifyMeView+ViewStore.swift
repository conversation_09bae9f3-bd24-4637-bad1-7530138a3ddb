import OGDIService
import OGDomainStore
import OGFeatureAdapter
import <PERSON><PERSON>outer
import OGViewStore

extension NotifyMeView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore(url: String?)
    -> Store {
    Store(
      initialState: .initial,
      reducer: NotifyMeView.Reducer.reduce,
      middleware: NotifyMeView.Middleware(url: url)
    )
  }

  enum Event: OGViewEvent {
    case openNotifyMe
  }

  public struct ViewState: OGViewState {
    public static var initial = ViewState()
  }

  enum Reducer {
    static func reduce(
      _ state: inout NotifyMeView.ViewState,
      with event: NotifyMeView.Event
    ) {}
  }

  struct Middleware: OGViewStoreMiddleware {
    let url: String?
    let router: OGRoutePublishing
    let baseUrl: OGBaseUrlFeatureAdaptable
    init(
      url: String?,
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      baseUrl: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl()
    ) {
      self.url = url
      self.router = router
      self.baseUrl = baseUrl
    }

    func callAsFunction(
      event: NotifyMeView.Event,
      for state: NotifyMeView.ViewState
    ) async -> NotifyMeView.Event? {
      switch event {
      case .openNotifyMe:
        guard let url else { return nil }
        let notifyMeURL = baseUrl.urlRelativeToWebUrl(forUrlPath: url)
        let route = OGRoute(.productDetailSubFlow, data: notifyMeURL)
        router.send(route)
        return nil
      }
    }
  }
}
