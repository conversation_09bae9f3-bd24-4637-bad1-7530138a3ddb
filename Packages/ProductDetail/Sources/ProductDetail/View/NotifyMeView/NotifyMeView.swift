import OGDIService
import OGL10n
import SwiftUI
import UICatalog

struct NotifyMeView: View {
  private let isWishlisted: Bool
  private let productIdForWishlisting: String?

  @StateObject private var viewStore: Self.Store
  @OGInjected(\ProductDetailContainer.buttonStyleResolver) private var buttonStyleResolver

  init(isWishlisted: Bool = false, productIdForWishlisting: String? = nil, url: String?) {
    self.isWishlisted = isWishlisted
    self.productIdForWishlisting = productIdForWishlisting
    _viewStore = StateObject(wrappedValue: Self.makeStore(url: url))
  }

  var body: some View {
    if let productIdForWishlisting {
      addToWishlistAndNotifyMe(productIdForWishlisting)
    } else {
      notifyMeButton
    }
  }

  func addToWishlistAndNotifyMe(_ productIdForWishlisting: String) -> some View {
    HStack(spacing: UILayoutConstants.Default.padding) {
      WishlistButtonView(isWishlisted: isWishlisted, productId: productIdForWishlisting, hasCircleBackground: true)
      Spacer()
      notifyMeButton
    }
  }

  var notifyMeButton: some View {
    C2AButton(
      title: ogL10n.ProductDetail.NotifyMe.Button.Title,
      accessibilityIdentifier: ogL10n.ProductDetail.NotifyMe.Button.Title
    ) {
      Task {
        await viewStore.dispatch(.openNotifyMe)
      }
    }
    .register(buttonStyleResolver.primaryButtonStyle)
  }
}
