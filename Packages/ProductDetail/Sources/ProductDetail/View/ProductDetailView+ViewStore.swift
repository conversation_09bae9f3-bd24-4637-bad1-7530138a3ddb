import AppCore
import Combine
import Foundation
import OGAppKitSDK
import OGDomainStore
import OGL10n
import <PERSON><PERSON>outer
import OGTracker
import OGViewStore
import SwiftUI
import UICatalog

extension ProductDetailView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore(
    route: OGRoute
  )
    -> Store {
    ProductDetailContainer.shared.domainStore.register {
      OGDomainStoreFactory.make(route: route)
    }

    let selectedVariantPublisher = CurrentValueSubject<Decodable, Never>(SelectedVariant.empty)
    return Store(
      initialState: .initial,
      reducer: ProductDetailView.ViewState.Reducer.reduce,
      middleware: ProductDetailView.ViewState.Middleware(selectedVariantPublisher: selectedVariantPublisher),
      connector: ProductDetailView.ViewState.Connector(selectedVariantPublisher: selectedVariantPublisher)
    )
  }
}

// MARK: - ProductDetailView.ViewState

extension ProductDetailView {
  public struct ViewState: OGViewState {
    private(set) var loadingState: Loadable
    private(set) var components: [OGProductDetailComponent]
    private(set) var header: ProductHeader?
    private(set) var stickyBasket: StickyBasket?
    private(set) var showsHeaderWishlistIcon: Bool
    private(set) var showsStickyBasketButton: Bool
    private(set) var indexOfCurrentImage: Int
    private(set) var isLoading: Bool
    private(set) var maxFlowedItems: Int = 0
    private(set) var galleryIndex: Int = 0
    private(set) var selectedVariant: SelectedVariant
    private(set) var hasVariantName: Bool
    private(set) var screeDidChange: Bool = false

    var canAddToBasket: Bool {
      selectedVariant.userDidChangeVariant || productDimensions == nil
    }

    var slowLoading: TimeInterval {
      8
    }

    private var productDimensions: ProductDimensions?
    private var monitoredViews: [MonitoredView: Bool]

    public init(
      loadingState: Loadable = .initial,
      components: [OGProductDetailComponent] = [],
      showsHeaderWishlistIcon: Bool = false,
      showsStickyBasketButton: Bool = false,
      indexOfCurrentImage: Int = 0,
      isLoading: Bool = true,
      selectedVariant: SelectedVariant = .empty,
      productDimensions: ProductDimensions? = nil,
      monitoredViews: [MonitoredView: Bool] = [:],
      hasVariantName: Bool = false,
      screeDidChange: Bool = false
    ) {
      self.loadingState = loadingState
      self.components = components
      self.showsHeaderWishlistIcon = showsHeaderWishlistIcon
      self.showsStickyBasketButton = showsStickyBasketButton
      self.indexOfCurrentImage = indexOfCurrentImage
      self.isLoading = isLoading
      self.selectedVariant = selectedVariant
      self.productDimensions = productDimensions
      self.monitoredViews = monitoredViews
      self.hasVariantName = hasVariantName
      self.screeDidChange =  screeDidChange
    }

    mutating func update(screeDidChange: Bool) {
      self.screeDidChange = screeDidChange
    }

    mutating func update(
      loadingState: Loadable? = nil,
      components: [OGProductDetailComponent]? = nil,
      header: ProductHeader? = nil,
      stickyBasket: StickyBasket? = nil,
      showsHeaderWishlistIcon: Bool? = nil,
      selectedVariant: SelectedVariant? = nil,
      indexOfCurrentImage: Int? = nil
    ) {
      self.loadingState = loadingState ?? self.loadingState
      if self.components.isEmpty {
        self.components = components ?? self.components
      } else if self.components.count != components?.count {
        self.components = components ?? self.components
      } else if !(components?.gallery?.content.hasEqualContent(to: self.components.gallery?.content) ?? false) {
        self.components = components ?? self.components
      } else if !(components?.dynamicYieldRecommendations.hasEqualContent(to: self.components.dynamicYieldRecommendations) ?? false) {
        self.components = components ?? self.components
      }

      productDimensions = components?.productDimensions ?? self.components.productDimensions

      if let productDimensions,
         productDimensions.isLoading == false,
         let index = self.components.firstIndex(where: { $0.toMonitoredView == .productDimensions }) {
        self.components[index] = .productDimensions(productDimensions)
      }

      galleryIndex = self.components.firstIndex(where: { $0.toMonitoredView == .productGallery }) ?? 0
      maxFlowedItems = (self.components.firstIndex(where: { $0.toMonitoredView == .addToBasketButton }) ?? 0) - galleryIndex
      self.header = header ?? self.header
      self.stickyBasket = stickyBasket ?? self.stickyBasket
      self.showsHeaderWishlistIcon = showsHeaderWishlistIcon ?? self.showsHeaderWishlistIcon
      self.selectedVariant = selectedVariant ?? self.selectedVariant
      self.indexOfCurrentImage = indexOfCurrentImage ?? self.indexOfCurrentImage
      hasVariantName = self.components.hasProductVariant
      isLoading = header?.isLoading ?? isLoading
    }

    mutating func update(monitoredView: MonitoredView, isVisible: Bool) {
      monitoredViews[monitoredView] = isVisible
      showsHeaderWishlistIcon = !(monitoredViews[.productGallery] ?? true)
      var components: [OGProductDetailComponent] {
        if UIDevice.current.userInterfaceIdiom == .pad {
          self.components.filter { $0.toMonitoredView != .productGallery }
        } else {
          self.components
        }
      }
      if let index = components.firstIndex(where: { $0.toMonitoredView == .addToBasketButton }), index > 0 {
        let relevantMonitoredViews = components[0...index]
          .map(\.toMonitoredView)
          .compactMap { relevantMonitoredView in
            monitoredViews.first(where: { $0.key == relevantMonitoredView }) ?? (relevantMonitoredView, true)
          }

        let allFalse = relevantMonitoredViews.allSatisfy { $0.value == false }
        let oneTrue = relevantMonitoredViews.first(where: { $0.value == true }) != nil
        let isVoiceOverRunning = UIAccessibility.isSpeakScreenEnabled || UIAccessibility.isSpeakSelectionEnabled || UIAccessibility.isVoiceOverRunning
        if !showsStickyBasketButton, allFalse, !isVoiceOverRunning, !isLoading {
          showsStickyBasketButton = true
        } else if showsStickyBasketButton, oneTrue {
          showsStickyBasketButton = false
        }
      }
    }

    public static var initial = ViewState()
  }
}

// MARK: - ProductDetailView.Event

extension ProductDetailView {
  enum Event: OGViewEvent {
    case reload
    case getDetailScreenForColor(id: String)
    case getDetailScreenForSize(id: String)
    case getDetailScreenWith(id: String, secondaryId: String?)
    case monitoredView(MonitoredView, Bool)
    case openVariantSelection
    case indexOfCurrentImage(Int)
    case slowLoadingBanner(Bool)
    case updateBadgeCount
    case trackScreenView

    /// Private Events
    case _error(ProductDetailError)
    case _updating
    case _received([OGProductDetailComponent], ProductHeader, StickyBasket)
    case _receivedSelectedVariant(SelectedVariant)
    case _resetScreeDidChange
  }
}

extension ProductDetailView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductDetailView.ViewState,
      with event: ProductDetailView.Event
    ) {
      switch event {
      case ._updating, .reload:
        state = ProductDetailView.ViewState(loadingState: .progress)
      case let ._received(components, header, stickyBasket):
        state.update(loadingState: .success, components: components, header: header, stickyBasket: stickyBasket)
      case let ._error(error):
        state = ProductDetailView.ViewState(loadingState: .failure(error))
      case let .monitoredView(monitoredView, isVisible):
        state.update(monitoredView: monitoredView, isVisible: isVisible)
      case let ._receivedSelectedVariant(selectedVariant):
        state.update(selectedVariant: selectedVariant)
      case let .indexOfCurrentImage(index):
        state.update(indexOfCurrentImage: index)
      case .getDetailScreenForColor:
        state.update(indexOfCurrentImage: 0)
        state.update(screeDidChange: true)
      case .getDetailScreenForSize:
        state.update(screeDidChange: true)
      case ._resetScreeDidChange:
        state.update(screeDidChange: false)
      case .getDetailScreenWith, .openVariantSelection, .slowLoadingBanner, .trackScreenView, .updateBadgeCount: break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    /// local domain store
    let productDetailStore: ProductDetailStore
    let router: OGRoutePublishing
    let priceFormatter: PriceFormattable
    let selectedVariantPublisher: CurrentValueSubject<Decodable, Never>
    let tracker: OGTrackerProtocol
    let accessibilityAnnouncer: AccessibilityAnnouncing
    init(
      productDetailStore: ProductDetailStore = ProductDetailContainer.shared.domainStore(),
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      priceFormatter: PriceFormattable = ProductDetailContainer.shared.priceFormatter(),
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker(),
      selectedVariantPublisher: CurrentValueSubject<Decodable, Never>,
      accessibilityAnnouncer: AccessibilityAnnouncing = AppContainer.shared.accessibilityAnnouncer()
    ) {
      self.productDetailStore = productDetailStore
      self.router = router
      self.priceFormatter = priceFormatter
      self.selectedVariantPublisher = selectedVariantPublisher
      self.tracker = tracker
      self.accessibilityAnnouncer = accessibilityAnnouncer
    }

    // swiftlint:disable cyclomatic_complexity
    // swiftlint:disable function_body_length
    func callAsFunction(
      event: ProductDetailView.Event,
      for state: ProductDetailView.ViewState
    ) async
      -> ProductDetailView.Event? {
      switch event {
      case .reload:
        productDetailStore.dispatchDetached(ProductDetailAction.getDetailScreen)
        return nil
      case let .getDetailScreenForSize(id):
        if let itemName = state.header?.content.title {
          tracker.multiplatformTrack(event: Interaction.ProductDetailSelectVariantBySize(itemId: id, itemName: itemName))
        }
        if !id.isEmpty {
          productDetailStore.dispatchDetached(ProductDetailAction.updateProductVariantSelection(id: id))
        }
        return nil
      case let .getDetailScreenForColor(id):
        if let itemName = state.header?.content.title {
          tracker.multiplatformTrack(event: Interaction.ProductDetailSelectVariantByColor(itemId: id, itemName: itemName))
        }
        if !id.isEmpty {
          productDetailStore.dispatchDetached(ProductDetailAction.updateProductColorSelection(id: id))
        }
        return nil
      case let .getDetailScreenWith(id, secondaryId):
        if !id.isEmpty {
          productDetailStore.dispatchDetached(ProductDetailAction.getDetailScreenWith(id: id, secondaryId: secondaryId))
        }
        return nil
      case .openVariantSelection:
        var style = state.productDimensions?.config.style.toDimensionStyle ?? .flat
        if state.productDimensions?.content as? ProductDimensions.NestedDimensions == nil && style == .nested || style == .categorized {
          style = .flat
        }
        if state.productDimensions?.content is ProductDimensions.VoucherDimension {
          style = .voucher
        }

        let data = state.productDimensions?.content.toVariantSelectionModel(
          style: style,
          priceFormatter: priceFormatter,
          customName: state.selectedVariant.customName
        )

        var variantSelectionRoute = OGRoute.variantSelection(data: data, style: style)
        variantSelectionRoute.set(publisher: selectedVariantPublisher)
        router.send(variantSelectionRoute)
        return nil
      case let ._receivedSelectedVariant(selectedProduct):
        if selectedProduct.userDidChangeVariant {
          return .getDetailScreenForSize(id: selectedProduct.productId)
        } else {
          return nil
        }
      case .updateBadgeCount:
        productDetailStore.dispatchDetached(ProductDetailAction.updateBadgeCount)
        return nil
      case let .slowLoadingBanner(shows):
        if shows {
          router.send(OGRoute(.errorBanner, data: ErrorBanner(
            title: ogL10n.ProductDetail.Error.SlowLoadingBannerText,
            imageName: OGImages.icon24x24Warning.name,
            colorName: OGColors.accentWarning.name
          )))
        } else {
          router.dismiss(route: .errorBanner)
        }
        return nil
      case ._error:
        return .slowLoadingBanner(false)
      case .trackScreenView:
        tracker.multiplatformTrack(event: View.ScreenProductDetailPage())
        if !state.isLoading {
          await accessibilityAnnouncer.announceScreenAppearance(message: "Produkt " + (state.header?.content.title ?? ""), config: .extended)
        }
        return nil
      case ._received:
        if state.screeDidChange, !state.isLoading {
          await accessibilityAnnouncer.announceScreenAppearance(message: "Auswahl aktualisiert", config: .init(screenChangedDelay: 0, announcementDelay: 1.nanoseconds))
          return ._resetScreeDidChange
        }
        return nil
      case ._resetScreeDidChange, ._updating, .indexOfCurrentImage, .monitoredView:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    let productDetailStore: ProductDetailStore
    let selectedVariantPublisher: CurrentValueSubject<Decodable, Never>
    var trace: PerformanceTracking
    init(
      productDetailStore: ProductDetailStore = ProductDetailContainer.shared.domainStore(),
      selectedVariantPublisher: CurrentValueSubject<Decodable, Never>,
      trace: PerformanceTracking = PerformanceTracker()
    ) {
      self.productDetailStore = productDetailStore
      self.selectedVariantPublisher = selectedVariantPublisher
      self.trace = trace
    }

    func configure(
      dispatch: @escaping (ProductDetailView.Event) async -> Void
    ) async {
      await trace.startTrace(name: "PDP_First_Contentful_Paint")
      await configureComponents(dispatch: dispatch)
      await configureError(dispatch: dispatch)

      selectedVariantPublisher
        .compactMap {
          ($0 as? SelectedVariant) != .empty ? $0 as? SelectedVariant : nil
        }
        .sink { selectedProduct in
          Task {
            await dispatch(._receivedSelectedVariant(selectedProduct))
          }
        }
        .store(in: &cancellables)
    }

    private func configureError(
      dispatch: @escaping (ProductDetailView.Event) async -> Void
    ) async {
      await productDetailStore
        .watchError(type: ProductDetailError.self)
        .sink { error in
          Task {
            await dispatch(._error(error))
          }
        }
        .store(in: &cancellables)
    }

    private func configureComponents(
      dispatch: @escaping (ProductDetailView.Event) async -> Void
    ) async {
      let components = await productDetailStore
        .statePublisher
        .map(\.components)
        .removeDuplicates()
      let header = await productDetailStore
        .statePublisher
        .map(\.header)
        .compactMap { $0 }
        .removeDuplicates()
      let basket = await productDetailStore
        .statePublisher
        .map(\.basket)
        .compactMap { $0 }
        .removeDuplicates()

      Publishers.CombineLatest3(components, header, basket)
        .sink { [weak self] components, header, basket in
          Task {
            await dispatch(._received(components, header, basket))
            if !header.isLoading {
              await self?.trace.stopTrace()
            }
          }
        }
        .store(in: &cancellables)
    }
  }
}

extension Double {
  /// Converts seconds to nanoseconds
  var nanoseconds: UInt64 {
    UInt64(self * 1_000_000_000)
  }
}
