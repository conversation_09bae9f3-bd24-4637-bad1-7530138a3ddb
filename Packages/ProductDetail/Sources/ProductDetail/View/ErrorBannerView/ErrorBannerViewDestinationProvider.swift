import OGCore
import OGDIService
import OGRouter
import SwiftUI
import UICatalog

// MARK: - ErrorBannerViewDestinationProvider

public struct ErrorBannerViewDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .errorBanner
  @OGInjected(\OGRoutingContainer.routePublisher) private var router
  public init() {}

  public func provide(_ route: OGRoute) -> some SwiftUI.View {
    if let errorBanner: ErrorBanner = route.getData() {
      BannerView(
        image: Image(errorBanner.imageName),
        text: errorBanner.title,
        backgroundColor: OGColors.backgroundBackground0.color,
        dividerColor: Color(errorBanner.colorName)
      ).onTapGesture {
        router.dismiss(route: .errorBanner)
      }
    }
  }

  public func presentationType() -> OGPresentationType {
    .banner
  }
}

extension OGIdentifier {
  public static let errorBanner = #identifier("errorBanner")
}

extension OGRoute {
  public static let errorBanner = OGRoute(OGIdentifier.errorBanner.value)
}

// MARK: - ErrorBanner

struct ErrorBanner: Codable {
  let title: String
  let imageName: String
  let colorName: String
  init(title: String, imageName: String = OGImages.icon24x24Danger.name, colorName: String = OGColors.accentDanger.name) {
    self.title = title
    self.imageName = imageName
    self.colorName = colorName
  }
}
