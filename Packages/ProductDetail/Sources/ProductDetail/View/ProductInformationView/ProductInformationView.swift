import OGAppKitSDK
import SwiftUI
import UICatalog

struct ProductInformationView: SwiftUI.View {
  let productInformation: ProductInformation
  var body: some SwiftUI.View {
    VStack(spacing: .zero) {
      ProductInformationDescriptionView(productDescription: productInformation.content.description_, isLoading: productInformation.isLoading)
        .padding(.leading, UILayoutConstants.Default.padding2x)
      ComponentDivider()
        .padding(.leading, UILayoutConstants.Default.padding2x)
      if !productInformation.content.details.attributesTable.sections.isEmpty {
        ProductInformationDetailsView(productDetails: productInformation.content.details, isLoading: productInformation.isLoading)
          .padding(.leading, UILayoutConstants.Default.padding2x)
        ComponentDivider()
          .padding(.leading, UILayoutConstants.Default.padding2x)
      }
      if let brand = productInformation.content.brand {
        ProductInformationBrandView(productBrand: brand, isLoading: productInformation.isLoading)
          .padding(.leading, UILayoutConstants.Default.padding2x)
        ComponentDivider()
          .padding(.leading, UILayoutConstants.Default.padding2x)
      }
      if let importantInformation = productInformation.content.importantInformation {
        ProductImportantInformationView(importantInformation: importantInformation, isLoading: productInformation.isLoading)
          .padding(.leading, UILayoutConstants.Default.padding2x)
        ComponentDivider()
          .padding(.leading, UILayoutConstants.Default.padding2x)
      }
      if let articleStandards = productInformation.content.articleStandards {
        ProductArticleStandardsView(articleStandards: articleStandards, isLoading: productInformation.isLoading)
          .padding(.leading, UILayoutConstants.Default.padding2x)
        ComponentDivider()
          .padding(.leading, UILayoutConstants.Default.padding2x)
      }
    }
    .padding(.bottom, UILayoutConstants.Default.padding2x)
  }
}
