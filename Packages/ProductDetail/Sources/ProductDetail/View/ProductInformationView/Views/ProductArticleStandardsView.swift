import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ProductArticleStandardsView

struct ProductArticleStandardsView: SwiftUI.View {
  let articleStandards: ArticleStandards
  let isLoading: Bool
  var body: some SwiftUI.View {
    DropdownView(isLoading: isLoading, name: ogL10n.ProductDetail.Information.ArticleStandards.Title) {
      HStack {
        Text(ogL10n.ProductDetail.Information.ArticleStandards.Title)
          .font(for: .copyL)
          .foregroundColor(OGColors.textOnLight.color)
        categoryIcons
      }
    } contentView: {
      VStack(alignment: .leading) {
        ForEach(Array(articleStandards.seals.enumerated()), id: \.offset) { index, seal in
          row(icon: seal.category.imageNameLarge, title: seal.category.title, detail: seal.category.detail)
          ComponentDivider()
            .padding(.vertical, UILayoutConstants.Default.padding2x)
          row(icon: seal.imageName, title: seal.title, detail: seal.detail)
          if index < articleStandards.seals.count - 1 {
            ComponentDivider()
              .padding(.vertical, UILayoutConstants.Default.padding2x)
          }
        }
      }
      .padding(.vertical, UILayoutConstants.Default.padding)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
      .frame(maxWidth: .infinity, alignment: .leading)
    }
  }

  func row(icon: String?, title: String, detail: String) -> some SwiftUI.View {
    HStack(alignment: .top) {
      VStack(alignment: .leading) {
        if let icon {
          Image(icon)
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: UILayoutConstants.ProductArticleStandardsView.imageWidth)
            .accessibilityHidden(true)
        }
      }.frame(width: UILayoutConstants.ProductArticleStandardsView.sectionWidth)

      VStack(alignment: .leading) {
        Text(title)
          .foregroundColor(OGColors.textOnLight.color)
          .font(for: .titleM)
          .padding(.bottom, UILayoutConstants.Default.padding)
        Text(detail)
          .foregroundColor(OGColors.textOnLight.color)
          .font(for: .copyMRegular)
          .multilineTextAlignment(.leading)
      }
    }
    .frame(maxWidth: .infinity, alignment: .leading)
  }

  private var categoryIcons: some SwiftUI.View {
    ForEach(articleStandards.seals, id: \.self) { seal in
      Image(seal.category.imageName)
        .accessibilityLabel(seal.category.title)
    }
  }
}

// MARK: - UILayoutConstants.ProductArticleStandardsView

extension UILayoutConstants {
  enum ProductArticleStandardsView {
    static let sectionWidth: CGFloat = 60
    static let imageWidth: CGFloat = 44
  }
}
