import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ProductSafetyView

struct ProductSafetyView: SwiftUI.View {
  let importantInformation: ProductInformation.ImportantInformation
  @Environment(\.dismiss) private var dismiss
  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: .zero) {
      headerView
        .padding(.bottom, UILayoutConstants.Default.padding2x)

      Text(ogL10n.ProductDetail.Information.ImportantInformation.ProductSafety.Responsible.Title)
        .font(for: .titleM)
        .foregroundColor(OGColors.textOnLight.color)
        .multilineTextAlignment(.leading)
        .padding(.bottom, UILayoutConstants.Default.padding)

      Text(ogL10n.ProductDetail.Information.ImportantInformation.ProductSafety.Responsible.Detail)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
        .multilineTextAlignment(.leading)
        .padding(.bottom, UILayoutConstants.Default.padding2x)
      ForEach(importantInformation.distributingCompanies, id: \.self) { distributingCompany in
        Text(distributingCompany.name)
          .font(for: .titleM)
          .foregroundColor(OGColors.textOnLight.color)
          .multilineTextAlignment(.leading)
          .padding(.bottom, UILayoutConstants.Default.padding)

        Text(distributingCompany.data)
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .multilineTextAlignment(.leading)
          .padding(.bottom, UILayoutConstants.Default.padding2x)
      }

      Text(ogL10n.ProductDetail.Information.ImportantInformation.ProductSafety.Responsible.Note)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
        .multilineTextAlignment(.leading)

      Spacer()
    }
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
    .frame(maxWidth: .infinity, alignment: .leading)
  }

  private var headerView: some SwiftUI.View {
    VStack(spacing: UILayoutConstants.ProductSafetyView.headerSpacing) {
      ZStack {
        Text(ogL10n.ProductDetail.Information.ImportantInformation.ProductSafety.Title)
          .font(for: .titleM)
          .foregroundColor(OGColors.navigationBarElementTitle.color)
          .accessibilityAddTraits(.isHeader)
          .dynamicTypeSize(...DynamicTypeSize.xLarge)
        HStack {
          Spacer()
          ProductDetailCloseButton(accessibilityLabel: ogL10n.General.Close, dropShadow: false) {
            dismiss()
          }
        }
      }
    }
    .padding(UILayoutConstants.ProductSafetyView.headerPadding)
    .background(OGColors.backgroundBarNavigationBar.color)
  }
}

// MARK: - UILayoutConstants.ProductSafetyView

extension UILayoutConstants {
  enum ProductSafetyView {
    static let headerPadding: EdgeInsets = .init(top: 16, leading: 16, bottom: 0, trailing: 0)
    static let headerSpacing: CGFloat = 8
  }
}
