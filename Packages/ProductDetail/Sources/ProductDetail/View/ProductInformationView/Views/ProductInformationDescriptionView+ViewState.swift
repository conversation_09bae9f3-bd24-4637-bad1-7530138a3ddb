import Foundation
import OGRouter
import OGViewStore

extension ProductInformationDescriptionView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore()
    -> Store {
    Store(
      initialState: .initial,
      reducer: ProductInformationDescriptionView.ViewState.Reducer.reduce,
      middleware: ProductInformationDescriptionView.ViewState.Middleware()
    )
  }
}

// MARK: - ProductInformationDescriptionView.ViewState

extension ProductInformationDescriptionView {
  public struct ViewState: OGViewState {
    public static var initial = ViewState()
  }
}

// MARK: - ProductInformationDescriptionView.Event

extension ProductInformationDescriptionView {
  enum Event: OGViewEvent {
    case openURL(String)
  }
}

extension ProductInformationDescriptionView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductInformationDescriptionView.ViewState,
      with event: ProductInformationDescriptionView.Event
    ) {}
  }

  struct Middleware: OGViewStoreMiddleware {
    let router: OGRoutePublishing
    init(router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher()) {
      self.router = router
    }

    func callAsFunction(
      event: ProductInformationDescriptionView.Event,
      for state: ProductInformationDescriptionView.ViewState
    ) async
      -> ProductInformationDescriptionView.Event? {
      switch event {
      case let .openURL(url):
        router.send(OGRoute(url: URL(string: url)))
        return nil
      }
    }
  }
}
