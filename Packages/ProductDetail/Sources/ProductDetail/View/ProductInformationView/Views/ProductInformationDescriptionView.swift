import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

struct ProductInformationDescriptionView: SwiftUI.View {
  let productDescription: ProductInformation.Description
  let isLoading: Bool
  @StateObject private var viewStore: Self.Store = Self.makeStore()
  var body: some SwiftUI.View {
    DropdownView(isLoading: isLoading, name: ogL10n.ProductDetail.Information.Description.Title) {
      Text(ogL10n.ProductDetail.Information.Description.Title)
        .font(for: .copyL)
        .foregroundColor(OGColors.textOnLight.color)
    } contentView: {
      VStack(alignment: .leading) {
        ForEach(productDescription.bulletPoints, id: \.self) { bulletPoint in
          HStack(alignment: .top) {
            Text("•")
              .accessibilityHidden(true)
            Text(bulletPoint)
          }
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
        }
        .padding(.bottom, UILayoutConstants.Default.padding)
        .padding(.trailing, UILayoutConstants.Default.padding2x)
        .accessibilityElement(children: .combine)
        ComponentDivider()
          .padding(.bottom, UILayoutConstants.Default.padding2x)

        Text("\(ogL10n.ProductDetail.Information.Description.ArticleNumber) \(productDescription.articleNumber)")
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .padding(.bottom, UILayoutConstants.Default.padding)
          .padding(.trailing, UILayoutConstants.Default.padding2x)
        ComponentDivider()
          .padding(.bottom, UILayoutConstants.Default.padding2x)

        Text(productDescription.text)
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .padding(.bottom, UILayoutConstants.Default.padding2x)
          .padding(.trailing, UILayoutConstants.Default.padding2x)

        if !productDescription.documents.isEmpty {
          ComponentDivider()
            .padding(.bottom, UILayoutConstants.Default.padding)
        }
        ForEach(productDescription.documents, id: \.self) { document in
          link(document.text, url: document.url)
            .padding(.bottom, UILayoutConstants.Default.padding)
        }
        .accessibilityElement(children: .combine)
        .padding(.trailing, UILayoutConstants.Default.padding2x)
      }

      .multilineTextAlignment(.leading)
      .padding(.vertical, UILayoutConstants.Default.padding)
      .frame(maxWidth: .infinity, alignment: .leading)
    }
  }

  private func link(_ title: String, url: String) -> some SwiftUI.View {
    Button {
      Task {
        await viewStore.dispatch(.openURL(url))
      }
    } label: {
      HStack {
        OGImages.icon24x24Document.image
        Text(title)
          .underline()
          .baselineOffset(UILayoutConstants.Default.underlineOffset)
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.navigationBarElementAction.color)
      }
      .padding(.trailing, UILayoutConstants.Default.padding)
    }
  }
}
