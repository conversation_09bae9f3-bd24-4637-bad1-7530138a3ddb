import OGAppKitSDK
import OGDIService
import OGL10n
import SwiftUI
import UICatalog

struct ProductImportantInformationView: SwiftUI.View {
  let importantInformation: ProductInformation.ImportantInformation
  let isLoading: Bool
  @OGInjected(\ProductDetailContainer.buttonStyleResolver) private var buttonStyleResolver
  @State private var showingSheet: Bool = false
  var body: some SwiftUI.View {
    DropdownView(isLoading: isLoading, name: ogL10n.ProductDetail.Information.ImportantInformation.Title) {
      Text(ogL10n.ProductDetail.Information.ImportantInformation.Title)
        .font(for: .copyL)
        .foregroundColor(OGColors.textOnLight.color)
    } contentView: {
      C2AButton(
        title: ogL10n.ProductDetail.Information.ImportantInformation.Button.Title,
        accessibilityIdentifier: ogL10n.ProductDetail.Information.ImportantInformation.Button.Title
      ) {
        showingSheet.toggle()
      }
      .register(buttonStyleResolver.secondaryButtonStyle)

      .padding(.vertical, UILayoutConstants.Default.padding)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
      .frame(maxWidth: .infinity, alignment: .leading)
    }
    .sheet(isPresented: $showingSheet) {
      ProductSafetyView(importantInformation: importantInformation)
    }
  }
}
