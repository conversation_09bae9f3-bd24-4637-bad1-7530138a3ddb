import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

struct ProductInformationBrandView: SwiftUI.View {
  let productBrand: ProductInformation.Brand
  let isLoading: Bool
  var body: some SwiftUI.View {
    DropdownView(isLoading: isLoading, name: ogL10n.ProductDetail.Information.Brand.Title) {
      Text(ogL10n.ProductDetail.Information.Brand.Title)
        .font(for: .copyL)
        .foregroundColor(OGColors.textOnLight.color)
    } contentView: {
      VStack(alignment: .leading) {
        if let description = productBrand.description_ {
          Text(description)
            .foregroundColor(OGColors.textOnLight.color)
            .font(for: .copyMRegular)
            .multilineTextAlignment(.leading)
        }
      }
      .frame(maxWidth: .infinity, alignment: .leading)
      .padding(.vertical, UILayoutConstants.Default.padding)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
    }
  }
}
