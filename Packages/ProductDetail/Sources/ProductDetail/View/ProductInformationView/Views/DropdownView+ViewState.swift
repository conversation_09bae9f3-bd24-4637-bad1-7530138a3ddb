import OGAppKitSDK
import OGTracker
import OGViewStore

extension DropdownView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore()
    -> Store {
    Store(
      initialState: .initial,
      reducer: DropdownView.ViewState.Reducer.reduce,
      middleware: DropdownView.ViewState.Middleware()
    )
  }

  public struct ViewState: OGViewState {
    public static var initial: Self {
      .init()
    }
  }

  enum Event: OGViewEvent {
    case trackExpanded(Bool, String)
  }
}

extension DropdownView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout DropdownView.ViewState,
      with event: DropdownView.Event
    ) {}
  }

  struct Middleware: OGViewStoreMiddleware {
    let tracker: OGTrackerProtocol
    init(tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()) {
      self.tracker = tracker
    }

    func callAsFunction(
      event: DropdownView.Event,
      for state: DropdownView.ViewState
    ) async
      -> DropdownView.Event? {
      switch event {
      case let .trackExpanded(isExpanded, title):
        if isExpanded {
          tracker.multiplatformTrack(event: Interaction.ProductDetailViewDescription(sectionTitle: title))
        }
        return nil
      }
    }
  }
}
