import OGL10n
import SwiftUI
import UICatalog

// MARK: - DropdownView

struct DropdownView<ContentView: View, TitleView: View>: View {
  let isLoading: Bool
  let name: String
  @ViewBuilder let titleView: () -> TitleView
  @ViewBuilder let contentView: () -> ContentView
  @State private var showDescription = false
  @StateObject private var viewStore: Self.Store = Self.makeStore()
  init(
    isLoading: Bool,
    name: String = "",
    @ViewBuilder titleView: @escaping () -> TitleView,
    @ViewBuilder contentView: @escaping () -> ContentView
  ) {
    self.isLoading = isLoading
    self.name = name
    self.titleView = titleView
    self.contentView = contentView
  }

  var body: some View {
    Button {
      withAnimation {
        showDescription.toggle()
        Task {
          await viewStore.dispatch(.trackExpanded(showDescription, name))
        }
      }
    } label: {
      HStack {
        titleView()
          .multilineTextAlignment(.leading)
        Spacer()
        Image(OGImages.icon24x24ChevronDownPrimary.name)
          .rotation3DEffect(.degrees(showDescription ? 180 : 0), axis: (x: 1, y: 0, z: 0))
          .accessibilityHidden(true)
          .padding(.trailing, UILayoutConstants.Default.padding2x)
      }
      .shimmering(active: isLoading)
      .frame(minHeight: UILayoutConstants.DropdownView.height)
      .frame(maxWidth: .infinity)
      .contentShape(Rectangle())
    }
    .accessibilityAddTraits(.isHeader)
    .accessibilityValue(showDescription ? ogL10n.General.Expanded.Accessibility : ogL10n.General.Collapsed.Accessibility)
    if showDescription {
      contentView()
        .padding(.bottom, UILayoutConstants.Default.padding2x)
    }
  }
}

// MARK: - UILayoutConstants.DropdownView

extension UILayoutConstants {
  enum DropdownView {
    static let height: CGFloat = 58
  }
}
