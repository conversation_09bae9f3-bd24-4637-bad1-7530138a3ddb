import OGAppKitSDK
import OGCore
import OGRouter
import SwiftUI

// MARK: - VariantSelectionDestinationProvider

public struct VariantSelectionDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.variantSelection
  }

  public func provide(_ route: OGRoute) -> some SwiftUI.View {
    if let model: VariantSelectionModel = route.getData() {
      VariantSelectionView(variantSelectionModel: model, route: route)
    } else {
      EmptyView()
    }
  }

  public func presentationType() -> OGPresentationType {
    .sheet([.large])
  }
}

extension OGIdentifier {
  public static let variantSelection = #identifier("variantSelection")
}

extension OGRoute {
  public static let variantSelection = OGRoute(OGIdentifier.variantSelection.value)

  static func variantSelection(data: VariantSelectionModel?, style: DimensionStyle) -> OGRoute {
    if style == .flatChips {
      return OGRoute(OGIdentifier.variantSelection.value, data: data, presentation: OGPresentationType.sheet([]))
    }
    return OGRoute(OGRoute.variantSelection, data: data)
  }
}
