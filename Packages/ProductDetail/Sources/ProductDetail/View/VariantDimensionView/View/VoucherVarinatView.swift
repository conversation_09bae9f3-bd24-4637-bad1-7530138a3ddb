import OGDIService
import OGL10n
import SwiftUI
import UICatalog

// MARK: - VoucherVarinatView.VoucherCardWidthPreferenceKey

extension VoucherVarinatView {
  fileprivate struct VoucherCardWidthPreferenceKey: PreferenceKey {
    static let defaultValue: CGFloat = 0

    static func reduce(
      value: inout CGFloat,
      nextValue: () -> CGFloat
    ) {
      value = max(value, nextValue())
    }
  }
}

// MARK: - VoucherVarinatView

struct VoucherVarinatView: View {
  @ObservedObject var viewStore: VariantSelectionView.Store

  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  @State private var voucherCardMaxWidth: CGFloat?
  @State private var text: String = ""
  @FocusState private var isTextFieldFocused: Bool
  private var maxTextLength: Int {
    viewStore.voucherCustomNameMaxLength ?? UILayoutConstants.VoucherVarinatView.maxTextLength
  }

  var body: some View {
    ScrollView(.vertical, showsIndicators: false) {
      VStack(alignment: .leading) {
        dimensionTitle
        voucherCards
        textFieldView
      }
    }
    .onAppear {
      text = viewStore.voucherCustomName ?? ""
    }
  }

  private var dimensionTitle: some SwiftUI.View {
    Text(viewStore.dimensionName)
      .font(for: .titleS)
      .foregroundColor(OGColors.textOnLight.color)
      .padding(UILayoutConstants.VariantSelectionView.rowPadding)
      .accessibilityAddTraits(.isHeader)
  }

  @ViewBuilder private var textFieldView: some View {
    VStack(alignment: .leading, spacing: UILayoutConstants.Default.stackSpacing) {
      Text(ogL10n.ProductDetail.Variant.Voucher.Recipient)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
      VStack {
        TextField("", text: $text)
          .font(for: .copyL)
          .tint(OGColors.textPrimary.color)
          .focused($isTextFieldFocused)
          .placeholder(when: text.isEmpty, placeholder: {
            Text(ogL10n.ProductDetail.Variant.Voucher.TextFieldPlaceholder)
              .font(for: .copyL)
              .foregroundColor(OGColors.backgroundBackground60.color)
              .accessibilityHidden(true)
          })
          .padding(.leading, UILayoutConstants.VoucherVarinatView.textFieldInnerPadding)
          .textFieldStyle(PlainTextFieldStyle())
          .foregroundColor(OGColors.textOnLight.color)
          .padding(.horizontal, UILayoutConstants.Default.padding)
      }
      .accessibilityElement(children: .combine)
      .accessibilityLabel(text.isEmpty ? ogL10n.ProductDetail.Variant.Voucher.TextFieldPlaceholder : text)
      .accessibilityHint(text.isEmpty ? ogL10n.General.DoubleTabToEdit.Accessibility : "")
      .frame(minHeight: UILayoutConstants.VoucherVarinatView.textFieldMinHeight)
      .background(OGColors.backgroundBackground0.color)
      .cornerRadius(cornerRadius.medium)
      .overlay(
        ZStack {
          RoundedRectangle(cornerRadius: cornerRadius.medium)
            .inset(by: -UILayoutConstants.VoucherVarinatView.textFieldOuterBorderWidth / 2)
            .stroke(isTextFieldFocused ? OGColors.accentInfo50.color : .clear, lineWidth: UILayoutConstants.VoucherVarinatView.textFieldOuterBorderWidth)
          RoundedRectangle(cornerRadius: cornerRadius.medium)
            .inset(by: UILayoutConstants.VoucherVarinatView.textFieldBorderWidth / 2)
            .stroke(isTextFieldFocused ? OGColors.accentInfo100.color : OGColors.backgroundBackground60.color, lineWidth: UILayoutConstants.VoucherVarinatView.textFieldBorderWidth)
        }
      )
      HStack(alignment: .top) {
        Text(ogL10n.ProductDetail.Variant.Voucher.Details)
          .font(for: .copyS)
          .foregroundColor(OGColors.textBlack60.color)
          .multilineTextAlignment(.leading)
        Spacer()
        Text("\(text.count) / \(maxTextLength)")
          .accessibilityLabel(ogL10n.ProductDetail.Voucher.TextField.Count.Accessibility(count: String(text.count), max: String(maxTextLength)))
          .font(for: .copyS)
          .foregroundColor(OGColors.textBlack60.color)
          .onChange(of: text, perform: {
            text = String($0.prefix(maxTextLength))
            Task {
              await viewStore.dispatch(.voucherCustomName(text))
            }
          })
          .onAppear {
            if text.isEmpty {
              text = viewStore.voucherCustomName ?? ""
            }
          }
      }
    }
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
    .padding(.top, UILayoutConstants.Default.padding2x)
  }

  @ViewBuilder private var voucherCards: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      HStack(spacing: UILayoutConstants.VoucherVarinatView.cardSpacing) {
        ForEach(viewStore.variants, id: \.name) { voucher in
          voucherCard(voucher: voucher)
        }
        .onPreferenceChange(VoucherCardWidthPreferenceKey.self) {
          voucherCardMaxWidth = $0
        }
      }
      .padding(.horizontal, UILayoutConstants.Default.padding2x)
    }
  }

  @ViewBuilder
  private func voucherCard(voucher: ProductDimensionsVariant) -> some View {
    let isSelected = voucher.isSelected
    VStack(alignment: .leading, spacing: UILayoutConstants.VoucherVarinatView.textSpacing) {
      Text(voucher.name)
        .lineLimit(1)
        .font(for: .copyMRegular)
        .foregroundColor(isSelected ? OGColors.textOnDark.color : OGColors.textOnLight.color)
    }
    .padding(UILayoutConstants.VoucherVarinatView.cardPadding)
    .frame(minWidth: voucherCardMaxWidth)
    .background(GeometryReader { geometry in
      Color.clear.preference(
        key: VoucherCardWidthPreferenceKey.self,
        value: geometry.size.width
      )
    })
    .background(isSelected ? OGColors.navigationBarElementAction.color : OGColors.backgroundBackground10.color)
    .cornerRadius(cornerRadius.full)
    .accessibilityAddTraits(voucher.isSelected ? .isSelected : .isButton)
    .overlay(
      Group {
        if !voucher.isAvailable {
          unavailableOverlay
        }
      }
    )
    .onTapGesture {
      if voucher.isAvailable {
        Task {
          await viewStore.dispatch(.selectVariant(voucher.productId, voucher.isAvailable))
        }
      }
    }
  }

  private var unavailableOverlay: some View {
    GeometryReader { geometry in
      Path { path in
        path.move(to: CGPoint(x: 0, y: geometry.size.height))
        path.addLine(to: CGPoint(x: geometry.size.width, y: 0))
      }
      .stroke(OGColors.backgroundBackground20.color, lineWidth: UILayoutConstants.VoucherVarinatView.unavailableLineWidth)
    }
    .clipShape(RoundedRectangle(cornerRadius: cornerRadius.full))
  }
}

// MARK: - UILayoutConstants.VoucherVarinatView

extension UILayoutConstants {
  enum VoucherVarinatView {
    static let cardSpacing: CGFloat = 8
    static let horizontalPadding: CGFloat = 16
    static let textSpacing: CGFloat = 4
    static let cardPadding: EdgeInsets = .init(top: 8, leading: 17.5, bottom: 8, trailing: 17.5)
    static let borderWidth: CGFloat = 1
    static let unavailableLineWidth: CGFloat = 1
    static let maxTextLength: Int = 29
    static let textFieldBorderWidth: CGFloat = 2.0
    static let textFieldOuterBorderWidth: CGFloat = 4.0
    static let textFieldMinHeight: CGFloat = 56.0
    static let textFieldInnerPadding: CGFloat = 6.0
  }
}
