import OGDIService
import OGL10n
import SwiftUI
import UICatalog

// MARK: - HorizontalVariantSelectionView.VariantCardWidthPreferenceKey

extension HorizontalVariantSelectionView {
  fileprivate struct VariantCardWidthPreferenceKey: PreferenceKey {
    static let defaultValue: CGFloat = 0

    static func reduce(
      value: inout CGFloat,
      nextValue: () -> CGFloat
    ) {
      value = max(value, nextValue())
    }
  }
}

// MARK: - HorizontalVariantSelectionView

struct HorizontalVariantSelectionView: View {
  let variants: [NestedDimension]
  let selectedEntry: NestedDimension?
  let onVariantSelected: (NestedDimension) -> Void
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  @State private var variantCardMaxWidth: CGFloat?
  var body: some View {
    ScrollView(.horizontal, showsIndicators: false) {
      HStack(spacing: UILayoutConstants.HorizontalVariantSelectionView.cardSpacing) {
        ForEach(variants, id: \.name) { variant in
          variantCard(variant: variant)
        }
        .onPreferenceChange(VariantCardWidthPreferenceKey.self) {
          variantCardMaxWidth = $0
        }
      }
      .padding(.horizontal, UILayoutConstants.HorizontalVariantSelectionView.horizontalPadding)
    }
  }

  @ViewBuilder
  private func variantCard(variant: NestedDimension) -> some View {
    let isSelected = selectedEntry?.name == variant.name
    VStack(alignment: .leading, spacing: UILayoutConstants.HorizontalVariantSelectionView.textSpacing) {
      Text(variant.name)
        .lineLimit(1)
        .font(for: OGFonts.copyMRegular)
        .foregroundColor(isSelected ? OGColors.textOnDark.color : OGColors.textOnLight.color)
    }
    .padding(UILayoutConstants.HorizontalVariantSelectionView.cardPadding)
    .frame(minWidth: variantCardMaxWidth)
    .background(GeometryReader { geometry in
      Color.clear.preference(
        key: VariantCardWidthPreferenceKey.self,
        value: geometry.size.width
      )
    })
    .background(isSelected ? OGColors.navigationBarElementAction.color : OGColors.backgroundBackground10.color)
    .cornerRadius(cornerRadius.full)
    .accessibilityAddTraits(isSelected ? .isSelected : .isButton)
    .overlay(
      Group {
        if !variant.isAvailable {
          unavailableOverlay
        }
      }
    )
    .onTapGesture {
      onVariantSelected(variant)
    }
  }

  private var unavailableOverlay: some View {
    GeometryReader { geometry in
      Path { path in
        path.move(to: CGPoint(x: 0, y: geometry.size.height))
        path.addLine(to: CGPoint(x: geometry.size.width, y: 0))
      }
      .stroke(OGColors.backgroundBackground20.color, lineWidth: UILayoutConstants.HorizontalVariantSelectionView.unavailableLineWidth)
    }
    .clipShape(RoundedRectangle(cornerRadius: cornerRadius.full))
    .accessibilityLabel(ogL10n.ProductDetail.Unavailable.Overlay.Accessibility)
  }
}

// MARK: - UILayoutConstants.HorizontalVariantSelectionView

extension UILayoutConstants {
  enum HorizontalVariantSelectionView {
    static let cardSpacing: CGFloat = 8
    static let horizontalPadding: CGFloat = 16
    static let textSpacing: CGFloat = 4
    static let cardPadding: EdgeInsets = .init(top: 8, leading: 17.5, bottom: 8, trailing: 17.5)
    static let borderWidth: CGFloat = 1
    static let unavailableLineWidth: CGFloat = 1
  }
}
