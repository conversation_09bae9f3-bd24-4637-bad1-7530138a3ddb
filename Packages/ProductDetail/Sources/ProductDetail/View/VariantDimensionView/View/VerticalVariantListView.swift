import OGL10n
import SwiftUI
import UICatalog

struct VerticalVariantListView: View {
  @ObservedObject var viewStore: VariantSelectionView.Store

  var body: some View {
    ScrollView {
      HStack {
        Text(viewStore.dimensionName)
          .accessibilityAddTraits(.isHeader)
          .font(for: .titleS)
          .foregroundColor(OGColors.textOnLight.color)
          .multilineTextAlignment(.leading)
        Spacer()
      }
      .padding(.leading, UILayoutConstants.Default.padding2x)
      variantList
      Text(ogL10n.ProductDetail.Variant.PriceDisclaimer)
        .font(for: OGFonts.copyS)
        .foregroundColor(OGColors.textOnLight.color)
        .multilineTextAlignment(.leading)
        .padding(UILayoutConstants.Default.padding2x)
        .frame(maxWidth: .infinity, alignment: .leading)
      Spacer(minLength: UILayoutConstants.VariantSelectionView.minSpacerLength)
    }
  }

  private var variantList: some View {
    VStack(spacing: 0) {
      ForEach(Array(viewStore.variants.enumerated()), id: \.offset) { index, variant in
        VariantRowView(variant: variant) { productId, available in
          Task {
            await viewStore.dispatch(.selectVariant(productId, available))
          }
        }
        if index != viewStore.variants.count - 1 {
          ComponentDivider()
            .padding(.leading, UILayoutConstants.Default.padding2x)
        }
      }
    }
  }
}
