import OGAppKitSDK
import OGDIService
import OGL10n
import <PERSON><PERSON>outer
import SwiftUI
import UICatalog

// MARK: - VariantSelectionLayout

enum VariantSelectionLayout {
  case vertical
  case horizontal
}

// MARK: - VariantSelectionView

struct VariantSelectionView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  @OGInjected(\ProductDetailContainer.buttonStyleResolver) private var buttonStyleResolver
  @State private var basketViewHeight: CGFloat = 0
  init(variantSelectionModel: VariantSelectionModel, route: OGRoute) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(variantSelectionModel: variantSelectionModel, route: route))
  }

  var body: some SwiftUI.View {
    VStack(spacing: .zero) {
      headerView
        .padding(.bottom, UILayoutConstants.Default.padding)
      VStack {
        switch viewStore.dimensionStyle {
        case .flatChips:
          VerticalVariantChipsView(viewStore: viewStore)
        case .flat:
          VerticalVariantListView(viewStore: viewStore)
        case .nested:
          NestedVariantListView(viewStore: viewStore)
        case .categorized:
          VerticalVariantSectionListView(viewStore: viewStore)
        case .voucher:
          VoucherVarinatView(viewStore: viewStore)
        }
        Spacer(minLength: basketViewHeight)
      }
    }
    .background(OGColors.backgroundBackground0.color)
    .frame(maxHeight: .infinity)
    .overlay(alignment: .bottom) {
      VStack(spacing: .zero) {
        ComponentDivider()
        basketButtonView
      }
      .frame(minHeight: UILayoutConstants.VariantSelectionView.basketOverlayHeight)
      .background {
        GeometryReader { proxy in
          Color.clear
            .onChange(of: proxy.size) { size in
              basketViewHeight = size.height
            }
        }
      }
    }
    .onDisappear {
      Task {
        await viewStore.dispatch(.viewDisappeared)
      }
    }
    .onAppear {
      Task {
        await viewStore.dispatch(.trackScreenView)
      }
    }
    .presentToContentHeight(for: viewStore.dimensionStyle, when: [.flatChips])
  }

  @ViewBuilder private var basketButtonView: some SwiftUI.View {
    if viewStore.canAddToBasket {
      BasketButtonView(
        productId: viewStore.selectedProductId ?? "",
        canAddToBasket: true,
        isVoucher: viewStore.isVoucher,
        customName: viewStore.voucherCustomName
      )
      .padding(.horizontal, UILayoutConstants.VariantSelectionView.buttonPadding)
      .padding(.top, UILayoutConstants.VariantSelectionView.buttonPadding)
      .padding(.bottom, UILayoutConstants.VariantSelectionView.buttonPaddingBottom)
      .dynamicTypeSize(...DynamicTypeSize.accessibility1)
    } else if viewStore.notifyMeUrl != nil {
      NotifyMeView(
        isWishlisted: viewStore.isWishlisted,
        productIdForWishlisting: viewStore.productIdForWishlisting,
        url: viewStore.notifyMeUrl
      )
      .padding(.horizontal, UILayoutConstants.VariantSelectionView.buttonPadding)
      .padding(.top, UILayoutConstants.VariantSelectionView.buttonPadding)
      .padding(.bottom, UILayoutConstants.VariantSelectionView.buttonPaddingBottom)
      .dynamicTypeSize(...DynamicTypeSize.accessibility1)
    }
  }

  private var headerView: some SwiftUI.View {
    VStack(spacing: UILayoutConstants.VariantSelectionView.headerSpacing) {
      ZStack {
        Text(viewStore.headerTile)
          .font(for: OGFonts.titleM)
          .foregroundColor(OGColors.navigationBarElementTitle.color)
          .accessibilityAddTraits(.isHeader)
          .dynamicTypeSize(...DynamicTypeSize.xLarge)
        HStack {
          Spacer()
          ProductDetailCloseButton(accessibilityLabel: ogL10n.General.Close, dropShadow: false) {
            Task {
              await viewStore.dispatch(.dismiss)
            }
          }
        }
      }
    }
    .padding(UILayoutConstants.VariantSelectionView.headerPadding)
    .background(OGColors.backgroundBarNavigationBar.color)
  }
}

// MARK: - UILayoutConstants.VariantSelectionView

extension UILayoutConstants {
  enum VariantSelectionView {
    static let cornerRadius: CGFloat = 12
    static let headerSpacing: CGFloat = 8
    static let headerLineWidth: CGFloat = 20
    static let headerLineHeight: CGFloat = 2
    static let headerTopPadding: CGFloat = 16
    static let horizontalPadding: CGFloat = 16
    static let buttonPadding: CGFloat = 16
    static let buttonPaddingBottom: CGFloat = 16
    static let buttonCornerRadius: CGFloat = 32
    static let rowSpacing: CGFloat = 12
    static let rowPadding: EdgeInsets = .init(top: 16, leading: 16, bottom: 16, trailing: 16)
    static let headerPadding: EdgeInsets = .init(top: 16, leading: 16, bottom: 0, trailing: 16)
    static let circleSize: CGFloat = 20
    static let innerCircleSize: CGFloat = 10
    static let circleStrokeWidth: CGFloat = 2
    static let minSpacerLength: CGFloat = 20
    static let basketOverlayHeight: CGFloat = 96.0
  }
}

extension SwiftUI.View {
  @ViewBuilder
  func presentToContentHeight(for style: DimensionStyle, when styles: [DimensionStyle]) -> some SwiftUI.View {
    modifier(PresentToContentHeight(styles: styles, style: style))
  }
}

// MARK: - PresentToContentHeight

struct PresentToContentHeight: ViewModifier {
  let styles: [DimensionStyle]
  let style: DimensionStyle
  func body(content: Content) -> some SwiftUI.View {
    if styles.contains(style) {
      content
        .presentationDetentToContentHeight
    } else {
      content
    }
  }
}
