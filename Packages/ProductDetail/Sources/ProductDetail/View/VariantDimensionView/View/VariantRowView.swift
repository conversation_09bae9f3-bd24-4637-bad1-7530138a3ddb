import SwiftUI
import UICatalog

// MARK: - VariantRowView

struct VariantRowView: View {
  let variant: ProductDimensionsVariant
  let onSelect: (String, Bool) -> Void

  var body: some View {
    HStack(spacing: UILayoutConstants.VariantSelectionView.rowSpacing) {
      SelectionIndicatorView(
        isSelected: variant.isSelected
      )
      variantName
      variantInfo
      Spacer()
      variantPrice
    }
    .accessibilityElement(children: .combine)
    .accessibilityAddTraits(variant.isSelected ? .isSelected : .isButton)
    .padding(UILayoutConstants.VariantSelectionView.rowPadding)
    .contentShape(Rectangle())
    .onTapGesture {
      onSelect(variant.productId, variant.isAvailable)
    }
  }

  private var variantName: some View {
    Text(variant.name)
      .font(for: OGFonts.copyMRegular)
      .foregroundColor(OGColors.textOnLight.color)
  }

  private var variantInfo: some View {
    Text(variant.availabilityInfo)
      .font(for: OGFonts.titleS)
      .foregroundColor(Color(variant.availabilityColorName))
  }

  private var variantPrice: some View {
    Text(variant.formattedPrice)
      .font(for: OGFonts.priceSEmphasized)
      .foregroundColor(variant.hasDiscount ? OGColors.textSale.color : OGColors.textOnLight.color)
  }
}
