import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

struct NestedVariantListView: SwiftUI.View {
  @ObservedObject var viewStore: VariantSelectionView.Store

  var body: some SwiftUI.View {
    ScrollView {
      VStack(alignment: .leading) {
        dimensionTitle
        nestedDimensionsContent
        Text(ogL10n.ProductDetail.Variant.PriceDisclaimer)
          .font(for: OGFonts.copyS)
          .foregroundColor(OGColors.textOnLight.color)
          .multilineTextAlignment(.leading)
          .padding(UILayoutConstants.Default.padding2x)
          .frame(maxWidth: .infinity, alignment: .leading)
      }
    }
  }

  private var dimensionTitle: some SwiftUI.View {
    Text(viewStore.dimensionName)
      .font(for: .titleS)
      .foregroundColor(OGColors.textOnLight.color)
      .padding(UILayoutConstants.VariantSelectionView.rowPadding)
      .accessibilityAddTraits(.isHeader)
  }

  @ViewBuilder private var nestedDimensionsContent: some SwiftUI.View {
    if let nestedDims = viewStore.nestedDimensions, !nestedDims.entries.isEmpty {
      VStack(alignment: .leading) {
        horizontalVariantSelection(for: nestedDims)
        selectedEntryContent
      }
    } else {
      EmptyView()
    }
  }

  private func horizontalVariantSelection(for nestedDims: NestedDimensions) -> some SwiftUI.View {
    HorizontalVariantSelectionView(
      variants: nestedDims.entries,
      selectedEntry: viewStore.selectedNestedDimensionEntry
    ) { entryName in
      Task {
        await viewStore.dispatch(.selectFirstDimensionVariant(entryName.name))
      }
    }
  }

  @ViewBuilder private var selectedEntryContent: some SwiftUI.View {
    if let selectedEntry = viewStore.selectedNestedDimensionEntry {
      VStack(alignment: .leading, spacing: .zero) {
        Text(selectedEntry.productDimension.name)
          .font(for: .titleS)
          .foregroundColor(OGColors.textOnLight.color)
          .padding(UILayoutConstants.VariantSelectionView.rowPadding)
          .accessibilityAddTraits(.isHeader)
        variantList(for: selectedEntry)
      }
    } else {
      EmptyView()
    }
  }

  private func variantList(for selectedEntry: NestedDimension) -> some SwiftUI.View {
    VStack(spacing: .zero) {
      ForEach(selectedEntry.productDimension.entries, id: \.productId) { variant in
        VariantRowView(variant: variant) { productId, available in
          Task {
            await viewStore.dispatch(.selectVariant(productId, available))
          }
        }
        ComponentDivider()
          .padding(.leading, UILayoutConstants.Default.padding2x)
      }
    }
  }
}
