import OGDIService
import <PERSON><PERSON>
import UICatalog

// MARK: - VerticalVariantChipsView

struct VerticalVariantChipsView: View {
  @ObservedObject var viewStore: VariantSelectionView.Store
  @State private var totalHeight = CGFloat.zero
  @Environment(\.screenSize) private var screenSize

  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  var body: some View {
    VStack(alignment: .leading) {
      ScrollView {
        variantList
        Spacer(minLength: UILayoutConstants.VariantSelectionView.minSpacerLength)
      }

      ComponentDivider()
      variantInfo
    }
    .padding(UILayoutConstants.Default.padding2x)
  }

  @ViewBuilder private var variantInfo: some View {
    if let variant = viewStore.variants.first(where: { $0.isSelected }) {
      VStack(alignment: .leading) {
        Text(variant.availabilityInfo)
          .font(for: .titleS)
          .foregroundColor(Color(variant.availabilityColorName))
        Text(variant.formattedPrice)
          .font(for: .priceSEmphasized)
          .foregroundColor(OGColors.textOnLight.color)
      }
    }
  }

  @ViewBuilder private var variantList: some View {
    generateContent(min(screenSize.width, screenSize.height) - UILayoutConstants.Default.padding4x)
  }

  private func generateContent(_ containerWidth: CGFloat) -> some View {
    var width = CGFloat.zero
    var height = CGFloat.zero
    let last = viewStore.variants.last
    return ZStack(alignment: .topLeading) {
      ForEach(viewStore.variants, id: \.self) { item in
        variantCard(variant: item)
          .padding([.horizontal, .vertical], UILayoutConstants.VerticalVariantChipsView.spacing)
          .alignmentGuide(.leading, computeValue: { dimension in
            calculateHorizontalAlignment(dimension: dimension, containerWidth: containerWidth, width: &width, height: &height, isLast: item == last)
          })
          .alignmentGuide(.top, computeValue: { _ in
            calculateVerticalAlignment(height: &height, isLast: item == last)
          })
      }
    }
  }

  @ViewBuilder
  private func variantCard(variant: ProductDimensionsVariant) -> some View {
    VStack(alignment: .leading, spacing: UILayoutConstants.HorizontalVariantSelectionView.textSpacing) {
      Text(variant.name.trimmingCharacters(in: .whitespacesAndNewlines))
        .font(for: OGFonts.copyMRegular)
        .lineLimit(1)
        .foregroundColor(variant.isSelected ? OGColors.textOnDark.color : OGColors.textOnLight.color)
    }
    .padding(UILayoutConstants.HorizontalVariantSelectionView.cardPadding)
    .frame(minHeight: UILayoutConstants.VerticalVariantChipsView.chipHeight)
    .frame(minWidth: UILayoutConstants.VerticalVariantChipsView.chipMinWidth)
    .background(variant.isSelected ? OGColors.navigationBarElementAction.color : OGColors.backgroundBackground10.color)
    .cornerRadius(cornerRadius.full)
    .onTapGesture {
      Task {
        await viewStore.dispatch(.selectVariant(variant.productId, variant.isAvailable))
      }
    }
    .accessibilityAddTraits(variant.isSelected ? .isSelected : .isButton)
  }

  private func calculateHorizontalAlignment(
    dimension: ViewDimensions,
    containerWidth: CGFloat,
    width: inout CGFloat,
    height: inout CGFloat,
    isLast: Bool
  ) -> CGFloat {
    if abs(width - dimension.width) > containerWidth {
      width = 0
      height -= dimension.height
    }
    let result = width
    width = isLast ? 0 : width - dimension.width
    return result
  }

  private func calculateVerticalAlignment(
    height: inout CGFloat,
    isLast: Bool
  ) -> CGFloat {
    let result = height
    height = isLast ? 0 : height
    return result
  }
}

// MARK: - UILayoutConstants.VerticalVariantChipsView

extension UILayoutConstants {
  enum VerticalVariantChipsView {
    static let chipMinWidth: CGFloat = 60
    static let chipHeight: CGFloat = 40
    static let spacing: CGFloat = 4
  }
}
