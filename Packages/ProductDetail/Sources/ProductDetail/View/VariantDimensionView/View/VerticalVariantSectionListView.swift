import OGL10n
import SwiftUI
import UICatalog

// MARK: - VerticalVariantSectionListView

struct VerticalVariantSectionListView: View {
  @ObservedObject var viewStore: VariantSelectionView.Store

  var body: some View {
    if let entries = viewStore.nestedDimensions?.entries {
      ScrollView {
        LazyVStack(spacing: 0, pinnedViews: .sectionHeaders) {
          ForEach(entries, id: \.name) { entry in
            Section(header: sectionHeader(title: entry.name)) {
              variantList(for: entry)
            }
          }
        }
        Text(ogL10n.ProductDetail.Variant.PriceDisclaimer)
          .font(for: OGFonts.copyS)
          .foregroundColor(OGColors.textOnLight.color)
          .multilineTextAlignment(.leading)
          .padding(UILayoutConstants.Default.padding2x)
          .frame(maxWidth: .infinity, alignment: .leading)
      }
      .background(OGColors.backgroundBackground0.color)
    }
  }

  private func sectionHeader(title: String) -> some View {
    Text(title)
      .font(for: OGFonts.titleS)
      .accessibilityAddTraits(.isHeader)
      .foregroundColor(OGColors.textOnLight.color)
      .frame(maxWidth: .infinity, alignment: .leading)
      .padding()
      .background(OGColors.backgroundBackground0.color)
  }

  private func variantList(for entry: NestedDimension) -> some View {
    ForEach(entry.productDimension.entries, id: \.id) { variant in
      VStack(spacing: 0) {
        VariantRowView(variant: variant) { productId, available in
          Task {
            await viewStore.dispatch(.selectVariant(productId, available))
          }
        }
        ComponentDivider()
      }
    }
  }
}
