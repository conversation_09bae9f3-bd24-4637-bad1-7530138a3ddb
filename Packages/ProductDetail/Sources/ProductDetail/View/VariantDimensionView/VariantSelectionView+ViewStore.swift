import Combine
import Foundation
import <PERSON>GAppKitSDK
import OGL10n
import <PERSON><PERSON>outer
import OGTracker
import OGViewStore

extension VariantSelectionView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(variantSelectionModel: VariantSelectionModel, route: OGRoute) -> Store {
    let selectedVariantPublisher = route.publisher
    return Store(
      initialState: ViewState(variantSelectionModel: variantSelectionModel),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(selectedVariantPublisher: selectedVariantPublisher)
    )
  }

  struct ViewState: OGViewState {
    private(set) var variants: [ProductDimensionsVariant]
    private(set) var dimensionStyle: DimensionStyle
    private(set) var nestedDimensions: NestedDimensions?
    private(set) var selectedNestedDimensionEntry: NestedDimension?
    private(set) var dimensionName: String
    private(set) var canAddToBasket: Bool
    private(set) var voucherCustomName: String?
    private(set) var voucherCustomNameMaxLength: Int?
    private(set) var isWishlisted: Bool
    private(set) var productIdForWishlisting: String
    private(set) var notifyMeUrl: String?

    private var userDidSelectVariant: Bool

    var headerTile: String {
      if isVoucher {
        dimensionName
      } else {
        ogL10n.ProductDetail.Variant.Title
      }
    }

    var selectedProductId: String? {
      switch dimensionStyle {
      case .flat, .flatChips, .voucher:
        return ProductDimension(name: dimensionName, entries: variants).selectedProductId
      case .categorized, .nested:
        return nestedDimensions?.selectedProductId
      }
    }

    var isVoucher: Bool {
      dimensionStyle == .voucher
    }

    init(
      variantSelectionModel: VariantSelectionModel,
      userDidSelectVariant: Bool = false
    ) {
      self.isWishlisted = variantSelectionModel.isWishlisted
      self.productIdForWishlisting = variantSelectionModel.productIdForWishlisting
      self.variants = variantSelectionModel.variants
      self.dimensionStyle = variantSelectionModel.dimensionStyle
      self.nestedDimensions = variantSelectionModel.nestedDimensions
      self.dimensionName = variantSelectionModel.nestedDimensions?.name ?? ""
      self.selectedNestedDimensionEntry = variantSelectionModel.nestedDimensions?.selectedEntry
      self.userDidSelectVariant = userDidSelectVariant
      self.canAddToBasket = variants.first(where: { $0.isSelected && $0.isAvailable }) != nil
      self.voucherCustomName = variantSelectionModel.customName
      self.voucherCustomNameMaxLength = variantSelectionModel.customNameMaxLength
      self.notifyMeUrl = variants.first(where: { $0.isSelected })?.notifyMeUrl
    }

    mutating func update(
      variantId: String? = nil,
      canAddToBasket: Bool? = nil,
      firstDimensionVariantName: String? = nil,
      userDidSelectVariant: Bool? = nil,
      voucherCustomName: String? = nil
    ) {
      if let variantId {
        notifyMeUrl = variants.first(where: { $0.productId == variantId })?.notifyMeUrl

        switch dimensionStyle {
        case .flat, .flatChips, .voucher:
          let updatedDimension = ProductDimension(
            name: dimensionName,
            entries: variants
          ).selectingVariant(
            withId: variantId
          )
          variants = updatedDimension.entries
        case .categorized, .nested:
          if var nestedDimensions {
            nestedDimensions = nestedDimensions
              .selectingVariant(
                withId: variantId
              )
            self.nestedDimensions = nestedDimensions
            variants = nestedDimensions.allVariants
            selectedNestedDimensionEntry = nestedDimensions.selectedEntry
          }
        }
      } else if let firstDimensionVariantName {
        selectedNestedDimensionEntry = nestedDimensions?.entries
          .first {
            $0.name == firstDimensionVariantName
          }
      }

      self.userDidSelectVariant = userDidSelectVariant ?? self.userDidSelectVariant
      self.canAddToBasket = canAddToBasket ?? self.canAddToBasket
      self.voucherCustomName = voucherCustomName ?? self.voucherCustomName
    }

    static var initial = ViewState(
      variantSelectionModel: VariantSelectionModel(
        variants: [],
        selectedVariantName: "",
        dimensionStyle: .flat,
        nestedDimensions: nil,
        customName: nil,
        customNameMaxLength: nil,
        isWishlisted: false,
        productIdForWishlisting: ""
      )
    )
  }

  enum Event: OGViewEvent {
    case voucherCustomName(String?)
    case selectVariant(String, Bool)
    case selectFirstDimensionVariant(String)
    case dismiss
    case viewDisappeared
    case trackScreenView
  }
}

extension VariantSelectionView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout VariantSelectionView.ViewState,
      with event: VariantSelectionView.Event
    ) {
      switch event {
      case let .selectVariant(productId, available):
        state.update(variantId: productId, canAddToBasket: available, userDidSelectVariant: true)
      case let .selectFirstDimensionVariant(variantName):
        state.update(firstDimensionVariantName: variantName)
      case let .voucherCustomName(voucherCustomName):
        state.update(voucherCustomName: voucherCustomName)
      case .dismiss, .trackScreenView, .viewDisappeared: break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing
    private let selectedVariantPublisher: CurrentValueSubject<Decodable, Never>?
    private let tracker: OGTrackerProtocol
    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      selectedVariantPublisher: CurrentValueSubject<Decodable, Never>?,
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()
    ) {
      self.router = router
      self.selectedVariantPublisher = selectedVariantPublisher
      self.tracker = tracker
    }

    func callAsFunction(
      event: VariantSelectionView.Event,
      for state: VariantSelectionView.ViewState
    ) async -> VariantSelectionView.Event? {
      switch event {
      case .selectFirstDimensionVariant, .selectVariant:
        return nil
      case .dismiss:
        router.send(.dismiss)
        return nil
      case .viewDisappeared:
        updateSelectedVariant(for: state)
        return nil
      case .trackScreenView:
        tracker.multiplatformTrack(event: View.ScreenProductDetailVariants())
        return nil
      case .voucherCustomName:
        return nil
      }
    }

    private func updateSelectedVariant(for state: VariantSelectionView.ViewState) {
      guard let selectedVariantPublisher,
            let productId = state.selectedProductId else { return }

      selectedVariantPublisher
        .send(
          SelectedVariant(
            productId: productId,
            userDidChangeVariant: state.userDidSelectVariant,
            isVoucher: state.isVoucher,
            customName: state.voucherCustomName
          )
        )
    }
  }
}
