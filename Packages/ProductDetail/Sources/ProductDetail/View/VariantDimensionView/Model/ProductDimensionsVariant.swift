import Foundation
import OGAppKitSDK

// MARK: - ProductDimensionsVariant

struct ProductDimensionsVariant: Identifiable, Equatable, Codable, Hashable {
  let id: String
  let name: String
  let productId: String
  let isSelected: Bool
  let formattedPrice: String
  let availabilityColorName: String
  let availabilityInfo: String
  let isAvailable: Bool
  let hasDiscount: Bool
  let notifyMeUrl: String?

  static func from(
    _ variant: ProductDimensions.ProductDimensionVariantLink,
    priceFormatter: PriceFormattable
  ) -> ProductDimensionsVariant {
    ProductDimensionsVariant(
      id: variant.productId,
      name: variant.name,
      productId: variant.productId,
      isSelected: variant.isSelected,
      formattedPrice: priceFormatter.format(Int(variant.price.value), currencyCode: variant.price.currency),
      availabilityColorName: variant.availability.state.colorName,
      availabilityInfo: variant.availability.availabilityInfo,
      isAvailable: variant.availability.state.isAvailable,
      hasDiscount: variant.price.oldValue != nil,
      notifyMeUrl: variant.availability.notifyMeUrl
    )
  }

  func selecting() -> ProductDimensionsVariant {
    select(isSelected: true)
  }

  func deselecting() -> ProductDimensionsVariant {
    select(isSelected: false)
  }

  private func select(isSelected: Bool) -> ProductDimensionsVariant {
    ProductDimensionsVariant(
      id: id,
      name: name,
      productId: productId,
      isSelected: isSelected,
      formattedPrice: formattedPrice,
      availabilityColorName: availabilityColorName,
      availabilityInfo: availabilityInfo,
      isAvailable: isAvailable,
      hasDiscount: hasDiscount,
      notifyMeUrl: notifyMeUrl
    )
  }
}
