import OGAppKitSDK

extension ProductDimensionsContent {
  func toVariantSelectionModel(
    style: DimensionStyle,
    priceFormatter: PriceFormattable,
    customName: String? = nil
  ) -> VariantSelectionModel? {
    if let flatDimension = self as? ProductDimensions.FlatDimension {
      return updateWithFlatDimension(
        flatDimension,
        priceFormatter: priceFormatter,
        style: style,
        isWishlisted: isWishlisted,
        productIdForWishlisting: productIdForWishlisting
      )
    } else if let nestedDimensions = self as? ProductDimensions.NestedDimensions {
      return updateWithNestedDimensions(
        nestedDimensions,
        priceFormatter: priceFormatter,
        style: style,
        isWishlisted: isWishlisted,
        productIdForWishlisting: productIdForWishlisting
      )
    } else if let voucherDimension = self as? ProductDimensions.VoucherDimension {
      return updateWithVoucherDimension(
        voucherDimension,
        priceFormatter: priceFormatter,
        style: style,
        customName: customName,
        isWishlisted: isWishlisted,
        productIdForWishlisting: productIdForWishlisting
      )
    } else {
      return nil
    }
  }

  var selectedID: String? {
    if let flatDimension = self as? ProductDimensions.FlatDimension {
      return flatDimension.dimension.variants.first(where: { $0.isSelected })?.productId
    } else if let nestedDimensions = self as? ProductDimensions.NestedDimensions {
      return nestedDimensions.entries.compactMap { $0.dimension.variants.first(where: { $0.isSelected }) }.first?.productId
    } else if let voucherDimension = self as? ProductDimensions.VoucherDimension {
      return voucherDimension.dimension.variants.first(where: { $0.isSelected })?.productId
    } else {
      return nil
    }
  }

  var selectedName: String? {
    if let flatDimension = self as? ProductDimensions.FlatDimension {
      return flatDimension.dimension.variants.first(where: { $0.isSelected })?.name
    } else if let nestedDimensions = self as? ProductDimensions.NestedDimensions {
      return nestedDimensions.entries.compactMap { $0.dimension.variants.first(where: { $0.isSelected }) }.first?.name
    } else if let voucherDimension = self as? ProductDimensions.VoucherDimension {
      return voucherDimension.dimension.variants.first(where: { $0.isSelected })?.name
    } else {
      return nil
    }
  }

  private func updateWithVoucherDimension(
    _ voucherDimension: ProductDimensions.VoucherDimension,
    priceFormatter: PriceFormattable,
    style: DimensionStyle,
    customName: String?,
    isWishlisted: Bool,
    productIdForWishlisting: String
  ) -> VariantSelectionModel {
    let productDimension = ProductDimension.from(voucherDimension.dimension, priceFormatter: priceFormatter)
    let customNameMaxLength = Int(voucherDimension.customName?.maxLength ?? 0)
    return VariantSelectionModel(
      variants: productDimension.entries,
      selectedVariantName: productDimension.selectedVariantName,
      dimensionStyle: style,
      nestedDimensions: NestedDimensions(name: productDimension.name, entries: []),
      customName: customName,
      customNameMaxLength: customNameMaxLength,
      isWishlisted: isWishlisted,
      productIdForWishlisting: productIdForWishlisting
    )
  }

  private func updateWithFlatDimension(
    _ flatDimension: ProductDimensions.FlatDimension,
    priceFormatter: PriceFormattable,
    style: DimensionStyle,
    isWishlisted: Bool,
    productIdForWishlisting: String
  ) -> VariantSelectionModel {
    let productDimension = ProductDimension.from(flatDimension.dimension, priceFormatter: priceFormatter)
    return VariantSelectionModel(
      variants: productDimension.entries,
      selectedVariantName: productDimension.selectedVariantName,
      dimensionStyle: style,
      nestedDimensions: NestedDimensions(name: productDimension.name, entries: []),
      customName: nil,
      customNameMaxLength: nil,
      isWishlisted: isWishlisted,
      productIdForWishlisting: productIdForWishlisting
    )
  }

  private func updateWithNestedDimensions(
    _ nestedDimensions: ProductDimensions.NestedDimensions,
    priceFormatter: PriceFormattable,
    style: DimensionStyle,
    isWishlisted: Bool,
    productIdForWishlisting: String
  ) -> VariantSelectionModel {
    let newNestedDimensions = NestedDimensions.from(nestedDimensions, priceFormatter: priceFormatter)
    return VariantSelectionModel(
      variants: newNestedDimensions.allVariants,
      selectedVariantName: newNestedDimensions.selectedVariantName,
      dimensionStyle: style,
      nestedDimensions: newNestedDimensions,
      customName: nil,
      customNameMaxLength: nil,
      isWishlisted: isWishlisted,
      productIdForWishlisting: productIdForWishlisting
    )
  }
}
