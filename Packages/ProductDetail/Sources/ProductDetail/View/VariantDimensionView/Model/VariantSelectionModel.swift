import Foundation
import OGAppKitSDK

// MARK: - VariantSelectionModel

struct VariantSelectionModel: Codable, Equatable {
  let variants: [ProductDimensionsVariant]
  let selectedVariantName: String
  let dimensionStyle: DimensionStyle
  let nestedDimensions: NestedDimensions?
  let customName: String?
  let customNameMaxLength: Int?
  let isWishlisted: Bool
  let productIdForWishlisting: String
}

extension VariantSelectionModel {
  func with(
    variants: [ProductDimensionsVariant],
    customName: String?,
    customNameMaxLength: Int?,
    isWishlisted: Bool,
    productIdForWishlisting: String,
    notifyMeUrl: String?
  ) -> VariantSelectionModel {
    VariantSelectionModel(
      variants: variants,
      selectedVariantName: selectedVariantName,
      dimensionStyle: dimensionStyle,
      nestedDimensions: nestedDimensions,
      customName: customName,
      customNameMaxLength: customNameMaxLength,
      isWishlisted: isWishlisted,
      productIdForWishlisting: productIdForWishlisting
    )
  }

  func with(
    nestedDimensions: NestedDimensions?,
    and variants: [ProductDimensionsVariant],
    isWishlisted: Bool,
    productIdForWishlisting: String,
    notifyMeUrl: String?
  ) -> VariantSelectionModel {
    VariantSelectionModel(
      variants: variants,
      selectedVariantName: selectedVariantName,
      dimensionStyle: dimensionStyle,
      nestedDimensions: nestedDimensions,
      customName: nil,
      customNameMaxLength: nil,
      isWishlisted: isWishlisted,
      productIdForWishlisting: productIdForWishlisting
    )
  }

  func with(
    selectedVariantName: String,
    customName: String?,
    customNameMaxLength: Int?,
    isWishlisted: Bool,
    productIdForWishlisting: String,
    notifyMeUrl: String?
  ) -> VariantSelectionModel {
    VariantSelectionModel(
      variants: variants,
      selectedVariantName: selectedVariantName,
      dimensionStyle: dimensionStyle,
      nestedDimensions: nestedDimensions,
      customName: customName,
      customNameMaxLength: customNameMaxLength,
      isWishlisted: isWishlisted,
      productIdForWishlisting: productIdForWishlisting
    )
  }
}

// MARK: - DimensionStyle

enum DimensionStyle: Codable {
  case flat
  case flatChips
  case nested
  case categorized
  case voucher
}

extension ProductDimensions.ConfigStyle {
  var toDimensionStyle: DimensionStyle {
    switch self {
    case .flat:
      return .flat
    case .nested:
      return .nested
    case .categorized:
      return .categorized
    case .flatchips:
      return .flatChips
    }
  }
}
