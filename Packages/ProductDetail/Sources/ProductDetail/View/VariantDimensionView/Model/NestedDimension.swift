import Foundation
import OGAppKitSDK

// MARK: - NestedDimension

struct NestedDimension: Equatable, Codable {
  let name: String
  let productDimension: ProductDimension
  var isSelected: Bool {
    productDimension.entries.contains { $0.isSelected }
  }

  var isAvailable: Bool {
    productDimension.entries.contains { $0.isAvailable }
  }

  func selectingVariant(withId productId: String) -> NestedDimension {
    NestedDimension(
      name: name,
      productDimension: productDimension.selectingVariant(withId: productId)
    )
  }

  static func from(
    _ entry: ProductDimensions.NestedDimensionsEntry,
    priceFormatter: PriceFormattable
  ) -> NestedDimension {
    NestedDimension(
      name: entry.name,
      productDimension: ProductDimension.from(entry.dimension, priceFormatter: priceFormatter)
    )
  }
}
