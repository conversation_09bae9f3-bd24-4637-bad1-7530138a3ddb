import Foundation
import OGAppKitSDK

// MARK: - NestedDimensions

struct NestedDimensions: Codable, Equatable {
  let name: String
  let entries: [NestedDimension]

  var titleName: String {
    if let firstEntry = entries.first {
      return "\(firstEntry.productDimension.name), \(name)"
    }
    return name
  }

  var selectedProductId: String? {
    entries.compactMap(\.productDimension.selectedProductId).first
  }

  var selectedVariantName: String {
    if let selectedEntry = entries.first(
      where: { entry in
        entry.productDimension.entries
          .contains(
            where: \.isSelected
          )
      }),
      let selectedVariant = selectedEntry.productDimension.entries.first(where: { $0.isSelected }) {
      return "\(selectedVariant.name)\(selectedEntry.name)"
    }
    return ""
  }

  var selectedEntry: NestedDimension? {
    entries.first { entry in
      entry.productDimension.entries.contains { $0.isSelected }
    }
  }

  var allVariants: [ProductDimensionsVariant] {
    entries.flatMap(\.productDimension.entries)
  }

  func selectingVariant(withId productId: String) -> NestedDimensions {
    NestedDimensions(
      name: name,
      entries: entries.map { $0.selectingVariant(withId: productId) }
    )
  }

  static func from(_ nestedDimensions: ProductDimensions.NestedDimensions, priceFormatter: PriceFormattable) -> NestedDimensions {
    NestedDimensions(
      name: nestedDimensions.name,
      entries: nestedDimensions.entries.map { NestedDimension.from($0, priceFormatter: priceFormatter) }
    )
  }
}
