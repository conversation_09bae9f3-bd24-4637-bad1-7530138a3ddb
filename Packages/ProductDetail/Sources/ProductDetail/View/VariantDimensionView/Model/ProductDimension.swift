import Foundation
import OGAppKitSDK

struct ProductDimension: Codable, Equatable {
  let name: String
  let entries: [ProductDimensionsVariant]

  var selectedProductId: String? {
    entries.first(where: { $0.isSelected })?.productId
  }

  var selectedVariantName: String {
    entries.first(where: { $0.isSelected })?.name ?? ""
  }

  func selectingVariant(withId productId: String) -> ProductDimension {
    ProductDimension(
      name: name,
      entries: entries.map { variant in
        variant.productId == productId ? variant.selecting() : variant.deselecting()
      }
    )
  }

  static func from(_ dimension: ProductDimensions.ProductDimension, priceFormatter: PriceFormattable) -> ProductDimension {
    ProductDimension(
      name: dimension.name,
      entries: dimension.variants.map { ProductDimensionsVariant.from($0, priceFormatter: priceFormatter) }
    )
  }
}
