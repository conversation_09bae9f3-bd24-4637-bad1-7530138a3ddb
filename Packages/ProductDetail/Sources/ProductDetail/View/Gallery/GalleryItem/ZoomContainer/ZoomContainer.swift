import SwiftUI
import UICatalog

struct ZoomContainer<Content: View>: View {
  private let content: Content
  private let maxAllowedScale: CGFloat = 2.0
  private let minAllowedScale: CGFloat = 1.0
  @Binding private var currentScale: CGFloat
  @State private var tapLocation: CGPoint = .zero
  init(
    scale: Binding<CGFloat>,
    @ViewBuilder content: () -> Content
  ) {
    _currentScale = scale
    self.content = content()
  }

  var body: some View {
    if #available(iOS 16.0, *) {
      ZoomScrollViewRepresentation(
        scale: $currentScale,
        tapLocation: $tapLocation,
        maxAllowedScale: maxAllowedScale,
        minAllowedScale: minAllowedScale
      ) {
        content
          .background(OGColors.backgroundBackground10.color)
          .frame(minWidth: 0, maxWidth: .infinity, minHeight: 0, maxHeight: .infinity, alignment: .center)
          .ignoresSafeArea()
      }
      .onTapGesture(count: 2, perform: { location in
        tapLocation = location
        currentScale = currentScale == minAllowedScale ? maxAllowedScale : minAllowedScale
      })
    } else {
      ZoomScrollViewRepresentation(
        scale: $currentScale,
        tapLocation: $tapLocation,
        maxAllowedScale: maxAllowedScale,
        minAllowedScale: minAllowedScale
      ) {
        content
          .background(OGColors.backgroundBackground10.color)
          .frame(minWidth: 0, maxWidth: .infinity, minHeight: 0, maxHeight: .infinity, alignment: .center)
          .ignoresSafeArea()
      }
      .onTapGesture(count: 2, perform: {
        currentScale = currentScale == minAllowedScale ? maxAllowedScale : minAllowedScale
      })
      .gesture(DragGesture(minimumDistance: 0).onEnded { value in
        tapLocation = value.location
      })
      .onDisappear {
        currentScale = minAllowedScale
      }
    }
  }
}
