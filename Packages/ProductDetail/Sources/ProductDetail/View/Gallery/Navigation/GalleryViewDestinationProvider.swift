import OGAppKitSDK
import OGCore
import OGRouter
import OGWebView
import Swift<PERSON>
import UICatalog

// MARK: - GalleryViewDestinationProvider

public struct GalleryViewDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.galleryView
  }

  public func provide(_ route: OGRoute) -> some SwiftUI.View {
    if let model: GalleryView.ViewState.Gallery = route.getData() {
      let appKitImages = model.images.map { OGAppKitSDK.Image(url: $0, thumbnailUrl: nil) }
      let gallery = ProductGallery(state: LoadingComponentStateDone(content: ProductGallery.Content(
        images: appKitImages,
        flags: [],
        isWishlisted: false,
        productIdForWishlisting: ""
      )))
      GalleryView(
        gallery: gallery,
        isFullscreen: true,
        indexOfCurrentImage: model.selectedIndex,
        route: route
      )
      .accessibilityAddTraits(.isModal)
    } else {
      EmptyView()
    }
  }

  public func presentationType() -> OGPresentationType {
    .overlay
  }
}

extension OGIdentifier {
  public static let galleryView = #identifier("galleryView")
}

extension OGRoute {
  public static let galleryView = OGRoute(OGIdentifier.galleryView.value)
}
