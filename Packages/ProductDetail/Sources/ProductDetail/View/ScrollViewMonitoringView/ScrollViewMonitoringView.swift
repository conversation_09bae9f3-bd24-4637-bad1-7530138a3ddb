import SwiftUI

// MARK: - ScrollViewMonitoringView

public struct ScrollViewMonitoringView<Content: View, ID: Hashable>: View {
  @ViewBuilder private let content: Content
  private let axes: Axis.Set
  private let showsIndicators: Bool
  @State private var visibilityManager: ViewVisibilityManager<ID>

  public init(
    _ axes: Axis.Set = .vertical,
    showsIndicators: Bool = true,
    onVisibilityChange: @escaping ViewVisibilityManager<ID>.VisibilityChangeAction,
    @ViewBuilder content: () -> Content
  ) {
    self.axes = axes
    self.showsIndicators = showsIndicators
    self.content = content()
    _visibilityManager = .init(initialValue: ViewVisibilityManager<ID>(axis: axes, onVisibilityChange: onVisibilityChange))
  }

  public var body: some View {
    ScrollView(axes, showsIndicators: showsIndicators) {
      content
        .environmentObject(visibilityManager)
    }
    .background(
      GeometryReader { geometry in
        updateContainerBounds(geometry: geometry)
      }
    )
  }

  private func updateContainerBounds(geometry: GeometryProxy) -> Color {
    visibilityManager.updateContainerBounds(bounds: geometry.frame(in: .global))
    return Color.clear
  }
}

// MARK: - ScrollAction

public enum ScrollAction {
  case scrollUp(offset: CGFloat)
  case scrollDown(offset: CGFloat)
  case scrollLeft(offset: CGFloat)
  case scrollRight(offset: CGFloat)
  case adjustLeftBy(width: CGFloat)
  case adjustRightBy(width: CGFloat)
  case adjustUpBy(width: CGFloat)
  case adjustDownBy(width: CGFloat)
  case none
}

import Combine

// MARK: - ScrollViewMonitoringScreenScrollView

@available(iOS 18.0, *)
public struct ScrollViewMonitoringScreenScrollView<Content: View, ID: Hashable>: View {
  @ViewBuilder private let content: Content
  private let axes: Axis.Set
  private let showsIndicators: Bool

  @State private var visibilityManager: ViewVisibilityManager<ID>
  @State private var position = ScrollPosition(edge: .top)
  @State private var bounds: CGRect = .zero
  @State private var origin: CGPoint = .zero

  private let scrollDirectionAction: PassthroughSubject<ScrollAction, Never>

  public init(
    _ axes: Axis.Set = .vertical,
    showsIndicators: Bool = true,
    scrollDirectionAction: PassthroughSubject<ScrollAction, Never>,
    onVisibilityChange: @escaping ViewVisibilityManager<ID>.VisibilityChangeAction,
    @ViewBuilder content: () -> Content
  ) {
    self.axes = axes
    self.showsIndicators = showsIndicators
    self.content = content()
    _visibilityManager = .init(initialValue: ViewVisibilityManager<ID>(axis: axes, onVisibilityChange: onVisibilityChange))
    self.scrollDirectionAction = scrollDirectionAction
  }

  public var body: some View {
    ScrollView(axes, showsIndicators: showsIndicators) {
      content
        .environmentObject(visibilityManager)
        .onGeometryChange(for: CGPoint.self) { geometry in
          geometry.frame(in: .named("scroll")).origin
        } action: { newValue in
          origin = newValue
        }
    }
    .coordinateSpace(name: "scroll")
    .scrollPosition($position)
    .animation(.default, value: position)
    .onGeometryChange(for: CGRect.self) { geometry in
      geometry.frame(in: .global)
    } action: { newValue in
      bounds = newValue
      visibilityManager.updateContainerBounds(bounds: bounds)
    }
    .onReceive(scrollDirectionAction) { action in
      switch action {
      case let .scrollUp(offset):
        position = ScrollPosition(y: abs(origin.y) - (bounds.height - offset))
      case let .scrollDown(offset):
        position = ScrollPosition(y: abs(origin.y) + (bounds.height - offset))
      case let .scrollLeft(offset):
        position = ScrollPosition(x: abs(origin.x) - (bounds.width - offset))
      case let .scrollRight(offset):
        position = ScrollPosition(x: abs(origin.x) + (bounds.width - offset))
      case let .adjustLeftBy(width):
        position = ScrollPosition(x: abs(origin.x) - width)
      case let .adjustRightBy(width):
        position = ScrollPosition(x: abs(origin.x) + width)
      case let .adjustUpBy(height):
        position = ScrollPosition(y: abs(origin.y) - height)
      case let .adjustDownBy(height):
        position = ScrollPosition(y: abs(origin.y) + height)
      case .none:
        break
      }
    }
  }
}
