import SwiftUI

// MARK: - ViewVisibilityMonitoringModifier

public struct ViewVisibilityMonitoringModifier<ID: Hashable>: ViewModifier {
  @EnvironmentObject var visibilityManager: ViewVisibilityManager<ID>
  public let viewID: ID

  public func body(content: Content) -> some View {
    content
      .id(viewID)
      .background(
        GeometryReader { geometry in
          updateViewBounds(geometry: geometry)
        }
      )
  }

  private func updateViewBounds(geometry: GeometryProxy) -> Color {
    visibilityManager.updateViewBounds(bounds: geometry.frame(in: .global), id: viewID)
    return Color.clear
  }
}

extension View {
  public func monitorVisibility(withID id: some Hashable) -> some View {
    modifier(ViewVisibilityMonitoringModifier(viewID: id))
  }
}
