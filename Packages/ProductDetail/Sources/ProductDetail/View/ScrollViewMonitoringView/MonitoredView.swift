enum MonitoredView: Has<PERSON>le {
  case galleryWishlistIcon
  case productAvailability
  case productColorDimension
  case productDimensions
  case productGallery
  case productHeader
  case productInformation
  case productPrice
  case productRating
  case productTitle
  case addToBasketButton
  case productColor
  case productReviews
  case recentlyViewedRecommendations
  case staticProductRecommendations
  case dynamicYieldRecommendations
  case shopTheLookRecommendations
  case shopUsps
  case productVariant
  case dynamicYieldBanner
  case moreFromTheSeriesRecommendations
}
