import SwiftUI

// MARK: - ViewVisibilityManager

public class ViewVisibilityManager<ID: Hashable>: ObservableObject {
  private var containerBounds: CGRect = .zero
  private let axis: Axis.Set

  var onVisibilityChange: VisibilityChangeAction
  public typealias VisibilityChangeAction = (ID, ItemVisibility) -> Void

  init(axis: Axis.Set = .vertical, onVisibilityChange: @escaping VisibilityChangeAction) {
    self.onVisibilityChange = onVisibilityChange
    self.axis = axis
  }

  func updateContainerBounds(bounds: CGRect) {
    containerBounds = bounds
  }

  func updateViewBounds(bounds: CGRect, id: ID) {
    let isHorizontallyContained = bounds.minX >= containerBounds.minX && bounds.maxX <= containerBounds.maxX
    let isVerticallyContained = bounds.minY >= containerBounds.minY && bounds.maxY <= containerBounds.maxY
    let fullyVisible: Bool = axis == .vertical ? isVerticallyContained : isHorizontallyContained
    let partiallyVisible: Bool = bounds.intersects(containerBounds)

    var itemVisibility: ItemVisibility {
      if fullyVisible {
        .fully
      } else if partiallyVisible {
        .partially
      } else {
        .not
      }
    }
    onVisibilityChange(id, itemVisibility)
  }
}

// MARK: - ItemVisibility

public enum ItemVisibility: Equatable {
  case fully
  case partially
  case not
}
