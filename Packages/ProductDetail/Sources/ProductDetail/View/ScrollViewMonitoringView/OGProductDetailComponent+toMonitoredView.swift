import OGAppKitSDK

// MARK: - ProductDetailView.ViewState

extension OGProductDetailComponent {
  var toMonitoredView: MonitoredView {
    switch self {
    case .productAvailability:
      .productAvailability
    case .productColorDimension:
      .productColorDimension
    case .productDimensions:
      .productDimensions
    case .productGallery:
      .productGallery
    case .productHeader:
      .productHeader
    case .productInformation:
      .productInformation
    case .productPrice:
      .productPrice
    case .productRating:
      .productRating
    case .productTitle:
      .productTitle
    case .addToBasketButton:
      .addToBasketButton
    case .productColor:
      .productColor
    case .productReviews:
      .productReviews
    case .recentlyViewedRecommendations:
      .recentlyViewedRecommendations
    case .staticProductRecommendations:
      .staticProductRecommendations
    case .dynamicYieldRecommendations:
      .dynamicYieldRecommendations
    case .shopTheLookRecommendations:
      .shopTheLookRecommendations
    case .shopUsps:
      .shopUsps
    case .productVariant:
      .productVariant
    case .dynamicYieldBanner:
      .dynamicYieldBanner
    case .moreFromTheSeriesRecommendations:
      .moreFromTheSeriesRecommendations
    }
  }
}
