import AppTrackerTestsUtils
import Foundation
import OGAppTestsUtils
import <PERSON>GCore
import OGNavigationCoreTestsUtils
import OGTrackerOptInServiceTestsUtils
import XCTest

@testable import AppTracker

final class AppTrackerStoreMiddlewareTests: XCTestCase {
  let tracker = OGAppTrackerMock()

  func test_WHEN_didTapTab_THEN_noNextAction_AND_trackerCalled() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackDidTapTab(.assortmentStub!), for: .initial)

    XCTAssertEqual(tracker.mock.trackDidTapCalls.latestCall, .assortmentStub)
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_trackATTDidAppear_THEN_ATTDidAppear_tracked() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackATTDidAppear, for: .initial)

    XCTAssertEqual(tracker.mock.trackATTDidAppearCalls.callsCount, 1)
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_trackDidGoBack_THEN_Back_tracked() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackDidGoBack, for: .initial)

    XCTAssertEqual(tracker.mock.trackDidGoBackCalls.callsCount, 1)
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_trackIsLoggedIn_THEN_UserLoggedIn_tracked() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackIsLoggedIn(true), for: .initial)

    XCTAssertEqual(tracker.mock.trackIsLoggedInCalls.callsCount, 1)
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_attStatus_authorized_WHEN_trackATTStatus_THEN_AttSystemDialogConfirm_tracked() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackATTStatus(.authorized), for: .initial)

    XCTAssertEqual(tracker.mock.trackATTStatusCalls.callsCount, 1)
    XCTAssertEqual(tracker.mock.trackATTStatusCalls.latestCall, .authorized)
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_attStatus_denied_WHEN_trackATTStatus_THEN_AttSystemDialogDecline_tracked() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackATTStatus(.denied), for: .initial)

    XCTAssertEqual(tracker.mock.trackATTStatusCalls.callsCount, 1)
    XCTAssertEqual(tracker.mock.trackATTStatusCalls.latestCall, .denied)
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_trackInboxButtonDidTap_THEN_OpenInboxHome_tracked() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackInboxButtonDidTap, for: .initial)

    XCTAssertEqual(tracker.mock.trackInboxButtonDidTapCalls.callsCount, 1)
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_didReloadTab_THEN_noNextAction_AND_trackerCalled() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackDidReloadTab(.homeStub!), for: .initial)

    XCTAssertEqual(tracker.mock.trackDidReloadCalls.callsCount, 1)
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_trackShareProductDidTap_THEN_noNextAction_AND_trackerCalled() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackShareProductDidTap, for: .initial)

    XCTAssertEqual(tracker.mock.trackShareProductDidTapCalls.callsCount, 1)
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_trackSearchFieldDidTap_THEN_noNextAction_AND_trackerCalled() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackSearchFieldDidTap, for: .initial)

    XCTAssertEqual(tracker.mock.trackSearchFieldDidTapCalls.callsCount, 1)
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_trackSearchButtonDidTap_THEN_noNextAction_AND_trackerCalled() async {
    let sut = AppTrackerState.Middleware(tracker: tracker)
    let nextEvent = await sut.callAsFunction(action: ._trackSearchButtonDidTap, for: .initial)

    XCTAssertEqual(tracker.mock.trackSearchButtonDidTapCalls.callsCount, 1)
    XCTAssertNil(nextEvent)
  }
}
