import AppCore
import AppCoreTestsUtils
import Foundation
import OGAppTestsUtils
import OGCore
import OGCoreTestsUtils
import OGNavigationCore
import OGNavigationCoreTestsUtils
import OGTrackerOptInService
import OGTrackerOptInServiceTestsUtils
import XCTest

@testable import AppTracker

class AppTrackerStoreConnectorTests: XCTestCase {
  override func setUpWithError() throws {
    try super.setUpWithError()

    OGNavigationCoreContainer.shared.tabTap.register {
      OGTabTapMock()
    }

    OGNavigationCoreContainer.shared.backTap.register {
      OGBackTapMock()
    }

    OGTrackerOptInContainer.shared.atTrackingStore.register {
      OGATTrackingStore.makeMock()
    }

    AppContainer.shared.appStore.register {
      AppStoreMock()
    }
  }

  override func tearDownWithError() throws {
    OGNavigationCoreContainer.shared.tabTap.reset()
    OGNavigationCoreContainer.shared.backTap.reset()
    OGTrackerOptInContainer.shared.atTrackingStore.reset()
    AppContainer.shared.appStore.reset()
    try super.tearDownWithError()
  }

  func test_WHEN_configure_THEN_updateAppUpdateVisibilityAndsetRequestATT() async {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected set event to be received")
      let sut = AppTrackerState.Connector()

      var actualEvents: [AppTrackerAction] = []

      let dispatch: (AppTrackerAction) -> Void = { event in
        actualEvents.append(event)

        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)
      await fulfillment(of: [expectation], timeout: 0.1)

      let expected: [AppTrackerAction] = [
        ._trackIsLoggedIn(false)
      ]
      XCTAssertEqual(expected, actualEvents)
    }
  }
}
