import AppTrackingTransparency
import Foundation
import OGAppKitSDK
import OGNavigation
import OGSystemKit
import OGTracker
import UserNotifications

// MARK: - OGAppViewTracking

public protocol OGAppViewTracking {
  func trackATTDidAppear()
}

// MARK: - OGAppUserPropertyTracking

public protocol OGAppUserPropertyTracking {
  func trackPushNotificationPermission()
}

// MARK: - OGAppInteractionTracking

public protocol OGAppInteractionTracking {
  func trackATTStatus(_ status: ATTrackingManager.AuthorizationStatus)
  func trackDidTap(tab: OGTab)
  func trackDidReload(tab: OGTab)
  func trackDidGoBack()
  func trackIsLoggedIn(_ loggedIn: Bool)
  func trackInboxButtonDidTap()
  func trackShareProductDidTap()
  func trackSearchButtonDidTap()
  func trackSearchFieldDidTap()
}

// MARK: - ScreenOrigin

public enum ScreenOrigin: String, Sendable {
  case home
  case shop
  case assortment
  case profile
  case account
  case productList
  case basket
  case cart
}

// MARK: - OGAppTracking

public protocol OGAppTracking: OGAppViewTracking, OGAppInteractionTracking, OGAppUserPropertyTracking {}

// MARK: - OGAppTracker

public struct OGAppTracker: OGAppTracking {
  private let tracker: OGTrackerProtocol
  @Injected(\OGSystemKitContainer.userNotificationCenter) private var userNotificationCenter

  public init(tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()) {
    self.tracker = tracker
  }

  // MARK: - OGAppInteractionTracking

  public func trackDidTap(tab: OGTab) {
    let home = InteractionEvent.HomeBottomNavigationEntry()
    let assortment = InteractionEvent.AssortmentBottomNavigationEntry()
    let wishlist = InteractionEvent.WishlistBottomNavigationEntry()
    let cart = InteractionEvent.CartBottomNavigationEntry()
    let account = InteractionEvent.AccountBottomNavigationEntry()

    switch tab.rawValue.lowercased() {
    case home.label?.lowercased(), ScreenOrigin.shop.rawValue:
      tracker.multiplatformTrack(event: home)

    case assortment.label?.lowercased():
      tracker.multiplatformTrack(event: assortment)

    case wishlist.label?.lowercased():
      tracker.multiplatformTrack(event: wishlist)

    case cart.label?.lowercased(), ScreenOrigin.basket.rawValue:
      tracker.multiplatformTrack(event: cart)

    case account.label?.lowercased(), ScreenOrigin.profile.rawValue:
      tracker.multiplatformTrack(event: account)

    default: break
    }
  }

  public func trackDidReload(tab: OGTab) {
    let home = InteractionEvent.ResetHomeTab()
    let assortment = InteractionEvent.ResetAssortmentTab()
    let wishlist = InteractionEvent.ResetWishlistTab()
    let cart = InteractionEvent.ResetCartTab()
    let account = InteractionEvent.ResetAccountTab()

    switch tab.rawValue.lowercased() {
    case home.label?.lowercased(), ScreenOrigin.shop.rawValue:
      tracker.multiplatformTrack(event: home)

    case assortment.label?.lowercased():
      tracker.multiplatformTrack(event: assortment)

    case wishlist.label?.lowercased():
      tracker.multiplatformTrack(event: wishlist)

    case cart.label?.lowercased(), ScreenOrigin.basket.rawValue:
      tracker.multiplatformTrack(event: cart)

    case account.label?.lowercased(), ScreenOrigin.profile.rawValue:
      tracker.multiplatformTrack(event: account)

    default: break
    }
  }

  public func trackATTStatus(_ status: ATTrackingManager.AuthorizationStatus) {
    switch status {
    case .authorized:
      tracker.multiplatformTrack(event: InteractionEvent.AttSystemDialogConfirm())

    case .denied, .notDetermined:
      tracker.multiplatformTrack(event: InteractionEvent.AttSystemDialogDecline())

    default: break
    }
  }

  public func trackDidGoBack() {
    tracker.multiplatformTrack(event: InteractionEvent.Back())
  }

  public func trackIsLoggedIn(_ loggedIn: Bool) {
    let event: TrackingEvent = loggedIn ? InteractionEvent.LoggedIn() : InteractionEvent.LoggedOut()
    tracker.multiplatformTrack(event: event)
  }

  public func trackInboxButtonDidTap() {
    tracker.multiplatformTrack(event: InteractionEvent.OpenInboxHome())
  }

  public func trackShareProductDidTap() {
    tracker.multiplatformTrack(event: InteractionEvent.ShareProduct())
  }

  public func trackSearchButtonDidTap() {
    tracker.multiplatformTrack(event: InteractionEvent.OpenSearchIcon())
  }

  public func trackSearchFieldDidTap() {
    tracker.multiplatformTrack(event: InteractionEvent.OpenSearchBar())
  }

  // MARK: - OGAppViewTracking

  public func trackATTDidAppear() {
    tracker.multiplatformTrack(event: ViewEvent.OverlayATTSystemDialog())
  }

  // MARK: - OGAppUserPropertyTracking

  public func trackPushNotificationPermission() {
    Task {
      let authorizationStatus = await userNotificationCenter.authorizationStatus()
      let optInStatus = pushNotificationOptInStatus(from: authorizationStatus)
      let userProperty = TrackingUserProperty.PushNotificationOptInUserProperty(optIn: optInStatus)
      tracker.setUserProperty(userProperty)
    }
  }

  private func pushNotificationOptInStatus(from authorizationStatus: UNAuthorizationStatus) -> Bool {
    switch authorizationStatus {
    case .authorized, .ephemeral, .provisional: return true
    case .denied, .notDetermined: return false
    }
  }
}
