import AppCore
import AppTrackingTransparency
import Combine
import OGAirshipKit
import OGCore
import OGDomainStore
import OGNavigationBar
import OGNavigationCore
import OGRouter
import <PERSON>GScreenViewUpdate
import OGTracker
import OGTrackerCore
import OGTrackerOptInService
import OGWebView

public typealias AppTrackerStore = OGDomainStore<AppTrackerState, AppTrackerAction>

extension AppTrackerStore {
  public static func make() -> AppTrackerStore {
    AppTrackerStore(
      reducer: AppTrackerState.Reducer.reduce,
      middlewares: AppTrackerState.Middleware(),
      connector: AppTrackerState.Connector()
    )
  }
}

// MARK: - AppTrackerAction

public enum AppTrackerAction: OGDomainAction, Equatable {
  case _trackDidTapTab(OGTab)
  case _trackDidReloadTab(OGTab)
  case _trackATTDidAppear
  case _trackATTStatus(ATTrackingManager.AuthorizationStatus)
  case _trackDidGoBack
  case _trackIsLoggedIn(Bool)
  case _trackInboxButtonDidTap
  case _trackShareProductDidTap
  case _trackSearchButtonDidTap
  case _trackSearchFieldDidTap
}

// MARK: - AppTrackerState

public struct AppTrackerState: OGDomainState {
  public var isAwaitingUpdate: Bool

  public init(isAwaitingUpdate: Bool = true) {
    self.isAwaitingUpdate = isAwaitingUpdate
  }

  public static var initial: Self = .init()
}

extension AppTrackerState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _ state: inout AppTrackerState,
      with action: AppTrackerAction
    ) {}
  }

  public struct Middleware: OGDomainMiddleware {
    private let tracker: OGAppTracking

    init(tracker: OGAppTracking = OGAppTracker()) {
      self.tracker = tracker
    }

    public func callAsFunction(
      action: AppTrackerAction,
      for state: AppTrackerState
    ) async -> AppTrackerAction? {
      switch action {
      case let ._trackDidTapTab(tab):
        tracker.trackDidTap(tab: tab)
        return nil

      case let ._trackDidReloadTab(tab):
        tracker.trackDidReload(tab: tab)
        return nil

      case ._trackATTDidAppear:
        tracker.trackATTDidAppear()
        return nil

      case let ._trackATTStatus(status):
        tracker.trackATTStatus(status)
        return nil

      case ._trackDidGoBack:
        tracker.trackDidGoBack()
        return nil

      case let ._trackIsLoggedIn(loggedIn):
        tracker.trackIsLoggedIn(loggedIn)
        return nil

      case ._trackInboxButtonDidTap:
        tracker.trackInboxButtonDidTap()
        return nil

      case ._trackShareProductDidTap:
        tracker.trackShareProductDidTap()
        return nil

      case ._trackSearchButtonDidTap:
        tracker.trackSearchButtonDidTap()
        return nil

      case ._trackSearchFieldDidTap:
        tracker.trackSearchFieldDidTap()
        return nil
      }
    }
  }

  actor Connector: OGDomainConnector {
    private let tapTab: OGTabTapping
    private let backTap: OGBackTapping
    private let atTrackingStore: OGATTrackingStore
    private let appStore: any AppStoreProtocol
    private let inboxButtonTapPublisher: OGInboxButtonTapPublishing
    private let routePublisher: OGRoutePublishing
    private let trackerFeatureAdapter: OGTrackerFeatureAdaptable
    private let shareButtonTapPublisher: OGShareButtonTapPublishing

    private var cancellables = Set<AnyCancellable>()

    init(
      tapTab: OGTabTapping = OGNavigationCoreContainer.shared.tabTap(),
      backTap: OGBackTapping = OGNavigationCoreContainer.shared.backTap(),
      atTrackingStore: OGATTrackingStore = OGTrackerOptInContainer.shared.atTrackingStore(),
      appStore: any AppStoreProtocol = AppContainer.shared.appStore(),
      inboxButtonTapPublisher: OGInboxButtonTapPublishing = OGAirshipContainer.shared.inboxButtonTapPublisher(),
      routePublisher: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      trackerFeatureAdapter: OGTrackerFeatureAdaptable = OGTrackerFeatureAdapterContainer.shared.adapter(),
      shareButtonTapPublisher: OGShareButtonTapPublishing = OGWebViewContainer.shared.shareButtonTapPublisher()
    ) {
      self.tapTab = tapTab
      self.backTap = backTap
      self.atTrackingStore = atTrackingStore
      self.appStore = appStore
      self.inboxButtonTapPublisher = inboxButtonTapPublisher
      self.routePublisher = routePublisher
      self.trackerFeatureAdapter = trackerFeatureAdapter
      self.shareButtonTapPublisher = shareButtonTapPublisher
    }

    func configure(dispatch: @escaping (AppTrackerAction) async -> Void) async {
      await observeATTStore(dispatch)
      await observeAppStore(dispatch)
      observeTaps(dispatch)
    }

    private func observeATTStore(_ dispatch: @escaping (AppTrackerAction) async -> Void) async {
      await Publishers.CombineLatest(
        atTrackingStore.statePublisher
          .map(\.authorizationStatus)
          .filter { $0 != .notDetermined },
        atTrackingStore.statePublisher
          .map(\.didAppear)
          .filter { $0 }
      )
      .sink { authorizationStatus, _ in
        Task {
          await dispatch(._trackATTDidAppear)
          await dispatch(._trackATTStatus(authorizationStatus))
        }
      }
      .store(in: &cancellables)
    }

    private func observeAppStore(_ dispatch: @escaping (AppTrackerAction) async -> Void) async {
      await appStore
        .watch(keyPath: \.userState)
        .filter { $0.isAwaitingUpdate == false }
        .map(\.isLoggedIn)
        .removeDuplicates()
        .sink { isLoggedIn in
          Task {
            await dispatch(._trackIsLoggedIn(isLoggedIn))
          }
        }
        .store(in: &cancellables)
    }

    private func observeTaps(_ dispatch: @escaping (AppTrackerAction) async -> Void) {
      tapTab
        .didTapPublisher
        .filter { $0 != .none }
        .removeDuplicates()
        .sink { tab in
          Task {
            await dispatch(._trackDidTapTab(tab))
          }
        }
        .store(in: &cancellables)

      tapTab
        .didReloadTabPublisher
        .compactMap { $0 }
        .removeDuplicates()
        .sink { tab in
          Task {
            await dispatch(._trackDidReloadTab(tab))
          }
        }
        .store(in: &cancellables)

      backTap
        .didTapPublisher
        .sink {
          Task {
            await dispatch(._trackDidGoBack)
          }
        }
        .store(in: &cancellables)

      inboxButtonTapPublisher
        .didTapPublisher
        .sink {
          Task {
            await dispatch(._trackInboxButtonDidTap)
          }
        }
        .store(in: &cancellables)

      routePublisher
        .provider
        .filter { $0.url.absoluteString == OGIdentifier.sharing.value }
        .sink { _ in
          Task {
            await dispatch(._trackShareProductDidTap)
          }
        }
        .store(in: &cancellables)

      shareButtonTapPublisher
        .didTapPublisher
        .sink {
          Task {
            await dispatch(._trackShareProductDidTap)
          }
        }
        .store(in: &cancellables)

      routePublisher
        .provider
        .filter { $0.url.absoluteString == OGIdentifier.search.value }
        .map {
          let searchTapOrigin: OGSearchTapOrigin? = $0.getData()
          return searchTapOrigin
        }
        .compactMap { $0 }
        .sink { searchTapOrigin in
          Task {
            switch searchTapOrigin {
            case .button:
              await dispatch(._trackSearchButtonDidTap)
            case .field:
              await dispatch(._trackSearchFieldDidTap)
            }
          }
        }
        .store(in: &cancellables)
    }
  }
}
