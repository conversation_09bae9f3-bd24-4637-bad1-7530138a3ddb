import AppTracker
import AppTrackingTransparency
import Foundation
import OGMacros
import <PERSON>GMock
import OG<PERSON><PERSON><PERSON>

@OGMock
public class OGAppTrackerMock: OGAppTracking {
  public init() {}

  public func trackDidTap(tab: OGTab) {
    mock.trackDidTap(tab: tab)
  }

  public func trackATTDidAppear() {
    mock.trackATTDidAppear()
  }

  public func trackATTStatus(_ status: ATTrackingManager.AuthorizationStatus) {
    mock.trackATTStatus(status)
  }

  public func trackDidGoBack() {
    mock.trackDidGoBack()
  }

  public func trackIsLoggedIn(_ loggedIn: Bool) {
    mock.trackIsLoggedIn(loggedIn)
  }

  public func trackInboxButtonDidTap() {
    mock.trackInboxButtonDidTap()
  }

  public func trackDidReload(tab: OGNavigationCore.OGTab) {
    mock.trackDidReload(tab: tab)
  }

  public func trackShareProductDidTap() {
    mock.trackShareProductDidTap()
  }

  public func trackSearchButtonDidTap() {
    mock.trackSearchButtonDidTap()
  }

  public func trackSearchFieldDidTap() {
    mock.trackSearchFieldDidTap()
  }

  public func trackPushNotificationPermission() {
    mock.trackPushNotificationPermission()
  }
}
