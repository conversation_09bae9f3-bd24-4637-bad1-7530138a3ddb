<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AppStoreID</key>
	<string>id1142609246</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>de_DE</string>
	<key>CFBundleDisplayName</key>
	<string>$(DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>sheegoApp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.pushwoosh.scheme</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>pw-E475E-09FBA</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>whatsapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>NSCameraUsageDescription</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>NSLocationAlwaysAndWhenInUseUsageDescription</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>NSLocationAlwaysUsageDescription</string>
	<key>NSLocationUsageDescription</key>
	<string>NSLocationUsageDescription</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location is required to deliver a better user experience.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>NSPhotoLibraryAddUsageDescription</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>NSPhotoLibraryAddUsageDescription</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>sheego möchte Deine IDFA zu Trackingzwecken verwenden.</string>
	<key>Pushwoosh_APPID</key>
	<string>E475E-09FBA</string>
	<key>UIAppFonts</key>
	<array>
		<string>Assistant-Bold.ttf</string>
		<string>Assistant-Regular.ttf</string>
		<string>Assistant-SemiBold.ttf</string>
		<string>NotoColorEmoji.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string></string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIRequiresFullScreen~ipad</key>
	<false/>
	<key>UIStatusBarTintParameters</key>
	<dict>
		<key>UINavigationBar</key>
		<dict>
			<key>Style</key>
			<string>UIBarStyleDefault</string>
			<key>Translucent</key>
			<false/>
		</dict>
	</dict>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
