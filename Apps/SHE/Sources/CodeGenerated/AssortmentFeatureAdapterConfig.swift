// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import Assortment
public struct AssortmentFeatureConfiguration: AssortmentFeatureConfigurable {
	public var isEnabled: Bool = true
  public var secretIdentifier: String = ""
	public var assortmentEndpoint: String = "navigation/assortment" 
	public var displayBanners: Bool = true 
	public var staticEntriesBottom: [AssortmentNavigationEntry] = [] 
	public var staticEntriesTop: [AssortmentNavigationEntry] = []
	public var supportedUrls: [URL] = [URL(string:"app://assortment")!] 
  public var apiKey: String?
}

extension AssortmentFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		assortment.register {
			AssortmentFeatureAdapter(configuration: AssortmentFeatureConfiguration())
		}
	}
}

