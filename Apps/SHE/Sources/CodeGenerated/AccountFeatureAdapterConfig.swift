// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import Account
public struct AccountFeatureConfiguration: AccountFeatureConfigurable {
	public var isEnabled: Bool = true
	
	public var accountUrl: String = "mein-konto" 
	public var loginUrl: String = "mein-konto" 
	public var logoutUrl: String = "index.php?cl=account&fnc=logout&redirect=1" 
	public var secretIdentifier: String = "navigationApiKey"
	public var serviceEndpoint: String = "navigation/service"
	public var showAsLoggedIn: Bool = true
	public var staticEntriesBottom: [AccountNavigationEntry] = []
	public var staticEntriesTop: [AccountNavigationEntry] = []
	public var supportedUrls: [URL] = [
    URL(string: "app://openAccount")!,
    URL(string: "app://openAccount/service")!
  ]
  public var apiKey: String?
}

extension AccountFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		account.register {
			AccountFeatureAdapter(configuration: AccountFeatureConfiguration())
		}
	}
}
