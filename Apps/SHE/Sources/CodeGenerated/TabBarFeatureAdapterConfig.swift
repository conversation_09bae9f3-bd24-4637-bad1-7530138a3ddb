// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import OGDIService
import TabBar

public struct TabBarFeatureConfiguration: TabBarFeatureConfigurable {
	public var isEnabled: Bool = true
	
  public var hideSearchOnTabs: [String] = [
    "basket",
    "wishlist",
    "account"
  ]
  public var tabs: [TabItem] = []
}

extension TabBarContainer: AutoRegistering {
  public func autoRegister() {
    tabBar.register {
      TabBar(
        configuration: TabBarFeatureConfiguration()
      )
		}
	}
}

