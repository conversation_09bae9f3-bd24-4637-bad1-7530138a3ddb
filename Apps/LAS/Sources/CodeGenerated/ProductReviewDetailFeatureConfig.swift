// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import ProductReviewDetail

public struct ProductReviewDetailFeatureConfiguration: ProductReviewDetailFeatureConfigurable {
  public var components: String = ""
  public var isEnabled: Bool = true
}

extension ProductReviewDetailFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    productReviewDetail.register {
      ProductReviewDetailFeatureAdapter(configuration: ProductReviewDetailFeatureConfiguration())
    }
  }
}
