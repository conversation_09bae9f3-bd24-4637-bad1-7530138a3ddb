// swiftlint:disable all
// periphery:ignore:all
// Generated using OGAssetFetcher
import Foundation
import UICatalog
// swiftlint:disable superfluous_disable_command
// swiftlint:disable file_length
struct OGFontResolver: OGFontResolvable {
  public func font(_ key: OGFonts) -> OGFontStyle {
    switch key {
      case .headlineXL:
        return OGFontStyle(name: "Anth-Regular", size: 24, lineHeight: 26)
      case .headlineXXXL:
        return OGFontStyle(name: "Anth-Regular", size: 36, lineHeight: 40)
      case .headlineXXL:
        return OGFontStyle(name: "Anth-Regular", size: 32, lineHeight: 44)
      case .headlineLEmphasized:
        return OGFontStyle(name: "Anth-Regular", size: 20, lineHeight: 28)
      case .headlineLRegular:
        return OGFontStyle(name: "Anth-Regular", size: 20, lineHeight: 28)
      case .titleM:
        return OGFontStyle(name: "Poppins-SemiBold", size: 16, lineHeight: 20)
      case .titleS:
        return OGFontStyle(name: "Poppins-SemiBold", size: 14, lineHeight: 24)
      case .copyXL:
        return OGFontStyle(name: "Poppins-Light", size: 24, lineHeight: 26)
      case .copyL:
        return OGFontStyle(name: "Poppins-Regular", size: 16, lineHeight: 20)
      case .copyMRegular:
        return OGFontStyle(name: "Poppins-Regular", size: 14, lineHeight: 22)
      case .copyS:
        return OGFontStyle(name: "Poppins-Regular", size: 12, lineHeight: 20)
      case .buttonLabelM:
        return OGFontStyle(name: "Poppins-Regular", size: 16, lineHeight: 24)
      case .buttonLabelS:
        return OGFontStyle(name: "Poppins-Regular", size: 12, lineHeight: 20)
      case .label:
        return OGFontStyle(name: "Poppins-Medium", size: 12, lineHeight: 20)
      case .caption:
        return OGFontStyle(name: "Poppins-Regular", size: 11, lineHeight: 16)
      case .footnote:
        return OGFontStyle(name: "Poppins-Regular", size: 13, lineHeight: 16)
      case .tabnavigation:
        return OGFontStyle(name: "Poppins-Medium", size: 10, lineHeight: 16)
      case .section:
        return OGFontStyle(name: "Poppins-Medium", size: 14, lineHeight: 20)
      case .priceL:
        return OGFontStyle(name: "Poppins-SemiBold", size: 18, lineHeight: 26)
      case .priceMRegular:
        return OGFontStyle(name: "Poppins-Regular", size: 16, lineHeight: 20)
      case .priceMEmphasized:
        return OGFontStyle(name: "Poppins-SemiBold", size: 16, lineHeight: 20)
      case .priceSEmphasized:
        return OGFontStyle(name: "Poppins-SemiBold", size: 14, lineHeight: 22)
      case .priceSRegular:
        return OGFontStyle(name: "Poppins-Regular", size: 14, lineHeight: 22)
      case .copyMEmphasized:
        return OGFontStyle(name: "Poppins-SemiBold", size: 14, lineHeight: 22)
    }
  }
}
