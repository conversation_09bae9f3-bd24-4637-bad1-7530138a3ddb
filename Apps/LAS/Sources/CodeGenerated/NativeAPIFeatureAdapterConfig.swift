// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Foundation
import OGDIService
import OGFeatureCore
import NativeAPI
public struct NativeAPIFeatureConfiguration: NativeAPIFeatureConfigurable {
	public var isEnabled: Bool = true
	
	public var apiTenant: String = "lascana" 
	public var graphQLApiUrl: String = "https://stage-www-lascana-de-ottolasc.unbelievable-machine.net/graphql"
  public var restApiUrl: String = ""
	public var productIdRegex: String = ""
  public var dynamicYieldTrackPageViewUrl: String = ""
}

extension NativeAPIFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
		nativeAPI.register {
			NativeAPIFeatureAdapter(configuration: NativeAPIFeatureConfiguration())
		}
	}
}

