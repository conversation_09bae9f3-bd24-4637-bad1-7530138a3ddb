{"features": [{"custom": {"numberOfTapsToTrigger": 4}, "identifier": "airshipChannelIdDialog", "isEnabled": true}, {"custom": {"supportedUrls": ["app://settings"]}, "identifier": "settings", "isEnabled": true}, {"custom": {"accountUrl": "/kundenkonto", "loginUrl": "app://login", "logoutUrl": "/api/ase/checkout/public/logout", "secretIdentifier": "navigationApiKey", "serviceEndpoint": "ase/app/account/nav", "showAsLoggedIn": false, "staticEntriesBottom": [{"children": [{"l10n": "account.settings.title", "type": "link", "url": "app://settings"}, {"l10n": "countrySelection.title", "type": "link", "url": "app://pushTenantChooser"}, {"l10n": "account.rate.title", "type": "link", "url": "https://apps.apple.com/app/id6453157853?action=write-review"}, {"l10n": "navigation.recommendation.title", "type": "link", "url": "app://recommendation"}], "l10n": "account.settings.sectionTitle", "type": "section"}], "staticEntriesTop": [], "supportedUrls": ["app://openAccount", "app://openAccount/service", "app://account"]}, "identifier": "account", "isEnabled": true}, {"custom": {"collectorUrl": "", "namespace": "", "pushPath": ""}, "identifier": "snowplow", "isEnabled": false}, {"custom": {"eventIdMapping": {"purchase_completed": "cwhv3o"}, "uninstall": true, "urlPrefix": ""}, "identifier": "adjustTracking", "isEnabled": true}, {"custom": {"attributeIdMapping": {}, "eventIdMapping": {"basket_badge_count": true, "purchase_completed": true, "user_info": true, "wishlist_badge_count": true}, "keepContactAssociation": true}, "identifier": "airshipTracking", "isEnabled": true}, {"custom": {"cloudSite": "eu"}, "identifier": "airship", "isEnabled": true}, {"custom": {"domains": []}, "identifier": "allowedDomains", "isEnabled": true}, {"custom": {"actionText": null, "bodyText": null, "externalTargetUrl": "itms-apps://itunes.apple.com/app/id6453157853", "headlineText": null, "isAllowedToStart": true}, "identifier": "appUpdate", "isEnabled": false}, {"custom": {"assortmentEndpoint": "sell/app/navigation/tree", "displayBanners": true, "staticEntriesBottom": [], "staticEntriesTop": [], "supportedUrls": ["app://assortment"]}, "identifier": "assortment", "isEnabled": true}, {"custom": {"api": "", "web": ""}, "identifier": "baseUrl", "isEnabled": true}, {"custom": {"configEndpoint": null, "joinRecommendationWithInfoString": null, "measureUrl": null, "recommendationEndpoint": null, "secretIdentifier": "", "supportedUrls": ["app://openBraFittingGuide"]}, "identifier": "braFittingGuide", "isEnabled": false}, {"custom": {"catalogEndpoint": "", "catalogOrderUrl": "", "secretIdentifier": "", "supportedUrls": ["app://openCatalogScanner"]}, "identifier": "catalogScanner", "isEnabled": false}, {"custom": {"customTabUrls": [], "exitUrls": [], "showExternalUrlsInWebView": false, "successUrls": [], "supportedUrls": [], "webViewUrls": []}, "identifier": "checkout", "isEnabled": false}, {"custom": {"enableLogs": true}, "identifier": "crashReporting", "isEnabled": true}, {"custom": {"apiUrl": "https://man.aac.ninja/api", "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "supportedUrls": ["app://deals"]}, "identifier": "deals", "isEnabled": true}, {"custom": {"matchers": ["regex:.*\\.csv", "regex:.*\\.pdf", "regex:.*\\/pdf\\/", "regex:.*\\/control\\/secure\\/.*", "regex:.*\\/purchaseinfo\\/secure\\/.*"], "mimeType": ["application/pdf"]}, "identifier": "download", "isEnabled": true}, {"custom": {}, "identifier": "firebaseTracking", "isEnabled": true}, {"custom": {"supportedUrls": ["app://home"]}, "identifier": "home", "isEnabled": true}, {"custom": {"imageEndpoint": "https://man.aac.ninja/image-v2/scaled?scaleUp=false&bucketPower=2", "secretIdentifier": "appConfigToken"}, "identifier": "imageAPI", "isEnabled": true}, {"custom": {"supportedUrls": ["www.econda-monitor.de", "www.googletagmanager.com", "fls.doubleclick.", "adservice.google.", "dis.eu.criteo", "https://www.computop-paygate.com/", "https://r3.girogate.de/", "https://www.paypal.com/", "https://www.sandbox.paypal.com/"]}, "identifier": "inAppBrowser", "isEnabled": true}, {"custom": {"deleteMessagesAfterDays": 30, "deleteMessagesAfterTenantChange": true, "forceMessageWebView": false, "shouldShowThumbnails": false, "supportedUrls": ["app://openInbox", "app://inbox/overview"]}, "identifier": "inbox", "isEnabled": true}, {"custom": {"supportedUrls": ["/login", "app://login"], "webPath": "/login?target=/kundenkonto"}, "identifier": "login", "isEnabled": true}, {"custom": {"navigationTitle": {"enabledUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?manufactum\\.(de|at|ch|nl)(\\?.*|;.*|\\#.*|\\/|\\/api|\\/\\?.*|\\/;.*|\\/\\#.*)?$"]}}, "identifier": "loginButton", "isEnabled": false}, {"custom": {"appendQueryParameterToDeeplinks": null, "login": "app://login", "registration": "app://register", "showLoginButton": true, "showRegisterButton": false, "toShop": ""}, "identifier": "onboarding", "isEnabled": true}, {"custom": {"promoPattern": "voucher=", "webbridgeTrigger": true}, "identifier": "promoBanner", "isEnabled": true}, {"custom": {"defaultNotificationChannel": null, "defaultOptIn": true, "deliveryStatusItemEnabled": false, "openSystemSettingsFromAppSettings": false, "showAndroid13OptInDialog": true, "showOptInDialog": false, "showOptInDialogAfterCheckout": false}, "identifier": "push", "isEnabled": true}, {"custom": {"supportedUrls": ["app://showPushDialog"]}, "identifier": "pushDialog", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:app:\\/\\/pushPromotionLayer(\\?.*)?"]}, "identifier": "pushPromotion", "isEnabled": true}, {"custom": {"message": "Hey, die Manufactum App könnte dir gefallen. Lade die App jetzt im App Store oder bei Google Play herunter:", "supportedUrls": ["app://recommendation"], "url": "https://app.adjust.com/145oqzeq"}, "identifier": "recommendation", "isEnabled": true}, {"custom": {"supportedUrls": ["app://review"]}, "identifier": "review", "isEnabled": true}, {"custom": {"": ""}, "identifier": "screenTracking", "isEnabled": true}, {"custom": {"debounceMillis": 500, "maxHistoryItems": 5, "minCharacters": 2, "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "suggestionsApiUrl": "https://man.aac.ninja/api/search/suggestions?query=", "supportedUrls": ["app://search"], "webPath": "/suche/?q="}, "identifier": "search", "isEnabled": true}, {"custom": {"clearSessionCookieOnAppStart": false, "sessionTimeout": 0}, "identifier": "session", "isEnabled": true}, {"custom": {"regionLowerLeftLatitude": 45.68858, "regionLowerLeftLongitude": 5.91474, "regionUpperRightLatitude": 55.25106, "regionUpperRightLongitude": 17.1109, "storesEndpoint": "https://las.aac.ninja/v1/shops/", "supportedUrls": ["app://openShopFinder"]}, "identifier": "storeFinder", "isEnabled": false}, {"custom": {"hideSearchOnTabs": ["basket", "wishlist", "account"], "noBackstackInTabs": [], "tabRouting": [{"allowedIds": ["cart", "basket"], "urls": ["warenkorb.*"]}, {"allowedIds": ["wishlist"], "urls": ["merkliste.*"]}, {"allowedIds": ["profile", "account"], "urls": ["kundenkonto.*"]}, {"allowedIds": ["assortment"], "urls": ["regex:^app:\\/\\/assortment\\/.*"]}, {"allowedIds": ["shop"], "urls": ["logout.*"]}, {"allowedIds": ["shop", "cart", "basket", "wishlist", "assortment", "account", "basket", "profile"], "urls": ["regex:^https?:\\/\\/.*\\.manufactum\\.(de|at|ch|nl).*", "app://login"]}], "tabs": [{"activeImageName": "icon24x24TabBarActiveTab1", "hasLogo": true, "identifier": "shop", "inactiveImageName": "icon24x24TabBarInactiveTab1", "l10n": "navigation.shop.title", "url": ""}, {"activeImageName": "icon24x24TabBarActiveTab2", "identifier": "assortment", "inactiveImageName": "icon24x24TabBarInactiveTab2", "l10n": "navigation.assortment.title", "url": "app://assortment"}, {"activeImageName": "icon24x24TabBarActiveTab3", "identifier": "wishlist", "inactiveImageName": "icon24x24TabBarInactiveTab3", "l10n": "navigation.wishlist.title", "url": "merkliste"}, {"activeImageName": "icon24x24TabBarActiveTab4", "identifier": "basket", "inactiveImageName": "icon24x24TabBarInactiveTab4", "l10n": "navigation.cart.title", "url": "warenkorb"}, {"activeImageName": "icon24x24TabBarActiveTab5", "identifier": "account", "inactiveImageName": "icon24x24TabBarInactiveTab5", "l10n": "navigation.profile.title", "url": "app://openAccount"}]}, "identifier": "tabBar", "isEnabled": true}, {"custom": {"ignoreSettingsTrackingWebbridge": false, "onlyGlobalOptIn": false, "requestATT": false, "showPreConsent": false, "viewEventMapping": {"Basket": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)/(warenkorb|winkelmand)(\\?|#.*)?$"], "Checkout": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)/(bestelluebersicht|besteloverzicht|zahlung|betaling|lieferadresse|afleveradres|rechnungsadresse|factuuradres)(?!\\?from=/(kundenkonto|klantaccount))(\\?.*)*$"], "Home": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)/?(\\?.*)?$"], "Login": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)\\/login(\\?.*)*$"], "OrderConfirmation": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)\\/(bestellbestaetigung|orderbevestiging)(\\?.*)*$"], "ProductDetailPage": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)\\/[a-zA-Z0-9\\-]+-a[0-9]+\\/?(\\?.*)?$"], "ProductList": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)\\/[a-zA-Z0-9\\-]+-c[0-9]+\\/?(\\?.*)?$"], "ProductListOfSearchResult": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)\\/suche\\/\\?q=(.*)+$"], "Registration": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)\\/(register|registreren)(\\?.*)*$"], "Wishlist": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)/(merkliste|verlanglijst)(\\?|#.*)?$"]}}, "identifier": "tracking", "isEnabled": true}, {"custom": {"basket": "", "privacyPolicy": "", "profile": "", "wishList": ""}, "identifier": "urlMapping", "isEnabled": true}, {"custom": {"isAdjustEnabled": true, "isAdvertisingIdEnabled": true, "isAirshipEnabled": true}, "identifier": "userAgent", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)/(warenkorb|winkelmand)(\\?.*)?", "regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)/(bestelluebersicht|besteloverzicht|zahlung|betaling|lieferadresse|afleveradres|rechnungsadresse|factuuradres)(?!\\?from=/(kundenkonto|klantaccount))(\\?.*)*$"]}, "identifier": "copyCodeBanner", "isEnabled": true}, {"custom": {"salutationUrls": ["regex:^https:\\/\\/(www\\.)?manufactum(\\.de|\\.at|\\.ch|\\.de|\\.nl)\\/(\\?.*)?$"]}, "identifier": "salutation", "isEnabled": false}, {"custom": {"blockedUrls": [], "disableSwipeToRefresh": [], "disabledBackButtonUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?manufactum\\.(de|at|ch|nl)(\\?.*|;.*|\\#.*|\\/|\\/api|\\/\\?.*|\\/;.*|\\/\\#.*)?$"], "emitFakePageEventsForCSR": false, "enableLargeScreenOptimization": [], "forceLogo": false, "logoDisplayString": "<PERSON><PERSON><PERSON>", "maxWebViewSavedStateSize": 80000, "pdfUrls": ["regex:.*\\/control\\/secure\\/.*", "regex:.*\\/purchaseinfo\\/secure\\/.*", "regex:.*\\.pdf"], "redirectUrls": ["regex:^https:\\/\\/www\\.abnamro\\.nl.*$", "regex:^https:\\/\\/betalen\\.rabobank\\.nl.*$", "regex:^https:\\/\\/ideal\\.ing\\.nl.*$", "regex:^https:\\/\\/diensten\\.asnbank\\.nl.*$", "regex:^https:\\/\\/diensten\\.snsbank\\.nl.*$", "regex:^https:\\/\\/diensten\\.regiobank\\.nl.*$", "regex:^https:\\/\\/ideal\\.bunq\\.com.*$", "regex:^https:\\/\\/ideal\\.triodos\\.nl.*$", "merkliste", "warenkorb", "regex:^tel:.*", "regex:^mailto:.*"], "reloadOnAppearUrls": ["merkliste", "warenkorb"], "restrictWebViewSavedStateSize": true, "sharingType": "TOOLBAR", "supportedUrls": ["https.*"]}, "identifier": "web", "isEnabled": true}, {"custom": {"behaviors": [{"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=onboarding"}, "conditions": [{"start": 1, "type": "screenViews"}], "disabledUrls": ["onboarding", "login", "kundenkonto.*", "regex:^https:\\/\\/www\\.manufactum\\.(de|ch|at|nl)/?(\\?.*)?$"], "id": "openPushPromoAfterWelcomeScreen", "maxInvocations": 1, "precondition": "pushDisabled"}], "debounceMs": 500}, "identifier": "coordinator", "isEnabled": true}]}