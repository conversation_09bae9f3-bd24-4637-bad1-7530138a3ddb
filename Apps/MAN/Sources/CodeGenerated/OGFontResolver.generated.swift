// swiftlint:disable all
// periphery:ignore:all
// Generated using OGAssetFetcher
import Foundation
import UICatalog
// swiftlint:disable superfluous_disable_command
// swiftlint:disable file_length
struct OGFontResolver: OGFontResolvable {
  public func font(_ key: OGFonts) -> OGFontStyle {
    switch key {
      case .headlineXXXL:
        return OGFontStyle(name: "Rando-Medium", size: 36, lineHeight: 40)
      case .headlineLEmphasized:
        return OGFontStyle(name: "Rando-Medium", size: 18, lineHeight: 25)
      case .headlineXL:
        return OGFontStyle(name: "AtlasGrotesk-Bold", size: 24, lineHeight: 26)
      case .headlineXXL:
        return OGFontStyle(name: "Rando-Medium", size: 32, lineHeight: 44)
      case .headlineLRegular:
        return OGFontStyle(name: "AtlasGrotesk-Regular", size: 18, lineHeight: 26)
      case .titleM:
        return OGFontStyle(name: "AtlasGrotesk-Bold", size: 16, lineHeight: 20)
      case .titleS:
        return OGFontStyle(name: "AtlasGrotesk-Bold", size: 14, lineHeight: 22)
      case .copyXL:
        return OGFontStyle(name: "AtlasGrotesk-Regular", size: 24, lineHeight: 26)
      case .copyL:
        return OGFontStyle(name: "AtlasGrotesk-Regular", size: 16, lineHeight: 20)
      case .copyMRegular:
        return OGFontStyle(name: "AtlasGrotesk-Regular", size: 14, lineHeight: 22)
      case .copyS:
        return OGFontStyle(name: "AtlasGrotesk-Regular", size: 12, lineHeight: 20)
      case .section:
        return OGFontStyle(name: "AtlasGrotesk-Bold", size: 13, lineHeight: 18)
      case .buttonLabelS:
        return OGFontStyle(name: "AtlasGrotesk-Bold", size: 12, lineHeight: 20)
      case .buttonLabelM:
        return OGFontStyle(name: "AtlasGrotesk-Bold", size: 14, lineHeight: 24)
      case .caption:
        return OGFontStyle(name: "AtlasGrotesk-Regular", size: 11, lineHeight: 16)
      case .label:
        return OGFontStyle(name: "AtlasGrotesk-Bold", size: 12, lineHeight: 20)
      case .tabnavigation:
        return OGFontStyle(name: "AtlasGrotesk-Bold", size: 9, lineHeight: 16)
      case .footnote:
        return OGFontStyle(name: "AtlasGrotesk-Regular", size: 13, lineHeight: 16)
      case .priceMEmphasized, .copyMEmphasized, .priceL, .priceMRegular, .priceSEmphasized, .priceSRegular:
        return OGFontStyle(name: "AtlasGrotesk-Regular", size: 14, lineHeight: 22)
    }
  }
}
