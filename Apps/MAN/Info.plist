<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>de</string>
	<key>CFBundleDisplayName</key>
	<string>$(DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>manufactumApp</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>NSCameraUsageDescription</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Location is required to provide a better user experience. We do not use your location without your consent.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Location is required to provide a better user experience. We do not use your location without your consent.</string>
	<key>NSLocationUsageDescription</key>
	<string>Location is required to provide a better user experience. We do not use your location without your consent.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location is required to provide a better user experience. We do not use your location without your consent.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>NSPhotoLibraryAddUsageDescription</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>NSPhotoLibraryAddUsageDescription</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Für Werbung, die zu Ihren Interessen passt.</string>
	<key>UIAppFonts</key>
	<array>
		<string>AtlasGrotesk-Black.otf</string>
		<string>AtlasGrotesk-BlackItalic.otf</string>
		<string>AtlasGrotesk-Bold.otf</string>
		<string>AtlasGrotesk-BoldItalic.otf</string>
		<string>AtlasGrotesk-Light.otf</string>
		<string>AtlasGrotesk-LightItalic.otf</string>
		<string>AtlasGrotesk-Medium.otf</string>
		<string>AtlasGrotesk-MediumItalic.otf</string>
		<string>AtlasGrotesk-Regular.otf</string>
		<string>AtlasGrotesk-RegularItalic.otf</string>
		<string>AtlasGrotesk-Thin.otf</string>
		<string>AtlasGrotesk-ThinItalic.otf</string>
		<string>Rando-Medium.otf</string>
		<string>TradeGothicLTStd-Bold.otf</string>
		<string>TradeGothicLTStd.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string></string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIRequiresFullScreen~ipad</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UIStatusBarTintParameters</key>
	<dict>
		<key>UINavigationBar</key>
		<dict>
			<key>Style</key>
			<string>UIBarStyleDefault</string>
			<key>Translucent</key>
			<false/>
		</dict>
	</dict>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
