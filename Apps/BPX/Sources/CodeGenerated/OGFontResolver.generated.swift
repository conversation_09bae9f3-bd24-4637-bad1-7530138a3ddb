// swiftlint:disable all
// periphery:ignore:all
// Generated using OGAssetFetcher
import Foundation
import UICatalog
// swiftlint:disable superfluous_disable_command
// swiftlint:disable file_length
struct OGFontResolver: OGFontResolvable {
  public func font(_ key: OGFonts) -> OGFontStyle {
    switch key {
      case .headlineXXL:
        return OGFontStyle(name: "bonprix-Regular", size: 32, lineHeight: 44)
      case .headlineXL:
        return OGFontStyle(name: "bonprix-Regular", size: 24, lineHeight: 26)
      case .headlineLEmphasized:
        return OGFontStyle(name: "SourceSansPro-Bold", size: 18, lineHeight: 26)
      case .titleM:
        return OGFontStyle(name: "SourceSansPro-Bold", size: 16, lineHeight: 20)
      case .titleS:
        return OGFontStyle(name: "SourceSansPro-Bold", size: 14, lineHeight: 24)
      case .copyXL:
        return OGFontStyle(name: "SourceSansPro-Regular", size: 24, lineHeight: 26)
      case .copyL:
        return OGFontStyle(name: "SourceSansPro-Regular", size: 16, lineHeight: 20)
      case .copyMRegular:
        return OGFontStyle(name: "SourceSansPro-Regular", size: 14, lineHeight: 22)
      case .copyS:
        return OGFontStyle(name: "SourceSansPro-Regular", size: 12, lineHeight: 20)
      case .section:
        return OGFontStyle(name: "SourceSansPro-Bold", size: 13, lineHeight: 18)
      case .buttonLabelM:
        return OGFontStyle(name: "bonprix-Regular", size: 14, lineHeight: 24)
      case .buttonLabelS:
        return OGFontStyle(name: "bonprix-Regular", size: 12, lineHeight: 20)
      case .label:
        return OGFontStyle(name: "SourceSansPro-Bold", size: 12, lineHeight: 20)
      case .caption:
        return OGFontStyle(name: "SourceSansPro-Regular", size: 11, lineHeight: 16)
      case .footnote:
        return OGFontStyle(name: "SourceSansPro-Regular", size: 13, lineHeight: 16)
      case .tabnavigation:
        return OGFontStyle(name: "SourceSansPro-Bold", size: 11, lineHeight: 14)
      case .headlineLRegular:
        return OGFontStyle(name: "SourceSansPro-Regular", size: 18, lineHeight: 26)
      case .headlineXXXL:
        return OGFontStyle(name: "bonprix-Regular", size: 36, lineHeight: 40)
      case .priceMEmphasized, .copyMEmphasized, .priceL, .priceMRegular, .priceSEmphasized, .priceSRegular:
        return OGFontStyle(name: "bonprix-Regular", size: 14, lineHeight: 22)
    }
  }
}
