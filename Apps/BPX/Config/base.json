{"features": [{"custom": {"numberOfTapsToTrigger": 4}, "identifier": "airshipChannelIdDialog", "isEnabled": true}, {"custom": {"supportedUrls": ["app://settings"]}, "identifier": "settings", "isEnabled": true}, {"custom": {"secretIdentifier": "navigationApiKey", "serviceEndpoint": "navigation/service", "showAsLoggedIn": false, "staticEntriesBottom": [{"children": [{"l10n": "account.settings.title", "type": "link", "url": "app://settings"}, {"l10n": "countrySelection.title", "type": "link", "url": "app://pushTenantChooser"}, {"l10n": "account.rate.title", "type": "link", "url": "https://apps.apple.com/app/id1519437688?action=write-review"}, {"l10n": "navigation.recommendation.title", "type": "link", "url": "app://recommendation"}], "l10n": "account.settings.sectionTitle", "type": "section"}], "staticEntriesTop": [], "supportedUrls": ["app://openAccount", "app://openAccount/service", "app://account"]}, "identifier": "account", "isEnabled": true}, {"custom": {"collectorUrl": "", "namespace": "", "pushPath": ""}, "identifier": "snowplow", "isEnabled": false}, {"custom": {"eventIdMapping": {"purchase_completed": "h4jozr"}, "uninstall": true, "urlPrefix": "https://xmzy.adj.st/"}, "identifier": "adjustTracking", "isEnabled": true}, {"custom": {"attributeIdMapping": {}, "eventIdMapping": {"purchase_completed": "purchase_completed", "user_info": "user_info"}, "keepContactAssociation": false}, "identifier": "airshipTracking", "isEnabled": true}, {"custom": {"cloudSite": "eu"}, "identifier": "airship", "isEnabled": true}, {"custom": {"actionText": null, "bodyText": null, "externalTargetUrl": "itms-apps://itunes.apple.com/app/id1519437688", "headlineText": null, "isAllowedToStart": true}, "identifier": "appUpdate", "isEnabled": true}, {"custom": {"assortmentEndpoint": "navigation/assortment", "displayBanners": true, "secretIdentifier": "navigationApiKey", "staticEntriesTop": [], "supportedUrls": ["app://assortment"]}, "identifier": "assortment", "isEnabled": true}, {"custom": {"configEndpoint": null, "joinRecommendationWithInfoString": null, "measureUrl": null, "recommendationEndpoint": null, "secretIdentifier": "", "supportedUrls": ["app://openBraFittingGuide"]}, "identifier": "braFittingGuide", "isEnabled": false}, {"custom": {"catalogEndpoint": "", "catalogOrderUrl": "", "secretIdentifier": "", "supportedUrls": ["app://openCatalogScanner"]}, "identifier": "catalogScanner", "isEnabled": false}, {"custom": {"behaviors": [{"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=orderconfirmation"}, "conditions": [{"type": "webBridgeCall", "webBridgeCallName": "purchaseCompleted"}], "id": "openPushPromoAfterPurchaseCompletedWebCall", "precondition": "pushDisabled"}, {"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=screenviews"}, "conditions": [{"start": 20, "type": "screenViews"}], "id": "openPushPromoAfterXScreenViews", "maxInvocations": 1, "precondition": "pushDisabled"}, {"action": {"type": "navigation", "url": "app://review"}, "conditions": [{"period": 10, "start": 10, "type": "appStarts"}], "id": "openAppRating"}, {"action": {"type": "navigation", "url": "app://review"}, "conditions": [{"type": "webBridgeCall", "webBridgeCallName": "purchaseCompleted"}], "id": "openAppRatingAfterPurchaseCompletedWebCall"}]}, "identifier": "coordinator", "isEnabled": true}, {"custom": {"enableLogs": true}, "identifier": "crashReporting", "isEnabled": true}, {"custom": {"apiUrl": "https://bpx.aac.ninja/api", "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "supportedUrls": ["app://deals"]}, "identifier": "deals", "isEnabled": true}, {"custom": {"supportedUrls": ["https://apps.apple.com/app/id1519437688?action=write-review", "itms-apps://itunes.apple.com/app/id1519437688", "regex:^bonprixxapp.*", "checkoutshopper-live.adyen.com", "pay.playground.klarna.com", "pay.klarna.com", "klarna.com"]}, "enabled": true, "identifier": "externalBrowser"}, {"custom": {}, "identifier": "firebaseTracking", "isEnabled": true}, {"custom": {"imageEndpoint": "https://bpx.aac.ninja/image-v2/scaled?scaleUp=false&bucketPower=2", "secretIdentifier": ""}, "identifier": "imageAPI", "isEnabled": true}, {"custom": {"supportedUrls": ["www.facebook.com", "https://www.instagram.com/bonprix", "https://www.youtube.com/bonprix", "https://www.pinterest.de/bonprix"]}, "enabled": true, "identifier": "inAppBrowser"}, {"custom": {"deleteMessagesAfterDays": 30, "deleteMessagesAfterTenantChange": true, "forceMessageWebView": false, "shouldShowThumbnails": false, "supportedUrls": ["app://openInbox", "app://inbox/overview"]}, "identifier": "inbox", "isEnabled": true}, {"custom": {"navigationTitle": {"enabledUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(es|fi)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"]}}, "identifier": "loginButton", "isEnabled": true}, {"custom": {"showLoginButton": true, "showRegisterButton": true, "toShop": ""}, "identifier": "onboarding", "isEnabled": true}, {"custom": {"promoPattern": "?cvp=", "webbridgeTrigger": true}, "identifier": "promoBanner", "isEnabled": true}, {"custom": {"defaultNotificationChannel": null, "defaultOptIn": true, "deliveryStatusItemEnabled": false, "openSystemSettingsFromAppSettings": false, "showAndroid13OptInDialog": true, "showOptInDialog": false, "showOptInDialogAfterCheckout": false}, "identifier": "push", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:app:\\/\\/pushPromotionLayer(\\?.*)?"]}, "identifier": "pushPromotion", "isEnabled": true}, {"custom": {"message": "", "supportedUrls": ["app://openRecommendation", "app://recommendation"], "url": "https://app.adjust.com/qnmxbf6"}, "identifier": "recommendation", "isEnabled": true}, {"custom": {"supportedUrls": ["/register", "app://register"]}, "identifier": "register", "isEnabled": true}, {"custom": {"supportedUrls": ["app://review", "app://openRating"]}, "identifier": "review", "isEnabled": true}, {"custom": {"debounceMillis": 500, "maxHistoryItems": 5, "minCharacters": 3, "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "shortcuts": [], "suggestionsApiUrl": "https://bpx.aac.ninja/api/search/suggestions?query=", "supportedUrls": ["app://search"], "webPath": "/mobile-api/search/?term="}, "identifier": "search", "isEnabled": true}, {"custom": {"clearSessionCookieOnAppStart": false, "sessionTimeout": 0}, "identifier": "session", "isEnabled": true}, {"custom": {"regionLowerLeftLatitude": 45.68858, "regionLowerLeftLongitude": 5.91474, "regionUpperRightLatitude": 55.25106, "regionUpperRightLongitude": 17.1109, "storesEndpoint": "https://las.aac.ninja/v1/shops/", "supportedUrls": ["app://openShopFinder"]}, "identifier": "storeFinder", "isEnabled": false}, {"custom": {"hideSearchOnTabs": ["basket", "wishlist", "profile"], "noBackstackInTabs": [], "tabRouting": [{"allowedIds": ["shop"], "urls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(es|fi)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "regex:^.*logout\\?redirect"]}, {"allowedIds": ["basket"], "urls": ["cart.*", "cesta.*", "osto<PERSON>ri.*", "checkout.*", "/checkout/", "/cesta/checkout", "/ostoskori/checkout"]}, {"allowedIds": ["wishlist"], "urls": ["wishlist.*", "favoritos.*", "to<PERSON><PERSON><PERSON>.*"]}, {"allowedIds": ["assortment"], "urls": ["regex:^app://assortment/.*"]}, {"allowedIds": ["account"], "urls": ["myaccount.*", "mi-cuenta.*", "tilini.*"]}, {"allowedIds": ["shop", "basket", "wishlist", "assortment", "account"], "urls": ["regex:^https?://.*\\.bonprix\\.(es|fi).*", "app://login", "app://register"]}]}, "identifier": "tabBar", "isEnabled": true}, {"custom": {"ignoreSettingsTrackingWebbridge": false, "requestATT": false, "viewEventMapping": {"Basket": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/(cesta|ostoskori|cart)\\/?$"], "Checkout": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/(.*checkout(\\/flow\\/start)?)\\/?$"], "Home": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)(\\/|.*logout\\?redirect=)\\/?$"], "Login": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/.*\\/login\\?redirect=.*$"], "OrderConfirmation": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/checkout\\/(confirm|thank[yY]ou[pP]age|jarjestys)\\/?(\\?.*)?$"], "ProductDetailPage": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/(producto|tyyli).*$"], "ProductList": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/(categoria|kategoria).*$"], "ProductListOfSearchResult": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/?(.*search\\/\\?term=.*)?$"], "Registration": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/(.*register(\\?redirect=(\\%2f)?)?)\\/?$"], "Wishlist": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/(favoritos|toivelista|wishlist)\\/?$"]}}, "identifier": "tracking", "isEnabled": true}, {"custom": {"isAdjustEnabled": true, "isAdvertisingIdEnabled": true, "isAirshipEnabled": true}, "identifier": "userAgent", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/(favoritos|toivelista|wishlist)\\/?$", "regex:^https:\\/\\/www\\.bonprix.(es|fi)\\/(.*checkout(\\/flow\\/start)?)\\/?$"]}, "identifier": "copyCodeBanner", "isEnabled": true}, {"custom": {"salutationUrls": ["regex:^https:\\/\\/(www\\.)?bonprix(\\.es|\\.fi)\\/(\\?.*)?$"]}, "identifier": "salutation", "isEnabled": true}, {"custom": {"disableSwipeToRefresh": [], "disabledBackButtonUrls": ["cesta/remove", "ostoskori/remove", "regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(es|fi)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"], "emitFakePageEventsForCSR": false, "enableLargeScreenOptimization": [], "enabledSearchUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(es|fi)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "regex:^.*logout\\?redirect"], "forceLogo": false, "logoDisplayString": "<PERSON><PERSON><PERSON>", "maxWebViewSavedStateSize": 80000, "redirectUrls": ["/categoria/", "/kategoria/", "/producto/", "/tyyli/", "regex:^bonprixxapp.*", "pay.playground.klarna.com", "pay.klarna.com"], "reloadOnAppearUrls": ["favoritos/", "cesta/", "to<PERSON>lista/", "ostoskori/"], "restrictWebViewSavedStateSize": true, "sharingType": "TOOLBAR", "supportedUrls": ["^https.*", "^file.*"]}, "identifier": "web", "isEnabled": true}]}