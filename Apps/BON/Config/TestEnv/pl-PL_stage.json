{"features": [{"custom": {"supportedUrls": ["/my-account", "/myaccount", "app://openAccount", "app://openAccount/service"]}, "identifier": "account"}, {"custom": {"domains": ["bonprixplnew.media4u.pl"]}, "identifier": "allowedDomains"}, {"custom": {"api": "https://bonprixplnew.media4u.pl/mobile-api/", "web": "https://bonprixplnew.media4u.pl/"}, "identifier": "baseUrl"}, {"custom": {"host": "bonprixplnew.media4u.pl", "password": "sF2hG6jd8e", "realm": "PASSWORD", "user": "clientuser"}, "identifier": "basicAuth", "isEnabled": true}, {"custom": {"toShop": "https://bonprixplnew.media4u.pl/"}, "identifier": "onboarding"}, {"custom": {}, "identifier": "pushPromotion"}, {"custom": {"url": "https://app.adjust.com/hy3l6r_30m3rr"}, "identifier": "recommendation"}, {"custom": {"apiUrl": "https://bon-master.aac.ninja/api/", "secretIdentifier": "backendApiKeyBeta", "supportedUrls": ["app://deals"]}, "identifier": "deals"}, {"custom": {"apiUrl": "https://bon-master.aac.ninja/api/", "secretIdentifier": "backendApiKeyBeta", "supportedUrls": ["app://deals"]}, "identifier": "deals"}]}