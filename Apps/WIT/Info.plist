<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>de</string>
	<key>CFBundleDisplayName</key>
	<string>$(DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wittApp</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>NSCameraUsageDescription</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Location is required to provide a better user experience. We do not use your location without your consent.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Location is required to provide a better user experience. We do not use your location without your consent.</string>
	<key>NSLocationUsageDescription</key>
	<string>Location is required to provide a better user experience. We do not use your location without your consent.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location is required to provide a better user experience. We do not use your location without your consent.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>NSPhotoLibraryAddUsageDescription</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Für Werbung, die zu Ihren Interessen passt.</string>
	<key>UIAppFonts</key>
	<array>
		<string>OpenSans-Bold.ttf</string>
		<string>OpenSans-BoldItalic.ttf</string>
		<string>OpenSans-ExtraBold.ttf</string>
		<string>OpenSans-ExtraBoldItalic.ttf</string>
		<string>OpenSans-Italic.ttf</string>
		<string>OpenSans-Light.ttf</string>
		<string>OpenSans-LightItalic.ttf</string>
		<string>OpenSans-Regular.ttf</string>
		<string>OpenSans-Semibold.ttf</string>
		<string>OpenSans-SemiboldItalic.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string></string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIRequiresFullScreen~ipad</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UIStatusBarTintParameters</key>
	<dict>
		<key>UINavigationBar</key>
		<dict>
			<key>Style</key>
			<string>UIBarStyleDefault</string>
			<key>Translucent</key>
			<false/>
		</dict>
	</dict>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>